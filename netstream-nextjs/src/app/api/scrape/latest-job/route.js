import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/mongodb';

export async function GET() {
  try {
    const db = await connectToDatabase();
    
    // Get the latest scraping job
    const latestJob = await db.collection('scraping_jobs')
      .findOne({}, { sort: { startedAt: -1 } });

    if (!latestJob) {
      return NextResponse.json({
        success: false,
        error: 'No scraping jobs found'
      }, { status: 404 });
    }

    // Calculate job duration if completed
    let duration = null;
    if (latestJob.completedAt && latestJob.startedAt) {
      duration = Math.round((latestJob.completedAt - latestJob.startedAt) / 1000); // seconds
    } else if (latestJob.startedAt && latestJob.status === 'running') {
      duration = Math.round((Date.now() - latestJob.startedAt) / 1000); // seconds
    }

    const jobData = {
      jobId: latestJob.jobId,
      status: latestJob.status,
      mode: latestJob.mode,
      type: latestJob.type,
      pages: latestJob.pages,
      enrichment: latestJob.enrichment,
      gemini: latestJob.gemini,
      startedAt: latestJob.startedAt,
      completedAt: latestJob.completedAt,
      failedAt: latestJob.failedAt,
      stoppedAt: latestJob.stoppedAt,
      stoppedBy: latestJob.stoppedBy,
      error: latestJob.error,
      output: latestJob.output,
      duration: duration ? `${Math.floor(duration / 60)}m ${duration % 60}s` : null,
      estimatedDuration: latestJob.estimatedDuration
    };

    return NextResponse.json({
      success: true,
      data: jobData
    });

  } catch (error) {
    console.error('Latest job fetch error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to fetch latest job: ${error.message}`
    }, { status: 500 });
  }
} 