'use client'
import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAdminContext } from '@/context/AdminContext'

export default function Sidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const pathname = usePathname()
  const { isAdmin, showLoginModal, showAdminPanel } = useAdminContext()

  const menuItems = [
    { href: '/', icon: 'fas fa-home', label: 'Home' },
    { href: '/movies', icon: 'fas fa-film', label: 'Movies' },
    { href: '/series', icon: 'fas fa-tv', label: 'Series' },
    { href: '/anime', icon: 'fas fa-dragon', label: 'Anime' },
    { href: '/livetv', icon: 'fas fa-broadcast-tower', label: 'Live TV' }
  ]

  const isActive = (href) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <div className={`bg-gray-800 transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } flex flex-col`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <h1 className="text-xl font-bold text-white">NetStream</h1>
          )}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 rounded-lg hover:bg-gray-700 text-gray-400 hover:text-white transition-colors"
          >
            <i className={`fas ${isCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'}`}></i>
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => (
            <li key={item.href}>
              <Link
                href={item.href}
                className={`flex items-center p-3 rounded-lg transition-colors ${
                  isActive(item.href)
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <i className={`${item.icon} ${isCollapsed ? 'text-center w-full' : 'mr-3'}`}></i>
                {!isCollapsed && <span>{item.label}</span>}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* Admin Section */}
      <div className="p-4 border-t border-gray-700">
        {isAdmin ? (
          <button
            onClick={showAdminPanel}
            className={`flex items-center w-full p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors ${
              isCollapsed ? 'justify-center' : ''
            }`}
          >
            <i className={`fas fa-cog ${isCollapsed ? '' : 'mr-3'}`}></i>
            {!isCollapsed && <span>Admin Panel</span>}
          </button>
        ) : (
          <button
            onClick={showLoginModal}
            className={`flex items-center w-full p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors ${
              isCollapsed ? 'justify-center' : ''
            }`}
          >
            <i className={`fas fa-sign-in-alt ${isCollapsed ? '' : 'mr-3'}`}></i>
            {!isCollapsed && <span>Admin Login</span>}
          </button>
        )}
      </div>
    </div>
  )
}
