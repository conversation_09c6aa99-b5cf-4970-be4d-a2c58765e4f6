# Enrichment Process Documentation

This document describes the enrichment process for NetStream, which adds metadata from TMDB and Jikan to movies, series, and anime.

## Overview

The enrichment process is responsible for:

1. Cleaning titles to improve matching
2. Searching for matches in TMDB and Jikan
3. Fetching detailed information for matches
4. Fetching seasons data for series and anime
5. Formatting and storing the data in the database

## Configuration

The enrichment process can be configured using environment variables or the configuration file at `src/enrichment/config/enrichmentConfig.js`.

### Environment Variables

- `USE_ADVANCED_ENRICHMENT`: Whether to enable advanced enrichment with Gemini AI. Set to `true` to enable, `false` to disable. Default: `false`
- `GEMINI_API_KEY`: The API key for Gemini AI. Required for advanced enrichment.
- `FETCH_SEASONS`: Whether to fetch seasons data. Can be `true` or `false`. Default: `true`

When `USE_ADVANCED_ENRICHMENT` is set to `true`, the following settings are automatically enabled:
- `USE_GEMINI`: Set to `true` to use Gemini AI for title optimization
- `USE_ADVANCED_MATCHING`: Set to `true` to use advanced matching with Gemini AI

### Enrichment Modes

- **BASIC**: No Gemini AI, but fetch seasons data
- **ADVANCED**: Use Gemini AI and fetch seasons data
- **MINIMAL**: No Gemini AI, no seasons data

## Usage

### Configuration in .env File

The simplest way to configure enrichment is through your `.env` file:

```
# Enable advanced enrichment with Gemini AI
GEMINI_API_KEY=your_api_key_here
USE_ADVANCED_ENRICHMENT=true
```

With this configuration, all enrichment operations will use Gemini AI for advanced matching.

### Basic Enrichment

Basic enrichment is used when `USE_ADVANCED_ENRICHMENT` is set to `false` or not set. It will fetch TMDB and Jikan data, including seasons data, but will not use Gemini AI for advanced matching.

```javascript
const { enrichItem } = require('../src/enrichment/services/enrichService');

// Enrich a movie
const enrichedMovie = await enrichItem(movie, 'movie');

// Enrich a series
const enrichedSeries = await enrichItem(series, 'series');

// Enrich an anime
const enrichedAnime = await enrichItem(anime, 'anime');
```

### Advanced Enrichment

Advanced enrichment is used when `USE_ADVANCED_ENRICHMENT` is set to `true` in your `.env` file. You can also explicitly request advanced enrichment in your code:

```javascript
const { enrichItemWithOptions } = require('../src/enrichment/services/enrichService');

// Enrich a movie with advanced matching
const enrichedMovie = await enrichItemWithOptions(movie, 'movie', {
  useAdvanced: true
});

// Enrich a series with advanced matching
const enrichedSeries = await enrichItemWithOptions(series, 'series', {
  useAdvanced: true
});

// Enrich an anime with advanced matching
const enrichedAnime = await enrichItemWithOptions(anime, 'anime', {
  useAdvanced: true
});
```

### Custom Options

You can also customize the enrichment process with specific options:

```javascript
const { enrichItemWithOptions } = require('../src/enrichment/services/enrichService');

// Enrich a movie with custom options
const enrichedMovie = await enrichItemWithOptions(movie, 'movie', {
  useAdvanced: true,
  fetchSeasons: false
});
```

## Testing

You can test the enrichment process using the `test_enrichment.js` script:

```bash
# Test basic enrichment for a series
node scripts/test_enrichment.js --type=series --title="Stranger Things"

# Test advanced enrichment for a movie
node scripts/test_enrichment.js --advanced --type=movie --title="Inception"

# Test basic enrichment for an anime without seasons
node scripts/test_enrichment.js --type=anime --title="Attack on Titan" --no-seasons
```

## Updating Existing Items

You can update the enrichment for existing items in the database using the `update_enrichment.js` script:

```bash
# Update all items with basic enrichment (limit 10 per type)
node scripts/update_enrichment.js

# Update all series with advanced enrichment (limit 10)
node scripts/update_enrichment.js --advanced --type=series

# Update a specific item by ID
node scripts/update_enrichment.js --id=<id>

# Update all items with basic enrichment (limit 100 per type)
node scripts/update_enrichment.js --limit=100
```

## Implementation Details

### TMDB Seasons Fetching

The TMDB service fetches all seasons for a series and formats them for database storage. This includes:

- Season metadata (air date, name, overview, etc.)
- Episode metadata (air date, name, overview, etc.)

### Jikan Seasons Fetching

The Jikan service fetches all seasons for an anime, including prequels and sequels, and formats them for database storage. This includes:

- Season metadata (air date, name, episodes, etc.)
- Related anime information

### Advanced Matching with Gemini AI

The advanced enrichment process uses Gemini AI to:

1. Generate optimal search keywords for better matching
2. Verify matches using string similarity and AI-based verification

This helps to handle title variations, translations, and misspellings that might cause issues with the basic matching process.

## Troubleshooting

### Missing Seasons Data

If seasons data is missing, check:

1. The `FETCH_SEASONS` environment variable is not set to `false`
2. The TMDB or Jikan API is not rate-limited
3. The series or anime has seasons in the TMDB or Jikan database

### How to Switch Between Modes

To switch between modes, simply update your `.env` file:

**For Basic Mode:**
```
# Disable advanced enrichment
USE_ADVANCED_ENRICHMENT=false
```

**For Advanced Mode:**
```
# Enable advanced enrichment
USE_ADVANCED_ENRICHMENT=true
GEMINI_API_KEY=your_api_key
```

Then run the server as usual:
```bash
node server.js
```

No additional command-line flags are needed.

### Gemini AI Issues

If advanced matching is not working, check:

1. The `GEMINI_API_KEY` environment variable is set correctly in your `.env` file
2. The `USE_ADVANCED_ENRICHMENT` environment variable is set to `true` in your `.env` file
3. The Gemini API is not rate-limited or experiencing issues
4. Check the server logs for any Gemini API initialization errors

### Database Issues

If the enriched data is not being saved to the database, check:

1. The MongoDB connection is working
2. The database models have the necessary fields for storing seasons data
3. The `saveToDB` function is being called with the enriched data
