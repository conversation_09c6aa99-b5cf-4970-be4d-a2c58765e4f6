// File: src/enrichment/services/advancedEnrichService.js
const { GoogleGenerativeAI } = require("@google/generative-ai");
const stringSimilarity = require("string-similarity");
const {
  enrichWithTmdb,
  fetchAllTmdbSeasons,
  formatTmdbSeasonsForDB
} = require('../tmdb/tmdbService');
const jikanService = require('../jikan/jikanService');
const logger = require('../../utils/logger');
const { getConfig } = require('../config/enrichmentConfig');
const { translateToEnglish } = require('../../utils/translationUtils');
const { cache } = require('../../utils/unifiedCache');
const { rateLimiters } = require('../../utils/intelligentRateLimiter');

// Import the basic enrichment functions directly to avoid circular dependency
const pLimit = require('p-limit');
const { cleanTitleFallback } = require('../regex/regexUtils');

// Get configuration options
const config = getConfig();

// Check if caching is enabled in the configuration
const isCachingEnabled = () => config.ENABLE_CACHING !== false;

// Configuration
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_MODEL_NAME = "gemini-2.0-flash-lite"; // Recommended model
const GEMINI_MIN_INTERVAL_MS = config.GEMINI_RATE_LIMIT_MS || 2000; // Default: 30 req/min = 2000ms
const TITLE_SIMILARITY_THRESHOLD = config.TITLE_SIMILARITY_THRESHOLD;

// Initialize Gemini AI
let genAI;
let geminiModel;
let lastGeminiCallTime = 0;

try {
  if (!GEMINI_API_KEY) throw new Error("GEMINI_API_KEY missing");
  genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
  geminiModel = genAI.getGenerativeModel({ model: GEMINI_MODEL_NAME });
  logger.info(`Gemini AI Initialized: ${GEMINI_MODEL_NAME}.`);
} catch (error) {
  logger.error(`Gemini AI Init: ${error.message}`);
}

// Utility function to sleep
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Flag to track if Gemini quota has been exceeded
let geminiQuotaExceeded = false;

// Function to check if an error is a Gemini API quota exceeded error
function isGeminiQuotaError(error) {
  // Check for various quota error patterns
  const errorMessage = error.message ? error.message.toLowerCase() : '';
  const quotaPatterns = [
    'quota exceeded',
    'resource exhausted',
    'rate limit',
    'too many requests',
    'usage limit',
    'daily limit',
    'request limit'
  ];

  // Check if error status is 429 (Too Many Requests) or 403 (Forbidden) which are common for quota issues
  const quotaStatus = error.status === 429 || error.status === 403;

  // Check if error message contains any quota-related patterns
  const hasQuotaMessage = quotaPatterns.some(pattern => errorMessage.includes(pattern));

  return quotaStatus || hasQuotaMessage;
}

// Function to handle Gemini quota exceeded
function handleGeminiQuotaExceeded() {
  if (!geminiQuotaExceeded) {
    geminiQuotaExceeded = true;
    logger.error('GEMINI API QUOTA EXCEEDED. Advanced enrichment will be disabled.');
  }
}

/**
 * Get optimized search query from Gemini AI
 * @param {string} baseTitle - The base title to optimize
 * @param {number} yearHint - Optional year hint
 * @param {string} mediaType - The media type ('movie', 'series', or 'anime')
 * @returns {Promise<string|null>} - The optimized search query or null if failed
 */
async function getGeminiSearchQuery(baseTitle, yearHint, mediaType) {
  if (!baseTitle) return null;
  if (geminiQuotaExceeded) return baseTitle; // Use original title if quota exceeded

  // Create a cache key from the parameters
  const cacheKey = `query_${baseTitle}_${yearHint || 'noYear'}_${mediaType}`;

  // Check if caching is enabled and we have a cached result
  if (isCachingEnabled()) {
    const cachedResult = cache.get('gemini', cacheKey);
    if (cachedResult) {
      logger.info(`Using cached Gemini search query for '${baseTitle}': '${cachedResult}'`);
      return cachedResult;
    }
  }

  let prompt = `Given the following basic information for a ${mediaType}:`;
  prompt += `\nOriginal Title: "${baseTitle}"`;
  if (yearHint) {
    prompt += `\nApproximate Release Year: ${yearHint}`;
  }
  prompt += `\nGenerate the most effective and concise title or keyword(s) to use as a search query for finding the correct entry on `;
  prompt +=
    mediaType === "anime"
      ? `the Jikan API (MyAnimeList).`
      : `The Movie Database (TMDB).`;
  prompt += `\nReturn *only* the generated title/keyword(s), without the release year, and without any explanation, introduction, or quotation marks around the result. Focus on the official title in the most common language (often English or Japanese for anime, English for movies/series, but if french keep it in french).`;

  logger.debug(`Sending prompt to Gemini for search keywords: ${prompt.replace(/\n/g, " ")}`);

  try {
    const result = await rateLimiters.gemini.execute(async () => {
      return await geminiModel.generateContent(prompt);
    });
    const response = await result.response;

    if (!response?.candidates?.[0]?.content?.parts?.[0]?.text) {
      logger.warn(`Gemini returned no valid content for title '${baseTitle}'. Block Reason: ${response?.promptFeedback?.blockReason || "N/A"}.`);
      return null;
    }

    const suggestion = response.candidates[0].content.parts[0].text.trim();
    const cleanedSuggestion = suggestion.replace(/^["']|["']$/g, "").trim();
    logger.debug(`Gemini suggested search keywords: "${cleanedSuggestion}"`);

    if (!cleanedSuggestion) {
      logger.warn(`Gemini returned empty search keywords after cleanup for title '${baseTitle}'. Raw: "${suggestion}"`);
      return null;
    }

    // Cache the result if caching is enabled
    if (isCachingEnabled()) {
      cache.set('gemini', cacheKey, cleanedSuggestion);
    }

    return cleanedSuggestion;
  } catch (error) {
    // Check for quota exceeded errors
    if (isGeminiQuotaError(error)) {
      handleGeminiQuotaExceeded();
      return baseTitle; // Use original title if quota exceeded
    }

    logger.error(`Gemini API call failed generating query for '${baseTitle}'. Status: ${error.status || "N/A"}, Message: ${error.message}`);
    return null;
  }
}

/**
 * Ask Gemini for final verification of a match
 * @param {string} originalTitle - The original title
 * @param {string} mediaType - The media type
 * @param {number} candidateId - The candidate ID
 * @param {string} candidateTitle - The candidate title
 * @returns {Promise<boolean>} - Whether the match is valid
 */
async function askGeminiForFinalVerdict(originalTitle, mediaType, candidateId, candidateTitle) {
  if (!originalTitle || !mediaType || !candidateId || !candidateTitle) {
    logger.warn("Gemini Verdict Check: Missing required information.");
    return false;
  }

  if (geminiQuotaExceeded) return false; // Skip if quota exceeded

  // Create a cache key from the parameters
  const cacheKey = `verdict_${originalTitle}_${mediaType}_${candidateId}_${candidateTitle}`;

  // Use unified cache system
  if (isCachingEnabled()) {
    const cachedResult = cache.get('gemini', cacheKey);
    if (cachedResult !== null) {
      logger.info(`Using cached Gemini verdict for '${originalTitle}' vs '${candidateTitle}': ${cachedResult ? 'YES' : 'NO'}`);
      return cachedResult;
    }
  }

  const now = Date.now();
  const elapsed = now - lastGeminiCallTime;
  if (lastGeminiCallTime > 0 && elapsed < GEMINI_MIN_INTERVAL_MS) {
    const wait = GEMINI_MIN_INTERVAL_MS - elapsed;
    logger.info(`API rate limit: Waiting ${wait}ms before Gemini verdict call...`);
    await sleep(wait);
  }

  const prompt = `Does the title "${originalTitle}" likely refer to the same ${mediaType} as the ${mediaType === "anime" ? "MyAnimeList" : "TMDB"} entry with ID ${candidateId} which has a title of "${candidateTitle}"? Consider potential translations or variations. Respond with ONLY 'YES' or 'NO'.`;

  logger.info(`Asking Gemini for final verdict: ${prompt}`);

  try {
    // Use the rate limiter for consistent API access
    lastGeminiCallTime = Date.now();
    const result = await rateLimiters.gemini.execute(async () => {
      return await geminiModel.generateContent(prompt);
    });
    const response = await result.response;
    const text = response.text().trim().toUpperCase();
    logger.info(`Gemini Verdict Response: "${text}"`);

    const isMatch = text === "YES";

    // Cache the result if caching is enabled
    if (isCachingEnabled()) {
      cache.set('gemini', cacheKey, isMatch);
    }

    return isMatch;
  } catch (error) {
    // Check for quota exceeded errors
    if (isGeminiQuotaError(error)) {
      handleGeminiQuotaExceeded();
      return false;
    }

    logger.error(`Gemini API call failed during final verdict check. Error: ${error.message}`);
    return false;
  }
}

// Create a limit for concurrent API calls
const limit = pLimit(config.MAX_CONCURRENT_ENRICHMENTS || 40);

/**
 * Basic enrichment function (copied from enrichService to avoid circular dependency)
 * @param {Object} item - The item to enrich
 * @param {string} type - The type of item ('movie', 'series', or 'anime')
 * @param {Object} options - Options for enrichment
 * @returns {Promise<Object>} - The enriched item
 */
async function basicEnrichItem(item, type, options = {}) {
  // Set default options
  const fetchSeasons = options.fetchSeasons ?? true;

  const rawTitle = item.title;
  // Simple title cleaning
  const cleanedTitle = rawTitle
    .replace(/\s*\(\d{4}\)/, '')
    .trim()
    .normalize('NFD').replace(/[\u0300-\u036f]/g, '');

  const tmdbType = type === 'movie' ? 'movie' : 'tv';
  const tmdbData = await limit(() => enrichWithTmdb(cleanedTitle, tmdbType, item.season || item.metadata?.year));

  logger.info(`Cleaned title: ${rawTitle} -> ${cleanedTitle}`);
  if (!tmdbData) {
    logger.warn(`No TMDb match for ${cleanedTitle} (type: ${tmdbType}, year: ${item.metadata?.year || 'none'})`);
  } else {
    logger.info(`Enriched ${cleanedTitle} with TMDb ID: ${tmdbData.id}`);
  }

  let jikanData = null;
  if (type === 'anime') {
    try {
      logger.debug(`Attempting Jikan enrichment for ${cleanedTitle} (language: ${item.animeLanguage}, year: ${item.metadata?.year})`);

      // Use the appropriate Jikan function based on options
      if (fetchSeasons) {
        jikanData = await jikanService.enrichAnimeWithJikanDataAndSeasons({ ...item, cleanedTitle }, true);
        if (jikanData && jikanData.jikan && jikanData.jikanSeasons) {
          logger.info(`Enriched anime ${cleanedTitle} with Jikan data: MAL ID ${jikanData.jikan.mal_id} and ${jikanData.jikanSeasons.length} seasons`);
        } else if (jikanData && jikanData.jikan) {
          logger.info(`Enriched anime ${cleanedTitle} with Jikan data: MAL ID ${jikanData.jikan.mal_id} but no seasons found`);
        } else {
          logger.warn(`No Jikan data found for ${cleanedTitle}`);
        }
      } else {
        jikanData = await jikanService.enrichAnimeWithJikanData({ ...item, cleanedTitle });
        if (jikanData && jikanData.jikan) {
          logger.info(`Enriched anime ${cleanedTitle} with Jikan data: MAL ID ${jikanData.jikan.mal_id}`);
        } else {
          logger.warn(`No Jikan data found for ${cleanedTitle}`);
        }
      }
    } catch (error) {
      logger.error(`Failed to enrich anime ${cleanedTitle} with Jikan: ${error.message}`);
    }
  }

  // If no data was found, return the item with empty data
  if (!tmdbData && !jikanData) {
    return {
      ...item,
      cleanedTitle,
      tmdb: {},
      jikan: {},
      metadata: item.metadata || {}
    };
  }

  // Format metadata
  const metadata = tmdbData ? {
    synopsis: tmdbData.overview || item.metadata?.synopsis || '',
    actors: tmdbData.credits?.cast?.slice(0, 5).map(c => c.name) || item.metadata?.actors || [],
    year: (tmdbData.release_date || tmdbData.first_air_date)?.split('-')[0] || item.metadata?.year || '',
    genre: tmdbData.genres?.map(g => g.name).join(', ') || item.metadata?.genre || '',
    origin: tmdbData.origin_country?.join(', ') || item.metadata?.origin || '',
    creator: tmdbType === 'tv'
      ? tmdbData.created_by?.map(c => c.name).join(', ') || item.metadata?.creator || ''
      : tmdbData.credits?.crew?.find(c => c.job === 'Director')?.name || item.metadata?.creator || ''
  } : item.metadata || {};

  // Format TMDB data
  const tmdb = tmdbData ? {
    id: tmdbData.id,
    title: tmdbData.title,
    overview: tmdbData.overview,
    release_date: tmdbData.release_date || tmdbData.first_air_date,
    poster_path: tmdbData.poster_path,
    vote_average: tmdbData.vote_average,
    genres: tmdbData.genres?.map(g => g.name) || []
  } : {};

  // Create the enriched item
  const enrichedItem = {
    ...item,
    cleanedTitle,
    tmdb,
    jikan: jikanData?.jikan || {},
    metadata
  };

  // Fetch and add TMDB seasons data if requested and applicable
  if (fetchSeasons && tmdbData && (type === 'series' || type === 'anime')) {
    try {
      logger.info(`Fetching TMDB seasons data for ${cleanedTitle} (TMDB ID: ${tmdbData.id})`);
      const seasonsData = await fetchAllTmdbSeasons(tmdbData.id);

      if (seasonsData && seasonsData.length > 0) {
        const formattedSeasons = formatTmdbSeasonsForDB(seasonsData);
        logger.info(`Found ${formattedSeasons.length} TMDB seasons for ${cleanedTitle}`);

        // Add seasons data to the enriched item
        enrichedItem.tmdbSeasons = formattedSeasons;

        // If we have a specific season number, also set the tmdbSeason field for backward compatibility
        if (item.season) {
          const seasonNumber = parseInt(item.season, 10);
          if (!isNaN(seasonNumber)) {
            const specificSeason = formattedSeasons.find(s => s.season_number === seasonNumber);
            if (specificSeason) {
              enrichedItem.tmdbSeason = specificSeason;
              logger.info(`Set tmdbSeason field for season ${seasonNumber}`);
            }
          }
        }
      } else {
        logger.warn(`No TMDB seasons found for ${cleanedTitle} (TMDB ID: ${tmdbData.id})`);
      }
    } catch (error) {
      logger.error(`Error fetching TMDB seasons for ${cleanedTitle}: ${error.message}`);
    }
  }

  // Add Jikan seasons data if it was fetched
  if (jikanData && jikanData.jikanSeasons) {
    enrichedItem.jikanSeasons = jikanData.jikanSeasons;
  }

  return enrichedItem;
}

/**
 * Advanced enrichment function that uses Gemini AI for better matching
 * @param {Object} item - The item to enrich
 * @param {string} type - The type of item ('movie', 'series', or 'anime')
 * @param {Object} options - Options for enrichment
 * @returns {Promise<Object>} - The enriched item
 */
async function advancedEnrichItem(item, type, options = {}) {
  // If Gemini API key is missing or quota exceeded, fall back to basic enrichment
  if (!GEMINI_API_KEY || geminiQuotaExceeded) {
    logger.warn(`Advanced enrichment unavailable: ${!GEMINI_API_KEY ? "API key missing" : "Quota exceeded"}. Using basic enrichment.`);
    return basicEnrichItem(item, type, { ...options, useGemini: false });
  }

  // Get base information
  const rawTitle = item.title;
  const yearHint = item.metadata?.year ? parseInt(item.metadata.year, 10) : null;

  // Get optimized search query from Gemini
  const optimizedQuery = await getGeminiSearchQuery(rawTitle, yearHint, type);

  if (!optimizedQuery) {
    logger.warn(`Failed to get optimized query for '${rawTitle}'. Falling back to basic enrichment.`);
    return basicEnrichItem(item, type, { ...options, useGemini: false });
  }

  logger.info(`Using Gemini-optimized query for '${rawTitle}': '${optimizedQuery}'`);

  // Create a modified item with the optimized query as the title
  const modifiedItem = {
    ...item,
    title: optimizedQuery,
    originalTitle: rawTitle // Keep the original title
  };

  // Use the basic enrichment with the modified item
  const enrichedItem = await basicEnrichItem(modifiedItem, type, { ...options, useGemini: true });

  // Restore the original title
  enrichedItem.title = rawTitle;

  // If we have TMDB or Jikan data, verify the match
  if ((type === 'anime' && enrichedItem.jikan?.mal_id) ||
      (enrichedItem.tmdb?.id && (type === 'movie' || type === 'series'))) {

    let matchVerified = false;

    if (type === 'anime' && enrichedItem.jikan?.mal_id) {
      // Verify anime match
      const malId = enrichedItem.jikan.mal_id;
      const jikanTitle = enrichedItem.jikan.title?.default || enrichedItem.jikan.title?.english || '';

      // Calculate similarity
      const similarity = stringSimilarity.compareTwoStrings(
        rawTitle.toLowerCase(),
        jikanTitle.toLowerCase()
      );

      logger.info(`Anime title similarity check: "${rawTitle}" vs "${jikanTitle}" = ${similarity.toFixed(3)}`);

      if (similarity >= TITLE_SIMILARITY_THRESHOLD) {
        matchVerified = true;
        logger.info(`Anime match verified by similarity: ${similarity.toFixed(3)} >= ${TITLE_SIMILARITY_THRESHOLD}`);
      } else {
        // Ask Gemini for verification
        matchVerified = await askGeminiForFinalVerdict(rawTitle, 'anime', malId, jikanTitle);
        logger.info(`Anime match verified by Gemini: ${matchVerified}`);
      }

      // If match is not verified, try with English translation
      if (!matchVerified) {
        logger.warn(`Anime match rejected for '${rawTitle}'. Attempting with English translation...`);

        // Try to translate the title to English
        const translatedTitle = await translateToEnglish(rawTitle);

        if (translatedTitle && translatedTitle !== rawTitle) {
          logger.info(`Translated title: '${rawTitle}' -> '${translatedTitle}'`);

          // Get optimized search query from Gemini using the translated title
          const translatedQuery = await getGeminiSearchQuery(translatedTitle, yearHint, type);

          if (translatedQuery) {
            logger.info(`Using translated query for '${rawTitle}': '${translatedQuery}'`);

            // Create a modified item with the translated query as the title
            const translatedItem = {
              ...item,
              title: translatedQuery,
              originalTitle: rawTitle
            };

            // Use the basic enrichment with the translated item
            const translatedEnrichedItem = await basicEnrichItem(translatedItem, type, { ...options, useGemini: true });

            // If we have Jikan data from the translated query, verify the match
            if (translatedEnrichedItem.jikan?.mal_id) {
              const malId = translatedEnrichedItem.jikan.mal_id;
              const jikanTitle = translatedEnrichedItem.jikan.title?.default || translatedEnrichedItem.jikan.title?.english || '';

              // Ask Gemini for verification with the translated title
              const translatedMatchVerified = await askGeminiForFinalVerdict(translatedTitle, 'anime', malId, jikanTitle);
              logger.info(`Translated anime match verified by Gemini: ${translatedMatchVerified}`);

              if (translatedMatchVerified) {
                // Use the Jikan data from the translated query
                enrichedItem.jikan = translatedEnrichedItem.jikan;
                enrichedItem.jikanSeasons = translatedEnrichedItem.jikanSeasons;
                logger.info(`Using Jikan data from translated query: MAL ID ${enrichedItem.jikan.mal_id}`);
                // Set metadataChanged flag to ensure database is updated
                enrichedItem.metadataChanged = true;
                logger.info(`Setting metadataChanged flag to ensure database update`);
                matchVerified = true;
              }
            }
          }
        }

        // If match is still not verified, clear Jikan data
        if (!matchVerified) {
          logger.warn(`Anime match rejected for '${rawTitle}' even after translation attempt. Clearing Jikan data.`);
          enrichedItem.jikan = {};
          enrichedItem.jikanSeasons = [];
        }
      }
    }

    if (enrichedItem.tmdb?.id && (type === 'movie' || type === 'series')) {
      // Verify TMDB match
      const tmdbId = enrichedItem.tmdb.id;
      const tmdbTitle = enrichedItem.tmdb.title || '';

      // Calculate similarity
      const similarity = stringSimilarity.compareTwoStrings(
        rawTitle.toLowerCase(),
        tmdbTitle.toLowerCase()
      );

      logger.info(`TMDB title similarity check: "${rawTitle}" vs "${tmdbTitle}" = ${similarity.toFixed(3)}`);

      if (similarity >= TITLE_SIMILARITY_THRESHOLD) {
        matchVerified = true;
        logger.info(`TMDB match verified by similarity: ${similarity.toFixed(3)} >= ${TITLE_SIMILARITY_THRESHOLD}`);
      } else {
        // Ask Gemini for verification
        matchVerified = await askGeminiForFinalVerdict(rawTitle, type, tmdbId, tmdbTitle);
        logger.info(`TMDB match verified by Gemini: ${matchVerified}`);
      }

      // If match is not verified, try with English translation
      if (!matchVerified) {
        logger.warn(`TMDB match rejected for '${rawTitle}'. Attempting with English translation...`);

        // Try to translate the title to English
        const translatedTitle = await translateToEnglish(rawTitle);

        if (translatedTitle && translatedTitle !== rawTitle) {
          logger.info(`Translated title: '${rawTitle}' -> '${translatedTitle}'`);

          // Get optimized search query from Gemini using the translated title
          const translatedQuery = await getGeminiSearchQuery(translatedTitle, yearHint, type);

          if (translatedQuery) {
            logger.info(`Using translated query for '${rawTitle}': '${translatedQuery}'`);

            // Create a modified item with the translated query as the title
            const translatedItem = {
              ...item,
              title: translatedQuery,
              originalTitle: rawTitle
            };

            // Use the basic enrichment with the translated item
            const translatedEnrichedItem = await basicEnrichItem(translatedItem, type, { ...options, useGemini: true });

            // If we have TMDB data from the translated query, verify the match
            if (translatedEnrichedItem.tmdb?.id) {
              const tmdbId = translatedEnrichedItem.tmdb.id;
              const tmdbTitle = translatedEnrichedItem.tmdb.title || '';

              // Ask Gemini for verification with the translated title
              const translatedMatchVerified = await askGeminiForFinalVerdict(translatedTitle, type, tmdbId, tmdbTitle);
              logger.info(`Translated TMDB match verified by Gemini: ${translatedMatchVerified}`);

              if (translatedMatchVerified) {
                // Use the TMDB data from the translated query
                enrichedItem.tmdb = translatedEnrichedItem.tmdb;
                enrichedItem.tmdbSeasons = translatedEnrichedItem.tmdbSeasons;
                enrichedItem.tmdbSeason = translatedEnrichedItem.tmdbSeason;
                logger.info(`Using TMDB data from translated query: TMDB ID ${enrichedItem.tmdb.id}`);
                // Set metadataChanged flag to ensure database is updated
                enrichedItem.metadataChanged = true;
                logger.info(`Setting metadataChanged flag to ensure database update`);
                matchVerified = true;
              }
            }
          }
        }

        // If match is still not verified, clear TMDB data
        if (!matchVerified) {
          logger.warn(`TMDB match rejected for '${rawTitle}' even after translation attempt. Clearing TMDB data.`);
          enrichedItem.tmdb = {};
          enrichedItem.tmdbSeasons = [];
          enrichedItem.tmdbSeason = null;
        }
      }
    }
  }

  return enrichedItem;
}

module.exports = {
  advancedEnrichItem,
  isGeminiAvailable: () => !!GEMINI_API_KEY && !geminiQuotaExceeded
};
