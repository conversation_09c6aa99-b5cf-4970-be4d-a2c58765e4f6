// src/api/routes/api.js
const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const Movie = require('../../db/models/Movie');
const Series = require('../../db/models/Series');
const Anime = require('../../db/models/Anime');
const LiveTV = require('../../db/models/LiveTV');
const logger = require('../../utils/logger');
const { fetchSourceStreamUrl } = require('../../utils/sourceStreamFetcher');

router.get('/', (req, res) => {
  logger.info('API root accessed');
  res.json({ message: 'NetStream API' });
});

router.get('/movies', async (req, res) => {
  try {
    const { sort, page = 1, limit = 20 } = req.query;
    logger.info(`Fetching movies: page=${page}, limit=${limit}, sort=${sort}`);
    let sortOptions = {};
    if (sort === 'latest') sortOptions = { updatedAt: -1 };
    if (sort === 'alpha') sortOptions = { 'tmdb.title': 1, title: 1 };
    if (sort === 'release') sortOptions = { createdAt: -1 };
    const movies = await Movie.find()
      .sort(sortOptions)
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    const response = movies.map(m => ({
      ...m.toObject(),
      displayTitle: m.tmdb?.title || m.title || 'Untitled'
    }));
    logger.debug(`Returning ${response.length} movies`);
    res.json(response);
  } catch (err) {
    logger.error(`Movies fetch error: ${err.message}`, { stack: err.stack });
    res.status(500).json({ error: err.message });
  }
});

router.get('/series', async (req, res) => {
  try {
    const { sort, page = 1, limit = 20 } = req.query;
    logger.info(`Fetching series: page=${page}, limit=${limit}, sort=${sort}`);
    let sortOptions = {};
    if (sort === 'latest') sortOptions = { updatedAt: -1 };
    if (sort === 'alpha') sortOptions = { 'tmdb.title': 1, title: 1 };
    if (sort === 'release') sortOptions = { createdAt: -1 };
    const series = await Series.find()
      .sort(sortOptions)
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    const response = series.map(s => ({
      ...s.toObject(),
      displayTitle: s.tmdb?.title || s.title || 'Untitled'
    }));
    logger.debug(`Returning ${response.length} series`);
    res.json(response);
  } catch (err) {
    logger.error(`Series fetch error: ${err.message}`, { stack: err.stack });
    res.status(500).json({ error: err.message });
  }
});

router.get('/anime', async (req, res) => {
  try {
    const { sort, page = 1, limit = 20 } = req.query;
    logger.info(`Fetching anime: page=${page}, limit=${limit}, sort=${sort}`);
    let sortOptions = {};
    if (sort === 'latest') sortOptions = { updatedAt: -1 };
    if (sort === 'alpha') sortOptions = { 'tmdb.title': 1, title: 1 };
    if (sort === 'release') sortOptions = { createdAt: -1 };
    const anime = await Anime.find()
      .sort(sortOptions)
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    const response = anime.map(a => ({
      ...a.toObject(),
      displayTitle: a.tmdb?.title || a.title || 'Untitled'
    }));
    logger.debug(`Returning ${response.length} anime`);
    res.json(response);
  } catch (err) {
    logger.error(`Anime fetch error: ${err.message}`, { stack: err.stack });
    res.status(500).json({ error: err.message });
  }
});

router.get('/livetv', async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    logger.info(`Fetching live TV: page=${page}, limit=${limit}`);
    const livetv = await LiveTV.find()
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    const response = livetv.map(l => ({
      ...l.toObject(),
      displayTitle: l.tmdb?.title || l.title || 'Untitled'
    }));
    logger.debug(`Returning ${response.length} live TVs`);
    res.json(response);
  } catch (err) {
    logger.error(`LiveTV fetch error: ${err.message}`, { stack: err.stack });
    res.status(500).json({ error: err.message });
  }
});

router.get('/:type/:id', async (req, res) => {
  const { type, id } = req.params;
  const Model = { movies: Movie, series: Series, anime: Anime, livetv: LiveTV }[type];
  try {
    logger.info(`Fetching ${type} with ID: ${id}`);
    if (!mongoose.Types.ObjectId.isValid(id)) {
      logger.error(`Invalid ID format: ${id}`);
      return res.status(400).json({ error: 'Invalid ID format' });
    }
    const item = await Model.findById(id);
    if (!item) {
      logger.warn(`Item not found: ${type}/${id}`);
      return res.status(404).json({ error: 'Item not found' });
    }
    const data = {
      ...item.toObject(),
      displayTitle: item.tmdb?.title || item.title || 'Untitled'
    };
    logger.debug(`Returning ${type}: ${data.displayTitle}`);
    res.json(data);
  } catch (err) {
    logger.error(`Item fetch error: ${err.message}`, { type, id, stack: err.stack });
    res.status(500).json({ error: err.message });
  }
});

// New endpoint for on-demand sourceStreamUrl fetching
router.get('/stream/:type/:id/:streamId', async (req, res) => {
  const { type, id, streamId } = req.params;
  const Model = { movies: Movie, series: Series, anime: Anime, livetv: LiveTV }[type];
  try {
    logger.info(`Fetching stream for ${type}/${id}/${streamId}`);
    if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(streamId)) {
      logger.error(`Invalid ID format: ${id} or ${streamId}`);
      return res.status(400).json({ error: 'Invalid ID or streamId format' });
    }
    const item = await Model.findById(id);
    if (!item) {
      logger.warn(`Item not found: ${type}/${id}`);
      return res.status(404).json({ error: 'Item not found' });
    }

    const streamingUrls = type === 'movies' || type === 'livetv' ? item.streamingUrls : flattenEpisodes(item.episodes);
    const stream = streamingUrls.find(s => s._id.toString() === streamId);
    if (!stream) {
      logger.warn(`Stream not found: ${streamId}`);
      return res.status(404).json({ error: 'Stream not found' });
    }

    const { url, provider, sourceStreamUrl: existingUrl } = stream;
    const BLACKLISTED_PROVIDERS = ['waaw1.tv', 'smoothpre.com', 'cybervynx.com'];
    if (!url || !provider || BLACKLISTED_PROVIDERS.includes(provider)) {
      logger.info(`Skipping invalid or blacklisted stream`, { url, provider });
      return res.json({ sourceStreamUrl: existingUrl || null });
    }

    const result = await fetchSourceStreamUrl(url, provider);
    if (result && result.url !== existingUrl) {
      logger.info(`New sourceStreamUrl found`, { directUrl: result.url, method: result.method, oldUrl: existingUrl });
      const updateQuery = type === 'movies' || type === 'livetv'
        ? { '_id': id, 'streamingUrls._id': streamId }
        : { '_id': id, 'episodes.streamingUrls._id': streamId };
      const updateSet = type === 'movies' || type === 'livetv'
        ? {
            $set: {
              'streamingUrls.$[streamUrlElem].sourceStreamUrl': result.url,
              'streamingUrls.$[streamUrlElem].size': result.size,
              'streamingUrls.$[streamUrlElem].type': result.type,
              'streamingUrls.$[streamUrlElem].method': result.method,
              'streamingUrls.$[streamUrlElem].lastChecked': new Date()
            }
          }
        : {
            $set: {
              'episodes.$[episodeElem].streamingUrls.$[streamUrlElem].sourceStreamUrl': result.url,
              'episodes.$[episodeElem].streamingUrls.$[streamUrlElem].size': result.size,
              'episodes.$[episodeElem].streamingUrls.$[streamUrlElem].type': result.type,
              'episodes.$[episodeElem].streamingUrls.$[streamUrlElem].method': result.method,
              'episodes.$[episodeElem].streamingUrls.$[streamUrlElem].lastChecked': new Date()
            }
          };
      const updateOptions = type === 'movies' || type === 'livetv'
        ? { arrayFilters: [{ 'streamUrlElem._id': streamId }] }
        : {
            arrayFilters: [
              { 'episodeElem.streamingUrls': { $elemMatch: { _id: streamId } } },
              { 'streamUrlElem._id': streamId }
            ]
          };
      await Model.updateOne(updateQuery, updateSet, updateOptions);
      res.json({ sourceStreamUrl: result.url });
    } else {
      res.json({ sourceStreamUrl: existingUrl || result?.url || null });
    }
  } catch (err) {
    logger.error(`Stream fetch error: ${err.message}`, { type, id, streamId, stack: err.stack });
    res.status(500).json({ error: err.message });
  }
});

router.get('/search', async (req, res) => {
  const { q, page = 1, limit = 20 } = req.query;
  if (!q) return res.status(400).json({ error: 'Query required' });
  try {
    logger.info(`Searching: q=${q}, page=${page}, limit=${limit}`);
    const regex = new RegExp(q, 'i');
    const movies = await Movie.find({ $or: [{ 'tmdb.title': regex }, { title: regex }] })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    const series = await Series.find({ $or: [{ 'tmdb.title': regex }, { title: regex }] })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    const anime = await Anime.find({ $or: [{ 'tmdb.title': regex }, { title: regex }] })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    const livetv = await LiveTV.find({ $or: [{ 'tmdb.title': regex }, { title: regex }] })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    const results = [
      ...movies.map(m => ({ ...m.toObject(), type: 'movies', displayTitle: m.tmdb?.title || m.title || 'Untitled' })),
      ...series.map(s => ({ ...s.toObject(), type: 'series', displayTitle: s.tmdb?.title || s.title || 'Untitled' })),
      ...anime.map(a => ({ ...a.toObject(), type: 'anime', displayTitle: a.tmdb?.title || a.title || 'Untitled' })),
      ...livetv.map(l => ({ ...l.toObject(), type: 'livetv', displayTitle: l.tmdb?.title || l.title || 'Untitled' }))
    ];
    logger.debug(`Returning ${results.length} search results`);
    res.json(results);
  } catch (err) {
    logger.error(`Search error: ${err.message}`, { stack: err.stack });
    res.status(500).json({ error: err.message });
  }
});

router.get('/:type/:id/play', async (req, res) => {
  const { type, id } = req.params;
  const { ep, lang } = req.query;
  try {
    let item, stream;
    switch (type) {
      case 'movies':
        item = await Movie.findById(id);
        stream = item.streamingUrls.find(s => s.isActive && (!lang || s.language === lang)) || item.streamingUrls[0];
        break;
      case 'series':
        item = await Series.findById(id);
        if (ep) {
          const episode = item.episodes.find(e => e.episodeNumber === parseInt(ep));
          stream = episode?.streamingUrls.find(s => s.isActive && (!lang || s.language === lang)) || episode?.streamingUrls[0];
        } else {
          stream = item.episodes[0]?.streamingUrls[0];
        }
        break;
      case 'anime':
        item = await Anime.findById(id);
        if (item.streamingUrls.length) {
          stream = item.streamingUrls.find(s => s.isActive && (!lang || s.language === lang)) || item.streamingUrls[0];
        } else if (ep) {
          const episode = item.episodes.find(e => e.episodeNumber === parseInt(ep));
          stream = episode?.streamingUrls.find(s => s.isActive && (!lang || s.language === lang)) || episode?.streamingUrls[0];
        } else {
          stream = item.episodes[0]?.streamingUrls[0];
        }
        break;
      case 'livetv':
        item = await LiveTV.findById(id);
        stream = item.streamingUrls.find(s => s.isActive) || item.streamingUrls[0];
        break;
      default:
        throw new Error('Invalid type');
    }
    if (!stream) throw new Error('No active stream found');
    logger.info(`Playing ${type} ${id}${ep ? ` ep ${ep}` : ''} in ${lang || stream.language}`);
    res.json({ url: stream.url });
  } catch (error) {
    logger.error(`Play error for ${type} ${id}: ${error.message}`);
    res.status(404).json({ error: 'Stream unavailable' });
  }
});

// Helper function
function flattenEpisodes(episodes) {
  return episodes.reduce((acc, ep) => acc.concat(ep.streamingUrls || []), []);
}

module.exports = router;