import { NextResponse } from 'next/server'

// Extract the base URL from NEXT_PUBLIC_API_URL or use default
const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_API_URL || 'http://netstream-backend-container:3001/graphql'

// Mock wishlist data - in a real app this would come from user preferences/database
const WISHLIST_QUERIES = {
  movies: `
    query GetWishlistMovies($limit: Int, $page: Int) {
      movies(limit: $limit, page: $page, sort: TRENDING) {
        id
        title
        displayTitle
        thumbnail
        image
        metadata {
          year
          synopsis
        }
        tmdb {
          id
          title
          overview
          poster_path
          release_date
          vote_average
          vote_count
          genres
        }
      }
    }
  `,
  series: `
    query GetWishlistSeries($limit: Int, $page: Int) {
      latestSeries(limit: $limit, page: $page) {
        id
        title
        displayTitle
        thumbnail
        image
        season
        episodes {
          episodeNumber
          streamingUrls {
            language
          }
        }
        metadata {
          year
          synopsis
        }
        tmdb {
          id
          title
          overview
          poster_path
          release_date
          vote_average
          vote_count
          genres
        }
      }
    }
  `,
  anime: `
    query GetWishlistAnime($limit: Int, $page: Int) {
      latestAnime(limit: $limit, page: $page) {
        id
        title
        displayTitle
        thumbnail
        image
        episodes {
          episodeNumber
        }
        metadata {
          year
          synopsis
        }
        jikan {
          mal_id
          title {
            default
            english
            japanese
          }
          synopsis
          images {
            jpg {
              image_url
              small_image_url
              large_image_url
            }
          }
          score
          status
        }
      }
    }
  `
}

export async function GET(request) {
  try {
    console.log('🔄 Wishlist API: Fetching mixed wishlist content...')

    const mixedWishlist = []
    const itemsPerType = 4 // Get 4 items from each type

    // Fetch movies, series, and anime in parallel
    const promises = Object.entries(WISHLIST_QUERIES).map(async ([type, query]) => {
      try {
        console.log(`📡 Wishlist API: Fetching ${type}...`)
        
        const response = await fetch(GRAPHQL_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query,
            variables: {
              limit: itemsPerType,
              page: 1
            }
          })
        })

        if (!response.ok) {
          throw new Error(`GraphQL request failed for ${type}: ${response.status}`)
        }

        const result = await response.json()
        
        if (result.errors) {
          console.error(`❌ Wishlist API: GraphQL errors for ${type}:`, result.errors)
          return []
        }

        // Extract data based on type
        let items = []
        if (type === 'movies') {
          items = result.data?.movies || []
        } else if (type === 'series') {
          items = result.data?.latestSeries || []
        } else if (type === 'anime') {
          items = result.data?.latestAnime || []
        }

        // Add type information to each item
        return items.map(item => ({
          ...item,
          __typename: type === 'movies' ? 'Movie' : type === 'series' ? 'Series' : 'Anime'
        }))

      } catch (error) {
        console.error(`💥 Wishlist API error for ${type}:`, error)
        return []
      }
    })

    const results = await Promise.all(promises)
    
    // Flatten and mix the results
    results.forEach(typeItems => {
      mixedWishlist.push(...typeItems)
    })

    // Shuffle the mixed wishlist for variety
    for (let i = mixedWishlist.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [mixedWishlist[i], mixedWishlist[j]] = [mixedWishlist[j], mixedWishlist[i]]
    }

    console.log(`✅ Wishlist API: Returning ${mixedWishlist.length} mixed wishlist items`)

    return NextResponse.json({
      success: true,
      data: mixedWishlist
    })

  } catch (error) {
    console.error('💥 Wishlist API error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
