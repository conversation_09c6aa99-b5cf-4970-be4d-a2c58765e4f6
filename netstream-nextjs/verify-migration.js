#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

const requiredFiles = [
  // App Router Pages
  'src/app/layout.js',
  'src/app/page.js',
  'src/app/movies/page.js',
  'src/app/series/page.js',
  'src/app/anime/page.js',
  'src/app/livetv/page.js',
  'src/app/admin/page.js',
  'src/app/movies/[id]/page.js',
  'src/app/series/[id]/page.js',
  'src/app/anime/[id]/page.js',

  // Context Providers
  'src/context/UserContext.js',
  'src/context/PlayerContext.js',
  'src/context/AdminContext.js',

  // Layout Components
  'src/components/layout/Sidebar.js',
  'src/components/layout/SearchBar.js',
  'src/components/layout/MobileSearchModal.js',

  // UI Components
  'src/components/ui/Carousel.js',
  'src/components/ui/CarouselItem.js',
  'src/components/ui/GridItem.js',

  // Feature Components
  'src/components/features/WishlistButton.js',
  'src/components/features/WishlistCarousels.js',
  'src/components/features/RecentlyWatchedCarousels.js',

  // Home Components
  'src/components/home/<USER>',

  // Media Components
  'src/components/media/MediaGrid.js',
  'src/components/media/MediaDetailPage.js',

  // Player Components
  'src/components/player/VideoPlayer.js',
  'src/components/player/DirectPlayerFix.js',

  // Admin Components
  'src/components/admin/AdminLogin.js',
  'src/components/admin/AdminPanel.js',

  // Common Components
  'src/components/common/LazyImage.js',

  // Hooks
  'src/hooks/useMediaData.js',
  'src/hooks/useWishlist.js',
  'src/hooks/useRecentlyWatched.js',

  // Utilities
  'src/utils/helpers.js',
  'src/utils/constants.js',
  'src/utils/cache.js',

  // Library Files
  'src/lib/apollo.js',
  'src/lib/queries.js',

  // Configuration
  '.env.local',
  'src/app/globals.css',
  'public/default-thumbnail.svg'
]

console.log('🔍 Verifying NetStream Next.js Migration...\n')

let missingFiles = []
let existingFiles = []

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file)
  if (fs.existsSync(filePath)) {
    existingFiles.push(file)
    console.log(`✅ ${file}`)
  } else {
    missingFiles.push(file)
    console.log(`❌ ${file}`)
  }
})

console.log('\n📊 Migration Summary:')
console.log(`✅ Existing files: ${existingFiles.length}`)
console.log(`❌ Missing files: ${missingFiles.length}`)
console.log(`📁 Total files checked: ${requiredFiles.length}`)

if (missingFiles.length === 0) {
  console.log('\n🎉 Migration Complete! All required files are present.')
  console.log('\n🚀 Next steps:')
  console.log('1. Start the development server: npm run dev')
  console.log('2. Open http://localhost:3000 in your browser')
  console.log('3. Test all functionality')
} else {
  console.log('\n⚠️  Migration Incomplete. Missing files:')
  missingFiles.forEach(file => {
    console.log(`   - ${file}`)
  })
}

console.log('\n📈 Migration Progress: ' + Math.round((existingFiles.length / requiredFiles.length) * 100) + '%')
