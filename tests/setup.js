// File: tests/setup.js
// Jest setup file for NetStream Fastify migration tests

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.MONGO_URI = 'mongodb://localhost:27017/netstream-test';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.ADMIN_KEY = 'test-admin-key';

// Increase timeout for database operations
jest.setTimeout(30000);

// Mock external services
jest.mock('node-fetch', () => {
  return jest.fn(() => Promise.resolve({
    ok: true,
    status: 200,
    headers: new Map([['content-type', 'image/jpeg']]),
    arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024))
  }));
});

// Mock Redis for tests
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn(),
    quit: jest.fn(),
    ping: jest.fn().mockResolvedValue('PONG'),
    get: jest.fn().mockResolvedValue(null),
    set: jest.fn().mockResolvedValue('OK'),
    setex: jest.fn().mockResolvedValue('OK'),
    del: jest.fn().mockResolvedValue(1),
    exists: jest.fn().mockResolvedValue(0),
    expire: jest.fn().mockResolvedValue(1),
    mget: jest.fn().mockResolvedValue([]),
    keys: jest.fn().mockResolvedValue([]),
    smembers: jest.fn().mockResolvedValue([]),
    sadd: jest.fn().mockResolvedValue(1),
    pipeline: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue([])
    }),
    status: 'ready'
  }));
});

// Global test utilities
global.testUtils = {
  // Helper to wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Helper to generate test data
  generateTestMovie: (id = 1) => ({
    title: `Test Movie ${id}`,
    cleanedTitle: `test-movie-${id}`,
    thumbnail: `test-thumbnail-${id}.jpg`,
    detailUrl: `test-detail-url-${id}`,
    streamingUrls: [
      { url: `test-stream-${id}.m3u8`, type: 'HLS', language: 'VF' }
    ],
    tmdb: { 
      id: 10000 + id, 
      title: `Test Movie ${id}`, 
      genres: ['Action', 'Drama'] 
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }),
  
  generateTestSeries: (id = 1) => ({
    title: `Test Series ${id}`,
    cleanedTitle: `test-series-${id}`,
    thumbnail: `test-series-thumbnail-${id}.jpg`,
    detailUrl: `test-series-detail-url-${id}`,
    episodes: [
      {
        episodeNumber: '1',
        title: 'Episode 1',
        streamingUrls: [
          { url: `test-series-stream-${id}-1.m3u8`, type: 'HLS', language: 'VF' }
        ]
      }
    ],
    tmdb: { 
      id: 20000 + id, 
      title: `Test Series ${id}`, 
      genres: ['Drama', 'Thriller'] 
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }),
  
  // Helper to create GraphQL query
  createGraphQLQuery: (query, variables = {}) => ({
    query,
    variables
  }),
  
  // Helper to validate GraphQL response
  validateGraphQLResponse: (response) => {
    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload).toHaveProperty('data');
    return payload;
  }
};

// Console override for cleaner test output
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: originalConsole.error // Keep errors visible
};

// Cleanup after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

console.log('Test setup completed');
