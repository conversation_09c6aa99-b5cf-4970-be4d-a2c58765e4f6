// File: src/enrichment/tmdb/tmdbService.js
const axios = require('axios');
const configService = require('../../services/configService');
const logger = require('../../utils/logger');
const { getConfig } = require('../config/enrichmentConfig');
const { cache } = require('../../utils/unifiedCache');
const { rateLimiters } = require('../../utils/intelligentRateLimiter');

const TMDB_BASE_URL = 'https://api.themoviedb.org/3';
// Get rate limit from config
const config = getConfig();
const API_REQUEST_DELAY_MS = config.TMDB_RATE_LIMIT_MS || 1500; // Default: 40 req/min = 1500ms

// Check if caching is enabled in the configuration
const isCachingEnabled = () => config.ENABLE_CACHING !== false;

// Utility function to sleep
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function enrichWithTmdb(title, type = 'multi', season = null) {
  try {
    // Get API key from config service
    const tmdbApiKey = await configService.getTmdbApiKey();
    if (!tmdbApiKey) {
      logger.warn('TMDB API key not configured, skipping enrichment');
      return null;
    }

    // Create cache keys
    const searchCacheKey = `search_${title}_${type}_${season || 'noSeason'}`;

    // Check if we have a cached search result
    if (isCachingEnabled()) {
      const cachedResult = cache.get('tmdb', searchCacheKey);
      if (cachedResult) {
        logger.info(`Using cached TMDB search result for '${title}' (${type})`);
        return cachedResult;
      }
    }

    // Step 1: Search TMDb
    const searchType = type === 'movie' ? 'movie' : 'tv'; // Use specific type, not 'multi'
    let searchUrl = `${TMDB_BASE_URL}/search/${searchType}?api_key=${tmdbApiKey}&query=${encodeURIComponent(title)}&language=fr-FR`;
    if (season && type === 'tv') {
      searchUrl += `&season_number=${season}`;
    }

    logger.info(`Searching TMDB for '${title}' (${searchType})`);
    const { data: searchData } = await axios.get(searchUrl);
    const result = searchData.results[0];
    if (!result) {
      logger.warn(`No TMDb match for ${title} (${searchType})`);
      return null;
    }

    // Create a cache key for the details
    const detailsCacheKey = `details_${searchType}_${result.id}`;

    // Check if we have cached details
    if (isCachingEnabled()) {
      const cachedDetails = cache.get('tmdb', detailsCacheKey);
      if (cachedDetails) {
        logger.info(`Using cached TMDB details for '${title}' (ID: ${result.id})`);

        // Cache the search result too
        cache.set('tmdb', searchCacheKey, cachedDetails);

        return cachedDetails;
      }
    }

    // Step 2: Fetch details with credits
    const detailType = type === 'movie' ? 'movie' : 'tv';
    const detailUrl = `${TMDB_BASE_URL}/${detailType}/${result.id}?api_key=${tmdbApiKey}&language=fr-FR&append_to_response=credits`;

    logger.info(`Fetching TMDB details for '${title}' (ID: ${result.id})`);
    const { data: detailData } = await axios.get(detailUrl);

    // Format the enriched data
    const enrichedData = {
      id: detailData.id,
      title: detailData.title || detailData.name,
      overview: detailData.overview || '',
      release_date: detailData.release_date || detailData.first_air_date || '',
      first_air_date: detailData.first_air_date || '', // For series
      poster_path: detailData.poster_path || null,
      vote_average: detailData.vote_average || null,
      genres: detailData.genres || [], // Array of { id, name }
      origin_country: detailData.origin_country || detailData.production_countries?.map(c => c.name) || [],
      credits: {
        cast: detailData.credits?.cast || [],
        crew: detailData.credits?.crew || []
      },
      created_by: detailData.created_by || [] // For series
    };

    // Cache both the search result and the details
    if (isCachingEnabled()) {
      cache.set('tmdb', searchCacheKey, enrichedData);
      cache.set('tmdb', detailsCacheKey, enrichedData);
    }

    return enrichedData;
  } catch (error) {
    logger.error(`TMDb error for ${title}: ${error.message}`);
    return null;
  }
}

/**
 * Fetches season-specific data from TMDB for a TV series
 * @param {number} tmdbId - The TMDB ID of the TV series
 * @param {string|number} seasonNumber - The season number to fetch
 * @returns {Promise<Object|null>} - Season data or null if not found
 */
async function fetchTmdbSeason(tmdbId, seasonNumber) {
  try {
    if (!tmdbId || !seasonNumber) {
      logger.warn(`Missing required parameters for fetchTmdbSeason: tmdbId=${tmdbId}, seasonNumber=${seasonNumber}`);
      return null;
    }

    // Get API key from config service
    const tmdbApiKey = await configService.getTmdbApiKey();
    if (!tmdbApiKey) {
      logger.warn('TMDB API key not configured, skipping season data fetch');
      return null;
    }

    // Convert seasonNumber to a number if it's a string
    const seasonNum = parseInt(seasonNumber, 10);
    if (isNaN(seasonNum)) {
      logger.warn(`Invalid season number for fetchTmdbSeason: ${seasonNumber}`);
      return null;
    }

    // Check if caching is enabled and we have cached data for this season
    const cacheKey = `season_${tmdbId}_${seasonNum}`;
    if (isCachingEnabled()) {
      const cachedData = cache.get('tmdb', cacheKey);
      if (cachedData) {
        logger.info(`Using cached TMDB season data for series ID ${tmdbId}, season ${seasonNum}`);
        return cachedData;
      }
    }

    const url = `${TMDB_BASE_URL}/tv/${tmdbId}/season/${seasonNum}?api_key=${tmdbApiKey}&language=fr-FR`;
    logger.info(`Fetching TMDB season data: ${url}`);

    const { data } = await axios.get(url);

    if (!data) {
      logger.warn(`No TMDB season data found for series ID ${tmdbId}, season ${seasonNum}`);
      return null;
    }

    // Format the season data
    const seasonData = {
      air_date: data.air_date || '',
      tmdb_season_id: data.id,
      name: data.name || `Season ${seasonNum}`,
      overview: data.overview || '',
      poster_path: data.poster_path || '',
      season_number: data.season_number,
      vote_average: data.vote_average || 0,
      episodes: data.episodes?.map(ep => ({
        air_date: ep.air_date || '',
        episode_number: ep.episode_number,
        tmdb_episode_id: ep.id,
        name: ep.name || `Episode ${ep.episode_number}`,
        overview: ep.overview || '',
        still_path: ep.still_path || '',
        vote_average: ep.vote_average || 0
      })) || []
    };

    // Cache the result if caching is enabled
    if (isCachingEnabled()) {
      cache.set('tmdb', cacheKey, seasonData);
    }

    return seasonData;
  } catch (error) {
    logger.error(`Error fetching TMDB season data for series ID ${tmdbId}, season ${seasonNumber}: ${error.message}`);
    return null;
  }
}

/**
 * Fetches all seasons for a TV series from TMDB
 * @param {number} seriesId - The TMDB ID of the TV series
 * @returns {Promise<Array>} - Array of season data objects or empty array if none found
 */
async function fetchAllTmdbSeasons(seriesId) {
  if (!seriesId) {
    logger.warn(`Invalid seriesId (${seriesId}) for fetchAllTmdbSeasons`);
    return [];
  }

  // Check if caching is enabled and we have cached seasons data for this series
  const cacheKey = `seasons_${seriesId}`;
  if (isCachingEnabled()) {
    const cachedData = cache.get('tmdb', cacheKey);
    if (cachedData) {
      logger.info(`Using cached TMDB seasons data for series ID ${seriesId} (${cachedData.length} seasons)`);
      return cachedData;
    }
  }

  // First, get the TV show details to know how many seasons exist
  const url = `${TMDB_BASE_URL}/tv/${seriesId}`;
  const params = {
    api_key: tmdbApiKey,
    language: 'fr-FR',
  };

  logger.info(`Fetching TMDB TV Series Details to get season count: SeriesID=${seriesId}`);
  try {
    const response = await axios.get(url, { params });

    if (!response.data || !response.data.seasons || !Array.isArray(response.data.seasons)) {
      logger.warn(`No seasons data found in TMDB TV series ${seriesId} details.`);
      return [];
    }

    const seasons = response.data.seasons;
    logger.info(`Found ${seasons.length} seasons for TMDB TV series ${seriesId}.`);

    // Fetch detailed data for each season sequentially to avoid rate limiting
    const seasonDetailsResults = [];
    for (const season of seasons) {
      // Skip season 0 (usually specials) if needed
      // if (season.season_number === 0) continue;

      const seasonData = await fetchTmdbSeason(seriesId, season.season_number);
      if (seasonData) {
        seasonDetailsResults.push(seasonData);
      }

      // Add a delay between requests to avoid rate limiting
      await sleep(API_REQUEST_DELAY_MS);
    }

    // Filter out null results (failed fetches)
    const validSeasonDetails = seasonDetailsResults.filter(season => season !== null);

    logger.info(`Successfully fetched details for ${validSeasonDetails.length} out of ${seasons.length} seasons for series ${seriesId}.`);

    // Cache the results if caching is enabled
    if (isCachingEnabled()) {
      cache.set('tmdb', cacheKey, validSeasonDetails);
    }

    return validSeasonDetails;
  } catch (error) {
    if (error.response?.status === 404) {
      logger.error(`TMDB TV Series endpoint returned 404 for ID ${seriesId}. Series might not exist.`);
    } else if (error.response?.status === 429) {
      logger.warn(`TMDB Rate Limit (429) during TV Series fetch for ID ${seriesId}.`);
    } else {
      logger.error(`Failed to fetch TMDB TV Series details for ID ${seriesId}: ${error.message}`);
    }
    return [];
  }
}

/**
 * Formats multiple seasons data for database storage
 * @param {Array} seasonsData - Array of season data objects from TMDB
 * @returns {Array} - Formatted seasons data for database storage
 */
function formatTmdbSeasonsForDB(seasonsData) {
  if (!seasonsData || !Array.isArray(seasonsData) || seasonsData.length === 0) {
    logger.warn("No valid seasons data provided for formatting.");
    return [];
  }

  const formattedSeasons = [];
  for (const seasonData of seasonsData) {
    if (!seasonData || !seasonData.tmdb_season_id || typeof seasonData.season_number === "undefined") {
      logger.warn("Missing essential season data (tmdb_season_id or season_number).");
      continue;
    }

    formattedSeasons.push(seasonData);
  }

  logger.info(`Formatted ${formattedSeasons.length} out of ${seasonsData.length} seasons for DB.`);
  return formattedSeasons;
}

module.exports = {
  enrichWithTmdb,
  fetchTmdbSeason,
  fetchAllTmdbSeasons,
  formatTmdbSeasonsForDB
};