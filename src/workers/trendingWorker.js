const cron = require('node-cron');
const mongoose = require('mongoose');
const { mongoUri } = require('../config/env');
const logger = require('../utils/logger');
const { updateTrendingData } = require('./scrapeWorker');

// Function to ensure database connection
async function ensureDbConnection() {
  if (mongoose.connection.readyState !== 1) {
    logger.info('Connecting to MongoDB...');
    try {
      await mongoose.connect(mongoUri, {
        serverSelectionTimeoutMS: 30000, // Increase timeout to 30 seconds
        connectTimeoutMS: 30000,
      });
      logger.info('Connected to MongoDB');
    } catch (error) {
      logger.error(`MongoDB connection error: ${error.message}`);
      throw error;
    }
  }
  return mongoose.connection;
}

// Function to safely run the update with connection handling
async function safeUpdateTrending() {
  let connection;
  try {
    connection = await ensureDbConnection();
    await updateTrendingData();
    logger.info('Trending data update completed successfully.');
  } catch (error) {
    logger.error(`Error during trending update: ${error.message}`, { stack: error.stack });
  }
}

console.log('Starting trending update cron job...');

// Schedule the job to run every 6 hours instead of once per day to keep data fresh
cron.schedule('0 */6 * * *', async () => {
  console.log('Starting scheduled trending data update...');
  await safeUpdateTrending();
});

// Run an initial update when the worker starts
safeUpdateTrending().catch(error => {
  console.error('Initial trending update failed:', error);
});

console.log('Trending update cron job started.');