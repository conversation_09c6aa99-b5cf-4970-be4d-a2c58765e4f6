// File: netlify/functions/server.js
const path = require('path');
const serverless = require('serverless-http');
const { performance } = require('perf_hooks'); // For more precise timing

// --- Configuration ---
const MAX_WAIT_TIME_MS = 8000; // Reduce wait time slightly below 10s default
const CHECK_INTERVAL_MS = 250; // Check more frequently

let serverlessHandler;
let appInitializationPromise = null;

function checkAppReadiness(expressApp) {
    // Same heuristic check for the /graphql route
    if (expressApp?._router?.stack) {
        return expressApp._router.stack.some(layer =>
            (layer.route && layer.route.path === '/graphql') ||
            (layer.name === 'router' && layer.handle?.stack?.some(inner => inner.route && inner.route.path === '/graphql'))
        );
    }
    return false;
}

async function initializeAppAndHandler() {
    const initStartTime = performance.now();
    console.log(`[Netlify Fn Init @ ${initStartTime.toFixed(0)}ms] Initializing App and Handler...`);

    // --- Require App ---
    console.time("[Netlify Fn Init] require('../../server.js') duration");
    let app;
    try {
        app = require(path.join(__dirname, '..', '..', 'server.js'));
        console.timeEnd("[Netlify Fn Init] require('../../server.js') duration");
        console.log("[Netlify Fn Init] Root server.js required.");
        if (!app) throw new Error("Required app is null or undefined.");
    } catch (requireError) {
        console.error("[Netlify Fn Init] CRITICAL: Error requiring root server.js:", requireError);
        throw requireError; // Stop initialization if require fails
    }
    // --- End Require App ---

    return new Promise((resolve, reject) => {
        let elapsedTime = 0;
        const readinessCheckStartTime = performance.now();
        console.log(`[Netlify Fn Init @ ${readinessCheckStartTime.toFixed(0)}ms] Starting readiness check loop (max wait: ${MAX_WAIT_TIME_MS}ms)`);

        const intervalId = setInterval(() => {
            if (checkAppReadiness(app)) {
                clearInterval(intervalId);
                const readyTime = performance.now();
                console.log(`[Netlify Fn Init @ ${readyTime.toFixed(0)}ms] App deemed ready after ${(readyTime - readinessCheckStartTime).toFixed(0)}ms.`);
                try {
                    console.time("[Netlify Fn Init] serverless(app) init duration");
                    const handler = serverless(app);
                    console.timeEnd("[Netlify Fn Init] serverless(app) init duration");
                    console.log("[Netlify Fn Init] Serverless handler created successfully.");
                    resolve(handler);
                } catch (error) {
                    console.error("[Netlify Fn Init] Error initializing serverless-http AFTER app ready:", error);
                    reject(error);
                }
                return;
            }

            elapsedTime += CHECK_INTERVAL_MS;
            if (elapsedTime >= MAX_WAIT_TIME_MS) {
                clearInterval(intervalId);
                const readinessTimeoutTime = performance.now();
                console.warn(`[Netlify Fn Init @ ${readinessTimeoutTime.toFixed(0)}ms] App readiness check TIMED OUT after ${MAX_WAIT_TIME_MS}ms.`);
                // Attempt to initialize anyway
                try {
                    console.warn("[Netlify Fn Init] Attempting serverless-http initialization despite readiness timeout...");
                    console.time("[Netlify Fn Init] serverless(app) init duration (TIMEOUT FALLBACK)");
                    const handler = serverless(app);
                    console.timeEnd("[Netlify Fn Init] serverless(app) init duration (TIMEOUT FALLBACK)");
                    console.warn("[Netlify Fn Init] Serverless handler created (TIMEOUT FALLBACK).");
                    resolve(handler);
                } catch (error) {
                    console.error("[Netlify Fn Init] Error initializing serverless-http on TIMEOUT FALLBACK:", error);
                    reject(error);
                }
            }
        }, CHECK_INTERVAL_MS);
    });
}

exports.handler = async (event, context) => {
    const handlerStartTime = performance.now();
    console.log(`[Netlify Fn Handler @ ${handlerStartTime.toFixed(0)}ms] Handler invoked. Path: ${event.path}. RequestId: ${context.awsRequestId}`);
    process.env.NETLIFY_FUNCTIONS_RUNNING = 'true';
    context.callbackWaitsForEmptyEventLoop = false;

    // Initialize handler only once
    if (!appInitializationPromise) {
        console.log("[Netlify Fn Handler] No cached handler promise. Triggering initialization.");
        appInitializationPromise = initializeAppAndHandler();
    } else {
         console.log("[Netlify Fn Handler] Handler promise exists. Awaiting resolution (if pending).");
    }

    try {
        console.time("[Netlify Fn Handler] await appInitializationPromise duration");
        serverlessHandler = await appInitializationPromise;
        console.timeEnd("[Netlify Fn Handler] await appInitializationPromise duration");
        const initCompleteTime = performance.now();
        console.log(`[Netlify Fn Handler @ ${initCompleteTime.toFixed(0)}ms] Initialization complete (Total init time: ${(initCompleteTime - handlerStartTime).toFixed(0)}ms).`);
    } catch (initializationError) {
        console.error("[Netlify Fn Handler] CRITICAL: Failed to get initialized serverless handler:", initializationError);
        return { /* ... error response ... */ };
    }

    console.log("[Netlify Fn Handler] Executing actual serverless handler...");
    console.time("[Netlify Fn Handler] serverlessHandler(event, context) duration");
    const response = await serverlessHandler(event, context);
    console.timeEnd("[Netlify Fn Handler] serverlessHandler(event, context) duration");
    const executionEndTime = performance.now();
    console.log(`[Netlify Fn Handler @ ${executionEndTime.toFixed(0)}ms] Serverless handler execution finished (Total handler time: ${(executionEndTime - handlerStartTime).toFixed(0)}ms).`);

    return response;
};