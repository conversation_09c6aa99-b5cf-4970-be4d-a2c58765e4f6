const axios = require('axios');
const logger = require('../../utils/logger');
const { getConfig } = require('../config/enrichmentConfig');
const { cache } = require('../../utils/unifiedCache');
const { rateLimiters } = require('../../utils/intelligentRateLimiter');

// Get rate limit from config
const config = getConfig();

class JikanService {
  constructor() {
    this.baseUrl = 'https://api.jikan.moe/v4';
    this.rateLimit = config.JIKAN_RATE_LIMIT_MS || 1000; // Default: 60 req/min = 1000ms
    this.lastRequest = 0;
    this.maxRetries = 3;
    this.retryBaseDelay = 2000; // 2s base delay for retries

    // Initialize local cache objects
    this.relationsCache = new Map();
    this.seasonsCache = new Map();

    // Check if caching is enabled in the configuration
    this.isCachingEnabled = () => config.ENABLE_CACHING !== false;
  }

  async waitForRateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequest;
    if (timeSinceLastRequest < this.rateLimit) {
      await new Promise(resolve => setTimeout(resolve, this.rateLimit - timeSinceLastRequest));
    }
    this.lastRequest = Date.now();
  }

  async searchAnime(title) {
    // Check if caching is enabled and we have a cached result
    if (this.isCachingEnabled()) {
      const cachedResult = cache.get('jikan', `search_${title}`);
      if (cachedResult !== null) {
        logger.info(`Using cached Jikan search result for "${title}"`);
        return cachedResult;
      }
    }

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        await this.waitForRateLimit();
        logger.info(`Searching Jikan for title: "${title}" (Attempt ${attempt}/${this.maxRetries})`);
        const response = await axios.get(`${this.baseUrl}/anime`, {
          params: {
            q: title,
            limit: 1
          },
          timeout: 10000
        });
        const result = response.data.data[0];
        if (result) {
          logger.info(`Found Jikan match: MAL ID ${result.mal_id}, Title: "${result.title}"`);
          // Cache the result if caching is enabled
          if (this.isCachingEnabled()) {
            cache.set('jikan', `search_${title}`, result);
          }
        } else {
          logger.warn(`No Jikan match found for "${title}"`);
          // Cache null result to avoid repeated searches if caching is enabled
          if (this.isCachingEnabled()) {
            cache.set('jikan', `search_${title}`, null);
          }
        }
        return result || null;
      } catch (error) {
        if (error.response && error.response.status === 429) {
          const delay = this.retryBaseDelay * Math.pow(2, attempt - 1); // 2s, 4s, 8s
          logger.warn(`Rate limit hit for "${title}", retrying in ${delay}ms (Attempt ${attempt}/${this.maxRetries})`);
          if (attempt === this.maxRetries) {
            logger.error(`Exhausted retries for "${title}": ${error.message}`);
            return null;
          }
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          logger.error(`Error searching anime "${title}": ${error.message}`);
          return null;
        }
      }
    }
    return null;
  }

  async getAnimeById(malId) {
    // Check if caching is enabled and we have a cached result
    if (this.isCachingEnabled()) {
      const cachedResult = cache.get('jikan', `anime_${malId}`);
      if (cachedResult) {
        logger.info(`Using cached Jikan anime data for MAL ID: ${malId}`);
        return cachedResult;
      }
    }

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        await this.waitForRateLimit();
        logger.info(`Fetching Jikan full data for MAL ID: ${malId} (Attempt ${attempt}/${this.maxRetries})`);
        const response = await axios.get(`${this.baseUrl}/anime/${malId}/full`, {
          timeout: 10000
        });

        // Cache the result if caching is enabled
        const animeData = response.data.data;
        if (this.isCachingEnabled()) {
          cache.set('jikan', `anime_${malId}`, animeData);
        }

        return animeData;
      } catch (error) {
        if (error.response && error.response.status === 429) {
          const delay = this.retryBaseDelay * Math.pow(2, attempt - 1);
          logger.warn(`Rate limit hit for MAL ID ${malId}, retrying in ${delay}ms (Attempt ${attempt}/${this.maxRetries})`);
          if (attempt === this.maxRetries) {
            logger.error(`Exhausted retries for MAL ID ${malId}: ${error.message}`);
            return null;
          }
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          logger.error(`Error fetching anime ID ${malId}: ${error.message}`);
          return null;
        }
      }
    }
    return null;
  }

  /**
   * Get relations for an anime by MAL ID
   * @param {number} malId - MyAnimeList ID
   * @returns {Promise<Array>} - Array of related anime
   */
  async getAnimeRelations(malId) {
    // Check if caching is enabled and we have a cached result
    if (this.isCachingEnabled() && this.relationsCache.has(malId)) {
      const cachedResult = this.relationsCache.get(malId);
      logger.info(`Using cached Jikan relations for MAL ID: ${malId}`);
      return cachedResult;
    }

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        await this.waitForRateLimit();
        logger.info(`Fetching relations for MAL ID: ${malId} (Attempt ${attempt}/${this.maxRetries})`);
        const response = await axios.get(`${this.baseUrl}/anime/${malId}/relations`, {
          timeout: 10000
        });

        const relations = response.data.data || [];

        // Cache the result if caching is enabled
        if (this.isCachingEnabled()) {
          this.relationsCache.set(malId, relations);
        }

        return relations;
      } catch (error) {
        if (error.response && error.response.status === 429) {
          const delay = this.retryBaseDelay * Math.pow(2, attempt - 1);
          logger.warn(`Rate limit hit for relations MAL ID ${malId}, retrying in ${delay}ms (Attempt ${attempt}/${this.maxRetries})`);
          if (attempt === this.maxRetries) {
            logger.error(`Exhausted retries for relations MAL ID ${malId}: ${error.message}`);
            return [];
          }
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          logger.error(`Error fetching relations for MAL ID ${malId}: ${error.message}`);
          return [];
        }
      }
    }
    return [];
  }

  /**
   * Recursively fetch all seasons related to an anime
   * @param {number} malId - MyAnimeList ID
   * @param {Set} processedIds - Set of already processed MAL IDs to avoid cycles
   * @returns {Promise<Array>} - Array of seasons in chronological order
   */
  async fetchRelatedSeasons(malId, processedIds = new Set()) {
    // Avoid processing the same anime twice
    if (processedIds.has(malId)) {
      return [];
    }

    processedIds.add(malId);

    // Get the current anime's full data
    const animeData = await this.getAnimeById(malId);
    if (!animeData) {
      return [];
    }

    // Initialize the seasons array with the current anime
    const seasons = [animeData];

    // Get the relations for this anime
    const relations = await this.getAnimeRelations(malId);

    // Find prequels and sequels
    const prequels = [];
    const sequels = [];

    for (const relation of relations) {
      if (relation.relation === 'Prequel') {
        for (const entry of relation.entry) {
          if (entry.type === 'anime') {
            prequels.push(parseInt(entry.mal_id));
          }
        }
      } else if (relation.relation === 'Sequel') {
        for (const entry of relation.entry) {
          if (entry.type === 'anime') {
            sequels.push(parseInt(entry.mal_id));
          }
        }
      }
    }

    // Log what we found
    logger.debug(`Found ${prequels.length} prequels and ${sequels.length} sequels for MAL ID ${malId}`);
    if (prequels.length > 0) logger.debug(`Prequels: ${prequels.join(', ')}`);
    if (sequels.length > 0) logger.debug(`Sequels: ${sequels.join(', ')}`);

    // Recursively fetch prequels (going backward in time)
    for (const prequelId of prequels) {
      const prequelSeasons = await this.fetchRelatedSeasons(prequelId, processedIds);
      // Add prequels to the beginning of the array
      seasons.unshift(...prequelSeasons);
    }

    // Recursively fetch sequels (going forward in time)
    for (const sequelId of sequels) {
      const sequelSeasons = await this.fetchRelatedSeasons(sequelId, processedIds);
      // Add sequels to the end of the array
      seasons.push(...sequelSeasons);
    }

    return seasons;
  }

  /**
   * Get all seasons for an anime, including prequels and sequels
   * @param {number} malId - MyAnimeList ID
   * @param {number} requestedSeasonNumber - Optional specific season number requested
   * @returns {Promise<Object>} - Object containing seasons data and metadata
   */
  async getAnimeSeasons(malId, requestedSeasonNumber = null) {
    try {
      // Create a cache key that includes the requested season number
      const cacheKey = `seasons_${malId}_${requestedSeasonNumber || 'all'}`;

      // Check if caching is enabled and we have a cached result
      if (this.isCachingEnabled() && this.seasonsCache.has(cacheKey)) {
        const cachedResult = this.seasonsCache.get(cacheKey);
        logger.info(`Using cached Jikan seasons for MAL ID: ${malId}`);
        return cachedResult;
      }

      logger.info(`Starting seasons fetch for MAL ID: ${malId}`);

      // Fetch all related seasons
      const allSeasons = await this.fetchRelatedSeasons(malId, new Set());

      // Remove duplicates based on mal_id
      const uniqueSeasons = [];
      const seenIds = new Set();

      for (const season of allSeasons) {
        if (!seenIds.has(season.mal_id)) {
          seenIds.add(season.mal_id);
          uniqueSeasons.push(season);
        }
      }

      // Sort seasons chronologically based on aired.from date
      uniqueSeasons.sort((a, b) => {
        const dateA = a.aired && a.aired.from ? new Date(a.aired.from) : new Date(0);
        const dateB = b.aired && b.aired.from ? new Date(b.aired.from) : new Date(0);
        return dateA - dateB;
      });

      logger.info(`Found ${uniqueSeasons.length} unique seasons for MAL ID ${malId}`);

      // Format the seasons data
      let formattedSeasons = this.formatJikanSeasons(uniqueSeasons);

      // Check if a specific season number was requested but not found
      if (requestedSeasonNumber &&
          requestedSeasonNumber > formattedSeasons.length &&
          formattedSeasons.length > 0) {

        logger.warn(`Requested season ${requestedSeasonNumber} not found in MyAnimeList. Only ${formattedSeasons.length} season(s) exist for this anime.`);
        logger.info(`Using actual seasons data from MyAnimeList without creating virtual seasons.`);

        // Add a flag to the result to indicate the season mismatch
        formattedSeasons.forEach(season => {
          season.season_mismatch = true;
        });
      }

      const result = {
        success: true,
        originalMalId: malId,
        seasonCount: formattedSeasons.length,
        seasons: formattedSeasons
      };

      // Cache the result if caching is enabled
      if (this.isCachingEnabled()) {
        this.seasonsCache.set(cacheKey, result);
      }

      return result;
    } catch (error) {
      logger.error(`Error fetching seasons for MAL ID ${malId}: ${error.message}`);
      return {
        success: false,
        message: `Error: ${error.message}`,
        originalMalId: malId,
        seasonCount: 0,
        seasons: []
      };
    }
  }

  /**
   * Format raw Jikan seasons data into a more usable structure
   * @param {Array} seasonsData - Raw seasons data from Jikan API
   * @returns {Array} - Formatted seasons data
   */
  formatJikanSeasons(seasonsData) {
    if (!seasonsData || !Array.isArray(seasonsData) || seasonsData.length === 0) {
      return [];
    }

    return seasonsData.map((season, index) => {
      return {
        mal_id: season.mal_id,
        title: season.title,
        title_english: season.title_english,
        title_japanese: season.title_japanese,
        season_number: index + 1, // Assign a sequential season number
        episodes: season.episodes,
        aired: season.aired,
        images: season.images,
        synopsis: season.synopsis,
        score: season.score,
        season: season.season, // Spring, Summer, Fall, Winter
        year: season.year,
        url: season.url
      };
    });
  }

  formatJikanData(jikanResponse) {
    if (!jikanResponse) return null;

    return {
      mal_id: jikanResponse.mal_id,
      title: {
        default: jikanResponse.title,
        english: jikanResponse.title_english,
        japanese: jikanResponse.title_japanese,
        synonyms: jikanResponse.title_synonyms
      },
      type: jikanResponse.type,
      source: jikanResponse.source,
      episodes: jikanResponse.episodes,
      status: jikanResponse.status,
      airing: jikanResponse.airing,
      aired: jikanResponse.aired,
      duration: jikanResponse.duration,
      rating: jikanResponse.rating,
      score: jikanResponse.score,
      scored_by: jikanResponse.scored_by,
      rank: jikanResponse.rank,
      popularity: jikanResponse.popularity,
      members: jikanResponse.members,
      favorites: jikanResponse.favorites,
      synopsis: jikanResponse.synopsis,
      background: jikanResponse.background,
      season: jikanResponse.season,
      year: jikanResponse.year,
      studios: jikanResponse.studios,
      genres: jikanResponse.genres,
      themes: jikanResponse.themes,
      demographics: jikanResponse.demographics,
      images: jikanResponse.images,
      trailer: jikanResponse.trailer,
      approved: jikanResponse.approved,
      relations: jikanResponse.relations,
      streaming_platforms: jikanResponse.streaming,
      lastUpdated: new Date()
    };
  }

  async enrichAnimeWithJikanData(anime) {
    try {
      let jikanData = await this.searchAnime(anime.title);
      if (!jikanData && anime.cleanedTitle && anime.cleanedTitle !== anime.title) {
        logger.debug(`Retrying Jikan search with cleaned title: "${anime.cleanedTitle}"`);
        jikanData = await this.searchAnime(anime.cleanedTitle);
      }

      if (jikanData) {
        jikanData = await this.getAnimeById(jikanData.mal_id);
      } else {
        logger.warn(`No Jikan data after title and cleanedTitle attempts for "${anime.title}"`);
      }

      return {
        ...anime,
        jikan: this.formatJikanData(jikanData)
      };
    } catch (error) {
      logger.error(`Error enriching anime "${anime.title}": ${error.message}`);
      return anime;
    }
  }

  /**
   * Enriches anime with both basic Jikan data and seasons data
   * @param {Object} anime - The anime object to enrich
   * @param {boolean} includeSeasons - Whether to include seasons data
   * @returns {Promise<Object>} - The enriched anime object
   */
  async enrichAnimeWithJikanDataAndSeasons(anime, includeSeasons = true) {
    try {
      // First get the basic Jikan data
      const enrichedAnime = await this.enrichAnimeWithJikanData(anime);

      // If no Jikan data was found or seasons are not requested, return early
      if (!enrichedAnime.jikan?.mal_id || !includeSeasons) {
        return enrichedAnime;
      }

      // Get the seasons data
      const malId = enrichedAnime.jikan.mal_id;
      const dbSeasonNumber = anime.season ? parseInt(anime.season, 10) : null;

      logger.info(`Fetching Jikan seasons for anime "${anime.title}" (MAL ID: ${malId}, DB Season: ${dbSeasonNumber || 'N/A'})`);
      const seasonsData = await this.getAnimeSeasons(malId, dbSeasonNumber);

      if (seasonsData.success && seasonsData.seasons.length > 0) {
        logger.info(`Found ${seasonsData.seasons.length} Jikan seasons for anime "${anime.title}"`);
        return {
          ...enrichedAnime,
          jikanSeasons: seasonsData.seasons
        };
      } else {
        logger.warn(`No Jikan seasons found for anime "${anime.title}" (MAL ID: ${malId}): ${seasonsData.message || 'Unknown error'}`);
        return enrichedAnime;
      }
    } catch (error) {
      logger.error(`Error enriching anime "${anime.title}" with Jikan data and seasons: ${error.message}`);
      return anime;
    }
  }
}

module.exports = new JikanService();