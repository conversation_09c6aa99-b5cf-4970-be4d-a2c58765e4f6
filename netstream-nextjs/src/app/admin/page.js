"use client"
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import AdminPanel from '@/components/admin/AdminPanel'

export default function AdminPage() {
  const [isAdmin, setIsAdmin] = useState(false)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    async function checkAdmin() {
      const res = await fetch('/api/auth/[...auth]', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'me' })
      })
      const data = await res.json()
      if (data.user?.role === 'admin') {
        setIsAdmin(true)
      } else {
        router.replace('/login')
      }
      setLoading(false)
    }
    checkAdmin()
  }, [router])

  if (loading) return <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">Loading...</div>

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-white mb-8">Admin Panel</h1>
        <AdminPanel />
      </div>
    </div>
  )
}
