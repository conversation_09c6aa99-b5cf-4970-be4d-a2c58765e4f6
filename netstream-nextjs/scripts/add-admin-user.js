// Usage: node scripts/add-admin-user.js
const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

const MONGO_URI = process.env.MONGO_URI || require('dotenv').config({ path: '../.env' }) && process.env.MONGO_URI;
if (!MONGO_URI) {
  console.error('MONGO_URI not set in environment.');
  process.exit(1);
}

const email = '<EMAIL>';
const password = 'N@mery14';
const name = 'Admin';
const role = 'admin';
const username = 'admin';
const uniqueCode = uuidv4();

async function addAdminUser() {
  const client = new MongoClient(MONGO_URI);
  try {
    await client.connect();
    const db = client.db();
    const users = db.collection('users');
    const existing = await users.findOne({ email });
    if (existing) {
      console.log('Admin user already exists:', email);
      return;
    }
    const hash = await bcrypt.hash(password, 10);
    const user = {
      email,
      username,
      password: hash,
      name,
      role,
      uniqueCode,
      createdAt: new Date()
    };
    await users.insertOne(user);
    console.log('Admin user created:', email);
  } catch (err) {
    console.error('Error adding admin user:', err);
  } finally {
    await client.close();
  }
}

addAdminUser();
// Reminder: Run 'npm install uuid' if you haven't already 