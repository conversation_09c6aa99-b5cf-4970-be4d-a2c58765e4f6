'use client'
import { useRef, useState, useEffect } from 'react'
import CarouselItem from './CarouselItem'

export default function Carousel({ title, items, type }) {
  const scrollRef = useRef(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  // Debug logging
  console.log('🎠 Carousel render:', {
    title,
    type,
    itemsCount: items?.length || 0,
    firstItem: items?.[0],
    hasItems: !!(items && items.length > 0)
  })

  const scroll = (direction) => {
    const container = scrollRef.current
    if (!container) return

    const scrollAmount = 300
    const newScrollLeft = container.scrollLeft + (direction === 'left' ? -scrollAmount : scrollAmount)

    container.scrollTo({
      left: newScrollLeft,
      behavior: 'smooth'
    })
  }

  const handleScroll = () => {
    const container = scrollRef.current
    if (!container) return

    setCanScrollLeft(container.scrollLeft > 0)
    setCanScrollRight(
      container.scrollLeft < container.scrollWidth - container.clientWidth - 1
    )
  }

  useEffect(() => {
    const container = scrollRef.current
    if (container) {
      handleScroll()
      container.addEventListener('scroll', handleScroll)
      return () => container.removeEventListener('scroll', handleScroll)
    }
  }, [items])

  if (!items || items.length === 0) return null

  return (
    <div className="relative mb-12 carousel-container">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-3xl font-bold text-white flex items-center">
          <span className={`w-1 h-8 mr-4 rounded ${title === 'Trending Now' ? 'bg-gradient-to-b from-red-500 to-pink-500' : 'bg-blue-500'}`}></span>
          {title}
          {title === 'Trending Now' && (
            <i className="fas fa-fire ml-3 text-red-500 animate-pulse"></i>
          )}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={() => scroll('left')}
            disabled={!canScrollLeft}
            className={`p-3 rounded-full transition-all duration-300 ${
              canScrollLeft
                ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                : 'bg-gray-800 text-gray-500 cursor-not-allowed'
            }`}
          >
            <i className="fas fa-chevron-left"></i>
          </button>
          <button
            onClick={() => scroll('right')}
            disabled={!canScrollRight}
            className={`p-3 rounded-full transition-all duration-300 ${
              canScrollRight
                ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                : 'bg-gray-800 text-gray-500 cursor-not-allowed'
            }`}
          >
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>

      <div
        ref={scrollRef}
        className="flex space-x-6 overflow-x-auto scrollbar-hide pb-6"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none'
        }}
      >
        {items.map((item) => (
          <CarouselItem
            key={`${item.type || type}-${item.id}`}
            item={item}
            type={item.type || type}
          />
        ))}
      </div>
    </div>
  )
}
