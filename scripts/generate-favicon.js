/**
 * <PERSON><PERSON>t to generate favicon files for NetStream
 * 
 * This script creates a PNG favicon with the "NT" text
 * representing NetStream branding.
 */

const { createCanvas } = require('canvas');
const fs = require('fs');
const path = require('path');

// Create directory for favicon files if it doesn't exist
const faviconDir = path.join(__dirname, '..', 'public', 'favicon');
if (!fs.existsSync(faviconDir)) {
  fs.mkdirSync(faviconDir, { recursive: true });
}

// Define sizes for different favicon versions
const sizes = [16, 32, 48, 64, 128, 192, 256];

// NetStream brand colors from CSS
const primaryColor = '#00bcd4';
const primaryDark = '#0097a7';

// Generate favicon for each size
sizes.forEach(size => {
  // Create canvas with the specified size
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');
  
  // Create rounded rectangle background with gradient
  const radius = size * 0.125; // 1/8 of the size for rounded corners
  
  // Create gradient background
  const gradient = ctx.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, primaryColor);
  gradient.addColorStop(1, primaryDark);
  
  // Draw rounded rectangle
  ctx.beginPath();
  ctx.moveTo(size - radius, 0);
  ctx.quadraticCurveTo(size, 0, size, radius);
  ctx.lineTo(size, size - radius);
  ctx.quadraticCurveTo(size, size, size - radius, size);
  ctx.lineTo(radius, size);
  ctx.quadraticCurveTo(0, size, 0, size - radius);
  ctx.lineTo(0, radius);
  ctx.quadraticCurveTo(0, 0, radius, 0);
  ctx.closePath();
  
  ctx.fillStyle = gradient;
  ctx.fill();
  
  // Add "NT" text
  ctx.fillStyle = '#ffffff';
  ctx.font = `bold ${size * 0.5}px Arial, sans-serif`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('NT', size / 2, size / 2);
  
  // Save the image
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(path.join(faviconDir, `favicon-${size}x${size}.png`), buffer);
  
  // Save the main favicon.ico size (32x32)
  if (size === 32) {
    fs.writeFileSync(path.join(__dirname, '..', 'public', 'favicon.ico'), buffer);
    fs.writeFileSync(path.join(__dirname, '..', 'public', 'favicon.png'), buffer);
  }
});

// Create apple-touch-icon (192x192)
const appleIconSize = 192;
const appleCanvas = createCanvas(appleIconSize, appleIconSize);
const appleCtx = appleCanvas.getContext('2d');

// Create gradient background
const appleGradient = appleCtx.createLinearGradient(0, 0, appleIconSize, appleIconSize);
appleGradient.addColorStop(0, primaryColor);
appleGradient.addColorStop(1, primaryDark);

// Draw rounded rectangle
appleCtx.beginPath();
appleCtx.moveTo(appleIconSize, 0);
appleCtx.lineTo(0, 0);
appleCtx.lineTo(0, appleIconSize);
appleCtx.lineTo(appleIconSize, appleIconSize);
appleCtx.closePath();

appleCtx.fillStyle = appleGradient;
appleCtx.fill();

// Add "NT" text
appleCtx.fillStyle = '#ffffff';
appleCtx.font = `bold ${appleIconSize * 0.5}px Arial, sans-serif`;
appleCtx.textAlign = 'center';
appleCtx.textBaseline = 'middle';
appleCtx.fillText('NT', appleIconSize / 2, appleIconSize / 2);

// Save the apple touch icon
const appleBuffer = appleCanvas.toBuffer('image/png');
fs.writeFileSync(path.join(faviconDir, 'apple-touch-icon.png'), appleBuffer);

console.log('Favicon files generated successfully!');
