// NetStream_GraphQL/src/db/models/Anime.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const StreamingUrlSchema = new Schema({
  url: { type: String, required: true },
  provider: { type: String, default: 'unknown' },
  language: { type: String, enum: ['VF', 'VOSTFR', 'unknown'], default: 'unknown' },
  lastChecked: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true },
  sourceStreamUrl: { type: String, default: null },
  size: { type: String, default: null },   // Added
  type: { type: String, default: null },   // Added (HLS or MP4)
  method: { type: String, default: null } // Added
});

const EpisodeSchema = new Schema({
  episodeNumber: { type: String, required: true }, // Changed to String
  season: { type: String, default: '1' }, // Keep as string
  streamingUrls: [StreamingUrlSchema],
  language: { type: String, enum: ['VF', 'VOSTFR', 'unknown'], default: 'unknown' }
});

const MetadataSchema = new Schema({
  synopsis: { type: String, default: '' },
  actors: [String],
  year: { type: String, default: '' },
  genre: { type: String, default: '' },
  origin: { type: String, default: '' },
  creator: { type: String, default: '' },
  duration: { type: String, default: '' }
});

const TmdbSchema = new Schema({
  id: Number,
  title: String,
  overview: String,
  release_date: String,
  poster_path: String,
  vote_average: Number
});

const JikanSchema = new Schema({
  mal_id: { type: Number, index: true },
  title: {
    default: String,
    english: String,
    japanese: String,
    synonyms: [String]
  },
  type: String,
  source: String,
  episodes: Number,
  status: String,
  airing: Boolean,
  aired: {
    from: Date,
    to: Date,
    string: String
  },
  duration: String,
  rating: String,
  score: Number,
  scored_by: Number,
  rank: Number,
  popularity: Number,
  members: Number,
  favorites: Number,
  synopsis: String,
  background: String,
  season: String,
  year: Number,
  studios: [{
    mal_id: Number,
    name: String
  }],
  genres: [{
    mal_id: { type: Number },
    name: { type: String },
    type: { type: String }
  }],
  themes: [{
    mal_id: { type: Number },
    name: { type: String },
    type: { type: String }
  }],
  demographics: [{
    mal_id: { type: Number },
    name: { type: String },
    type: { type: String }
  }],
  images: {
    jpg: {
      image_url: String,
      small_image_url: String,
      large_image_url: String
    },
    webp: {
      image_url: String,
      small_image_url: String,
      large_image_url: String
    }
  },
  trailer: {
    youtube_id: String,
    url: String,
    embed_url: String
  },
  approved: Boolean,
  relations: [{
    relation: String,
    entry: [{
      mal_id: { type: Number },
      type: { type: String },
      name: { type: String },
      url: { type: String }
    }]
  }],
  streaming_platforms: [{
    name: String,
    url: String
  }],
  lastUpdated: { type: Date, default: Date.now }
});

// Define TmdbSeasonSchema directly to avoid circular dependency
const TmdbSeasonSchema = new Schema({
    air_date: String, // Or Date
    tmdb_season_id: { type: Number }, // TMDB's own ID for the season
    name: String, // e.g., "Season 1"
    overview: String,
    poster_path: String,
    season_number: Number,
    vote_average: Number, // TMDB's vote average for this specific season
    episodes: [{ // Optional: if you want to store TMDB's episode list for the season
        air_date: String,
        episode_number: Number,
        tmdb_episode_id: { type: Number },
        name: String,
        overview: String,
        still_path: String,
        vote_average: Number,
    }]
}, { _id: false }); // _id: false if you don't need a separate MongoDB ID for each season entry

// Define JikanSeasonSchema for storing Jikan seasons data
const JikanSeasonSchema = new Schema({
    mal_id: { type: Number },
    title: { type: String },
    title_english: { type: String },
    title_japanese: { type: String },
    season_number: { type: Number }, // Sequential number (1, 2, 3, etc.)
    episodes: { type: Number },
    aired: {
        from: { type: Date },
        to: { type: Date },
        string: { type: String }
    },
    images: {
        jpg: {
            image_url: { type: String },
            small_image_url: { type: String },
            large_image_url: { type: String }
        },
        webp: {
            image_url: { type: String },
            small_image_url: { type: String },
            large_image_url: { type: String }
        }
    },
    synopsis: { type: String },
    score: { type: Number },
    season: { type: String }, // Spring, Summer, Fall, Winter
    year: { type: Number },
    url: { type: String }
}, { _id: false });

const AnimeSchema = new Schema({
    title: { type: String, required: true },
    detailUrl: { type: String, required: true },
    detailUrlPath: { type: String, required: false },
    image: String,
    season: { type: String, default: '1' }, // Keep as string
    animeLanguage: { type: String, enum: ['VF', 'VOSTFR', 'unknown'], default: 'unknown' },
    episodes: [EpisodeSchema],
    streamingUrls: [StreamingUrlSchema], // For consistency
    tmdbSeasons: [TmdbSeasonSchema], // Add tmdbSeasons array
    jikanSeasons: [JikanSeasonSchema], // Add jikanSeasons array
    metadata: MetadataSchema,
    tmdb: TmdbSchema,
    jikan: JikanSchema,
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  }, {
    collection: 'animes',
    timestamps: true
});

AnimeSchema.index({ title: 'text', 'jikan.title.english': 'text', 'jikan.title.japanese': 'text' });

// Add pre-save hook to ensure updatedAt is set
AnimeSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    next();
});

module.exports = mongoose.model('Anime', AnimeSchema);