// src/scripts/standaloneScrape.js
// Standalone scraping script for Next.js admin panel

require('dotenv').config();
const mongoose = require('mongoose');
const { scrapeAll, SCRAPE_MODE } = require('../scrapers/services/scrapeService');
const logger = require('../utils/logger');
const { closeBrowser } = require('../utils/browserUtils');

// Get environment variables
const mode = process.env.SCRAPE_MODE || 'latest';
const type = process.env.SCRAPE_TYPE || 'all';
const jobId = process.env.JOB_ID || 'standalone';

// Page limits from environment variables
const pageLimits = {
  movies: parseInt(process.env.SCRAPE_PAGES_MOVIES) || 2,
  series: parseInt(process.env.SCRAPE_PAGES_SERIES) || 2,
  anime: parseInt(process.env.SCRAPE_PAGES_ANIME) || 2,
  livetv: parseInt(process.env.SCRAPE_PAGES_LIVETV) || 4
};

// Enrichment settings
const enableEnrichment = process.env.ENABLE_ENRICHMENT === 'true';
const enableGemini = process.env.ENABLE_GEMINI === 'true';

async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGO_URI;
    if (!mongoUri) {
      throw new Error('MONGO_URI environment variable is required');
    }
    
    await mongoose.connect(mongoUri, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.error('Failed to connect to MongoDB:', error.message);
    throw error;
  }
}

async function updateJobStatus(status, error = null) {
  try {
    const db = mongoose.connection.db;
    await db.collection('scraping_jobs').updateOne(
      { jobId },
      { 
        $set: { 
          status,
          ...(status === 'running' && { startedAt: new Date() }),
          ...(status === 'completed' && { completedAt: new Date() }),
          ...(status === 'failed' && { failedAt: new Date(), error: error?.message || error }),
          ...(status === 'stopped' && { stoppedAt: new Date() })
        }
      }
    );
  } catch (updateError) {
    logger.error('Failed to update job status:', updateError.message);
  }
}

async function checkIfJobStopped() {
  try {
    const db = mongoose.connection.db;
    const job = await db.collection('scraping_jobs').findOne({ jobId });
    return job?.status === 'stopped';
  } catch (error) {
    logger.error('Failed to check job status:', error.message);
    return false;
  }
}

async function runScraping() {
  logger.info(`Starting standalone scraping job ${jobId}`, {
    mode,
    type,
    pageLimits,
    enableEnrichment,
    enableGemini
  });

  try {
    // Connect to database
    await connectToDatabase();
    
    // Update job status to running
    await updateJobStatus('running');
    
    // Check if job was stopped before starting
    if (await checkIfJobStopped()) {
      logger.info(`Job ${jobId} was stopped before starting`);
      return;
    }

    // Set environment variables for the scraping service
    process.env.SCRAPE_MODE = mode;
    process.env.SCRAPE_TYPE = type;
    process.env.SCRAPE_PAGES_MOVIES = pageLimits.movies.toString();
    process.env.SCRAPE_PAGES_SERIES = pageLimits.series.toString();
    process.env.SCRAPE_PAGES_ANIME = pageLimits.anime.toString();
    process.env.SCRAPE_PAGES_LIVETV = pageLimits.livetv.toString();
    process.env.ENABLE_ENRICHMENT = enableEnrichment.toString();
    process.env.ENABLE_GEMINI = enableGemini.toString();

    // Run the scraping
    logger.info(`Executing scrapeAll with mode: ${mode}`);
    const result = await scrapeAll(mode);
    
    // Check if job was stopped during execution
    if (await checkIfJobStopped()) {
      logger.info(`Job ${jobId} was stopped during execution`);
      return;
    }

    logger.info(`Scraping completed successfully`, result);
    await updateJobStatus('completed');

  } catch (error) {
    logger.error(`Scraping job ${jobId} failed:`, error.message, { stack: error.stack });
    await updateJobStatus('failed', error);
    process.exit(1);
  } finally {
    // Always close browser and disconnect from database
    try {
      await closeBrowser();
      logger.info('Browser closed successfully');
    } catch (browserError) {
      logger.error('Error closing browser:', browserError.message);
    }

    try {
      if (mongoose.connection.readyState === 1) {
        await mongoose.disconnect();
        logger.info('MongoDB disconnected');
      }
    } catch (dbError) {
      logger.error('Error disconnecting from MongoDB:', dbError.message);
    }
  }
}

// Handle process signals
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully...');
  await updateJobStatus('stopped');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  await updateJobStatus('stopped');
  process.exit(0);
});

// Run the scraping if this file is executed directly
if (require.main === module) {
  runScraping().catch(error => {
    logger.error('Unhandled error in standalone scraping:', error);
    process.exit(1);
  });
}

module.exports = { runScraping }; 