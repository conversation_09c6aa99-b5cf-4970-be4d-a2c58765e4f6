# Frontend Dockerfile (Root Level) - UPDATED
# Builds the Next.js app from the 'netstream-nextjs' subdirectory

# Stage 1: Builder
# This stage installs dependencies and builds the application.
FROM node:18-alpine AS builder

WORKDIR /app

# Accept a dummy MON<PERSON>O_URI during the build phase to prevent build errors.
# This value is NOT used in the final running container.
ARG MONGO_URI="mongodb+srv://crypto:<EMAIL>/NetStream?retryWrites=true&w=majority"

ENV MONGO_URI=$MONGO_URI

# Set other build-time environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Copy package definitions from the subdirectory
COPY netstream-nextjs/package.json netstream-nextjs/package-lock.json* ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy the entire frontend source code into the build stage
COPY netstream-nextjs/ .

# Build the application
RUN npm run build

# Stage 2: Runner
# This stage creates the final, lean production image.
FROM node:18-alpine AS runner
WORKDIR /app

# Create a non-root user for security
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy only the necessary built files from the 'builder' stage
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

# Switch to the non-root user
USER nextjs

EXPOSE 3000

# Set environment variables for the running container.
# The PORT is automatically supplied by Render.
ENV NEXT_TELEMETRY_DISABLED 1

# This is the correct command to run the standalone server.
CMD ["node", "server.js"] 