// src/utils/urlNormalizer.js
const { WIFLIX_BASE, WITV_BASE } = require('../config/constants');
const logger = require('./logger');
const Config = require('../db/models/Config');

/**
 * Normalizes a URL to use the current WIFLIX_BASE domain
 * @param {string} url - The URL to normalize
 * @returns {string} - The normalized URL
 */
async function normalizeWiflixUrl(url) {
  if (!url || typeof url !== 'string') {
    return url;
  }

  try {
    // Check if this is a Wiflix URL
    if (!url.includes('wiflix') && !url.includes('flemmix')) {
      return url; // Not a Wiflix URL, return as is
    }

    // Get the latest WIFLIX_BASE from the database
    const wiflixBase = await Config.getValue('WIFLIX_BASE', WIFLIX_BASE);

    // Parse the URL to get its components
    const urlObj = new URL(url);

    // Check if the domain is different from the current WIFLIX_BASE
    if (urlObj.hostname !== wiflixBase) {
      // Log the domain change
      logger.info(`Normalizing URL domain from ${urlObj.hostname} to ${wiflixBase}`);

      // Create a new URL with the current WIFLIX_BASE
      const normalizedUrl = `https://${wiflixBase}${urlObj.pathname}${urlObj.search}${urlObj.hash}`;
      return normalizedUrl;
    }

    return url; // Already using the correct domain
  } catch (error) {
    logger.warn(`Failed to normalize URL: ${url}`, { error: error.message });
    return url; // Return the original URL in case of error
  }
}

/**
 * Extracts the path from a URL
 * @param {string} url - The URL to extract the path from
 * @returns {string} - The path component of the URL
 */
function extractPathFromUrl(url) {
  if (!url || typeof url !== 'string') {
    return '';
  }

  try {
    // Handle relative URLs
    if (url.startsWith('/')) {
      return url;
    }

    // Parse the URL to get its components
    const urlObj = new URL(url);
    return `${urlObj.pathname}${urlObj.search}${urlObj.hash}`;
  } catch (error) {
    logger.warn(`Failed to extract path from URL: ${url}`, { error: error.message });
    return '';
  }
}

/**
 * Normalizes a URL to use the current WITV_BASE domain
 * @param {string} url - The URL to normalize
 * @returns {string} - The normalized URL
 */
async function normalizeWitvUrl(url) {
  if (!url || typeof url !== 'string') {
    return url;
  }

  try {
    // Check if this is a WiTV URL
    if (!url.includes('witv')) {
      return url; // Not a WiTV URL, return as is
    }

    // Get the latest WITV_BASE from the database
    const witvBase = await Config.getValue('WITV_BASE', WITV_BASE);

    // Parse the URL to get its components
    const urlObj = new URL(url);

    // Check if the domain is different from the current WITV_BASE
    if (urlObj.hostname !== witvBase) {
      // Log the domain change
      logger.info(`Normalizing WiTV URL domain from ${urlObj.hostname} to ${witvBase}`);

      // Create a new URL with the current WITV_BASE
      const normalizedUrl = `https://${witvBase}${urlObj.pathname}${urlObj.search}${urlObj.hash}`;
      return normalizedUrl;
    }

    return url; // Already using the correct domain
  } catch (error) {
    logger.warn(`Failed to normalize WiTV URL: ${url}`, { error: error.message });
    return url; // Return the original URL in case of error
  }
}

module.exports = {
  normalizeWiflixUrl,
  normalizeWitvUrl,
  extractPathFromUrl
};
