/**
 * jikan_seasons_fetcher.js
 *
 * This module provides functionality to fetch anime seasons data from the Jikan API.
 * It handles rate limiting, retries, and recursive fetching of related seasons.
 *
 * Usage:
 * const jikanSeasonsFetcher = require('./jikan_seasons_fetcher');
 * const seasons = await jikanSeasonsFetcher.getAnimeSeasons(malId);
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const JIKAN_BASE_URL = 'https://api.jikan.moe/v4';
const API_REQUEST_DELAY_MS = 1000; // 1 second to respect Ji<PERSON>'s rate limit
const MAX_RETRIES = 3;
const RETRY_DELAY_MS = 2000; // Base delay for retries

// Utility function for logging
function log(message, verbose = false) {
  if (verbose) {
    console.log(`[Jikan Seasons] ${message}`);
  }

  // Also log to file
  const logFile = path.join(__dirname, 'jikan_seasons_fetcher.log');
  fs.appendFileSync(logFile, `${new Date().toISOString()} - ${message}\n`);
}

// Utility function to sleep
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Make a rate-limited request to the Jikan API
 * @param {string} endpoint - The API endpoint to call
 * @param {Object} params - Query parameters
 * @param {boolean} verbose - Whether to log verbose output
 * @returns {Promise<Object>} - The API response data
 */
async function makeJikanRequest(endpoint, params = {}, verbose = false) {
  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      // Respect rate limit
      log(`Waiting ${API_REQUEST_DELAY_MS}ms before Jikan API call...`, verbose);
      await sleep(API_REQUEST_DELAY_MS);

      const url = `${JIKAN_BASE_URL}${endpoint}`;
      log(`Sending request to Jikan: ${url}`, verbose);

      const response = await axios.get(url, { params, timeout: 10000 });
      log(`Jikan response received: Status ${response.status}`, verbose);

      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 429) {
        // Rate limit hit, exponential backoff
        const delay = RETRY_DELAY_MS * Math.pow(2, attempt - 1);
        log(`Rate limit hit, retrying in ${delay}ms (Attempt ${attempt}/${MAX_RETRIES})`, true);

        if (attempt === MAX_RETRIES) {
          log(`Exhausted retries: ${error.message}`, true);
          throw new Error(`Jikan API rate limit exceeded after ${MAX_RETRIES} attempts`);
        }

        await sleep(delay);
      } else {
        log(`Error making Jikan request: ${error.message}`, true);

        if (attempt < MAX_RETRIES) {
          log(`Retrying (Attempt ${attempt + 1}/${MAX_RETRIES})...`, true);
          await sleep(RETRY_DELAY_MS);
        } else {
          throw error;
        }
      }
    }
  }
}

/**
 * Get full anime details by MAL ID
 * @param {number} malId - MyAnimeList ID
 * @param {boolean} verbose - Whether to log verbose output
 * @returns {Promise<Object>} - Full anime details
 */
async function getAnimeById(malId, verbose = false) {
  try {
    log(`Fetching anime details for MAL ID: ${malId}`, verbose);
    const response = await makeJikanRequest(`/anime/${malId}/full`, {}, verbose);
    return response.data;
  } catch (error) {
    log(`Error fetching anime details for MAL ID ${malId}: ${error.message}`, true);
    return null;
  }
}

/**
 * Get relations for an anime by MAL ID
 * @param {number} malId - MyAnimeList ID
 * @param {boolean} verbose - Whether to log verbose output
 * @returns {Promise<Array>} - Array of related anime
 */
async function getAnimeRelations(malId, verbose = false) {
  try {
    log(`Fetching relations for MAL ID: ${malId}`, verbose);
    const response = await makeJikanRequest(`/anime/${malId}/relations`, {}, verbose);
    return response.data;
  } catch (error) {
    log(`Error fetching relations for MAL ID ${malId}: ${error.message}`, true);
    return { data: [] };
  }
}

/**
 * Recursively fetch all seasons related to an anime
 * @param {number} malId - MyAnimeList ID
 * @param {Set} processedIds - Set of already processed MAL IDs to avoid cycles
 * @param {boolean} verbose - Whether to log verbose output
 * @returns {Promise<Array>} - Array of seasons in chronological order
 */
async function fetchRelatedSeasons(malId, processedIds = new Set(), verbose = false) {
  // Avoid processing the same anime twice
  if (processedIds.has(malId)) {
    return [];
  }

  processedIds.add(malId);

  // Get the current anime's full data
  const animeData = await getAnimeById(malId, verbose);
  if (!animeData) {
    return [];
  }

  // Initialize the seasons array with the current anime
  const seasons = [animeData];

  // Get the relations for this anime
  const relationsResponse = await getAnimeRelations(malId, verbose);
  const relations = relationsResponse.data || [];

  // Find prequels and sequels
  const prequels = [];
  const sequels = [];

  for (const relation of relations) {
    if (relation.relation === 'Prequel') {
      for (const entry of relation.entry) {
        if (entry.type === 'anime') {
          prequels.push(parseInt(entry.mal_id));
        }
      }
    } else if (relation.relation === 'Sequel') {
      for (const entry of relation.entry) {
        if (entry.type === 'anime') {
          sequels.push(parseInt(entry.mal_id));
        }
      }
    }
  }

  // Log what we found
  if (verbose) {
    log(`Found ${prequels.length} prequels and ${sequels.length} sequels for MAL ID ${malId}`, true);
    if (prequels.length > 0) log(`Prequels: ${prequels.join(', ')}`, true);
    if (sequels.length > 0) log(`Sequels: ${sequels.join(', ')}`, true);
  }

  // Recursively fetch prequels (going backward in time)
  for (const prequelId of prequels) {
    const prequelSeasons = await fetchRelatedSeasons(prequelId, processedIds, verbose);
    // Add prequels to the beginning of the array
    seasons.unshift(...prequelSeasons);
  }

  // Recursively fetch sequels (going forward in time)
  for (const sequelId of sequels) {
    const sequelSeasons = await fetchRelatedSeasons(sequelId, processedIds, verbose);
    // Add sequels to the end of the array
    seasons.push(...sequelSeasons);
  }

  return seasons;
}

/**
 * Get all seasons for an anime, including prequels and sequels
 * @param {number} malId - MyAnimeList ID
 * @param {boolean} verbose - Whether to log verbose output
 * @param {number} requestedSeasonNumber - Optional specific season number requested
 * @returns {Promise<Object>} - Object containing seasons data and metadata
 */
async function getAnimeSeasons(malId, verbose = false, requestedSeasonNumber = null) {
  try {
    log(`Starting seasons fetch for MAL ID: ${malId}`, verbose);

    // Fetch all related seasons
    const allSeasons = await fetchRelatedSeasons(malId, new Set(), verbose);

    // Remove duplicates based on mal_id
    const uniqueSeasons = [];
    const seenIds = new Set();

    for (const season of allSeasons) {
      if (!seenIds.has(season.mal_id)) {
        seenIds.add(season.mal_id);
        uniqueSeasons.push(season);
      }
    }

    // Sort seasons chronologically based on aired.from date
    uniqueSeasons.sort((a, b) => {
      const dateA = a.aired && a.aired.from ? new Date(a.aired.from) : new Date(0);
      const dateB = b.aired && b.aired.from ? new Date(b.aired.from) : new Date(0);
      return dateA - dateB;
    });

    log(`Found ${uniqueSeasons.length} unique seasons for MAL ID ${malId}`, verbose);

    // Format the seasons data
    let formattedSeasons = formatJikanSeasons(uniqueSeasons);

    // Check if a specific season number was requested but not found
    if (requestedSeasonNumber &&
        requestedSeasonNumber > formattedSeasons.length &&
        formattedSeasons.length > 0) {

      log(`WARNING: Requested season ${requestedSeasonNumber} not found in MyAnimeList. Only ${formattedSeasons.length} season(s) exist for this anime.`, true);
      log(`Using actual seasons data from MyAnimeList without creating virtual seasons.`, true);

      // Add a flag to the result to indicate the season mismatch
      formattedSeasons.forEach(season => {
        season.season_mismatch = true;
      });
    }

    return {
      success: true,
      originalMalId: malId,
      seasonCount: formattedSeasons.length,
      rawSeasons: uniqueSeasons,
      seasons: formattedSeasons
    };
  } catch (error) {
    log(`Error fetching seasons for MAL ID ${malId}: ${error.message}`, true);
    return {
      success: false,
      message: `Error: ${error.message}`,
      originalMalId: malId
    };
  }
}

/**
 * Format raw Jikan seasons data into a more usable structure
 * @param {Array} seasonsData - Raw seasons data from Jikan API
 * @returns {Array} - Formatted seasons data
 */
function formatJikanSeasons(seasonsData) {
  if (!seasonsData || !Array.isArray(seasonsData) || seasonsData.length === 0) {
    return [];
  }

  return seasonsData.map((season, index) => {
    return {
      mal_id: season.mal_id,
      title: season.title,
      title_english: season.title_english,
      title_japanese: season.title_japanese,
      season_number: index + 1, // Assign a sequential season number
      episodes: season.episodes,
      aired: season.aired,
      images: season.images,
      synopsis: season.synopsis,
      score: season.score,
      season: season.season, // Spring, Summer, Fall, Winter
      year: season.year,
      url: season.url
    };
  });
}

module.exports = {
  getAnimeSeasons,
  getAnimeById,
  getAnimeRelations,
  formatJikanSeasons
};
