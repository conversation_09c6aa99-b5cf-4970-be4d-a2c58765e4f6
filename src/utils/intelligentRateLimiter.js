/**
 * Intelligent Rate Limiter with Adaptive Throttling
 * Optimizes API calls based on response times and error rates
 */

const logger = require('./logger');

class IntelligentRateLimiter {
  constructor(options = {}) {
    this.name = options.name || 'default';
    this.baseRateLimit = options.baseRateLimit || 1000; // Base delay in ms
    this.maxRateLimit = options.maxRateLimit || 5000; // Max delay in ms
    this.minRateLimit = options.minRateLimit || 100; // Min delay in ms
    this.maxConcurrent = options.maxConcurrent || 3; // Max concurrent requests
    
    // Adaptive parameters
    this.currentDelay = this.baseRateLimit;
    this.activeCalls = 0;
    this.queue = [];
    this.lastCallTime = 0;
    
    // Performance tracking
    this.stats = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      rateLimitHits: 0,
      avgResponseTime: 0,
      lastAdjustment: Date.now()
    };
    
    // Response time tracking (rolling window)
    this.responseTimes = [];
    this.maxResponseTimeHistory = 50;
    
    // Error rate tracking
    this.recentErrors = [];
    this.errorWindowMs = 60000; // 1 minute window
  }

  /**
   * Execute a function with intelligent rate limiting
   */
  async execute(fn, context = {}) {
    return new Promise((resolve, reject) => {
      this.queue.push({ fn, context, resolve, reject, queuedAt: Date.now() });
      this.processQueue();
    });
  }

  /**
   * Process the request queue
   */
  async processQueue() {
    if (this.queue.length === 0 || this.activeCalls >= this.maxConcurrent) {
      return;
    }

    const { fn, context, resolve, reject, queuedAt } = this.queue.shift();
    this.activeCalls++;

    // Calculate delay based on current rate limit
    const now = Date.now();
    const timeSinceLastCall = now - this.lastCallTime;
    const delay = Math.max(0, this.currentDelay - timeSinceLastCall);

    if (delay > 0) {
      await this.sleep(delay);
    }

    this.lastCallTime = Date.now();
    const startTime = Date.now();

    try {
      const result = await fn();
      const responseTime = Date.now() - startTime;
      
      this.recordSuccess(responseTime);
      this.activeCalls--;
      resolve(result);
      
      // Continue processing queue
      setImmediate(() => this.processQueue());
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordError(error, responseTime);
      this.activeCalls--;
      reject(error);
      
      // Continue processing queue after error handling
      setImmediate(() => this.processQueue());
    }
  }

  /**
   * Record successful API call
   */
  recordSuccess(responseTime) {
    this.stats.totalCalls++;
    this.stats.successfulCalls++;
    
    // Update response time tracking
    this.responseTimes.push(responseTime);
    if (this.responseTimes.length > this.maxResponseTimeHistory) {
      this.responseTimes.shift();
    }
    
    // Calculate average response time
    this.stats.avgResponseTime = this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length;
    
    // Adaptive rate adjustment - decrease delay if performing well
    if (this.shouldDecreaseDelay()) {
      this.adjustDelay(-0.1); // Decrease by 10%
    }
  }

  /**
   * Record failed API call
   */
  recordError(error, responseTime) {
    this.stats.totalCalls++;
    this.stats.failedCalls++;
    
    // Track recent errors
    this.recentErrors.push({
      timestamp: Date.now(),
      error: error.message,
      responseTime
    });
    
    // Clean old errors
    this.cleanOldErrors();
    
    // Check if it's a rate limit error
    if (this.isRateLimitError(error)) {
      this.stats.rateLimitHits++;
      this.adjustDelay(0.5); // Increase delay by 50%
      logger.warn(`${this.name}: Rate limit hit, increasing delay to ${this.currentDelay}ms`);
    } else if (this.shouldIncreaseDelay()) {
      this.adjustDelay(0.2); // Increase delay by 20%
    }
  }

  /**
   * Check if error is rate limit related
   */
  isRateLimitError(error) {
    const rateLimitIndicators = [
      'rate limit',
      'too many requests',
      '429',
      'quota exceeded',
      'throttled'
    ];
    
    const errorMessage = error.message.toLowerCase();
    return rateLimitIndicators.some(indicator => errorMessage.includes(indicator));
  }

  /**
   * Determine if delay should be decreased
   */
  shouldDecreaseDelay() {
    // Only decrease if we have enough data and low error rate
    if (this.responseTimes.length < 10) return false;
    
    const recentErrorRate = this.getRecentErrorRate();
    const avgResponseTime = this.stats.avgResponseTime;
    
    // Decrease delay if error rate is low and response times are good
    return recentErrorRate < 0.05 && avgResponseTime < 2000; // Less than 5% error rate and under 2s response
  }

  /**
   * Determine if delay should be increased
   */
  shouldIncreaseDelay() {
    const recentErrorRate = this.getRecentErrorRate();
    const avgResponseTime = this.stats.avgResponseTime;
    
    // Increase delay if error rate is high or response times are slow
    return recentErrorRate > 0.15 || avgResponseTime > 5000; // More than 15% error rate or over 5s response
  }

  /**
   * Get recent error rate
   */
  getRecentErrorRate() {
    if (this.recentErrors.length === 0) return 0;
    
    const recentCalls = Math.max(this.stats.totalCalls - (this.stats.totalCalls - this.recentErrors.length), 1);
    return this.recentErrors.length / recentCalls;
  }

  /**
   * Adjust current delay
   */
  adjustDelay(factor) {
    const oldDelay = this.currentDelay;
    
    if (factor > 0) {
      // Increase delay
      this.currentDelay = Math.min(this.maxRateLimit, this.currentDelay * (1 + factor));
    } else {
      // Decrease delay
      this.currentDelay = Math.max(this.minRateLimit, this.currentDelay * (1 + factor));
    }
    
    this.stats.lastAdjustment = Date.now();
    
    if (Math.abs(this.currentDelay - oldDelay) > 50) { // Only log significant changes
      logger.info(`${this.name}: Adjusted rate limit from ${oldDelay}ms to ${this.currentDelay}ms`);
    }
  }

  /**
   * Clean old errors from tracking
   */
  cleanOldErrors() {
    const cutoff = Date.now() - this.errorWindowMs;
    this.recentErrors = this.recentErrors.filter(error => error.timestamp > cutoff);
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current statistics
   */
  getStats() {
    const errorRate = this.stats.totalCalls > 0 
      ? (this.stats.failedCalls / this.stats.totalCalls * 100).toFixed(2)
      : 0;
    
    return {
      name: this.name,
      currentDelay: this.currentDelay,
      activeCalls: this.activeCalls,
      queueLength: this.queue.length,
      errorRate: `${errorRate}%`,
      recentErrors: this.recentErrors.length,
      ...this.stats
    };
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.stats = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      rateLimitHits: 0,
      avgResponseTime: 0,
      lastAdjustment: Date.now()
    };
    this.responseTimes = [];
    this.recentErrors = [];
  }

  /**
   * Force delay adjustment (for manual tuning)
   */
  setDelay(newDelay) {
    this.currentDelay = Math.max(this.minRateLimit, Math.min(this.maxRateLimit, newDelay));
    logger.info(`${this.name}: Manually set delay to ${this.currentDelay}ms`);
  }
}

// Create rate limiters for different APIs
const rateLimiters = {
  gemini: new IntelligentRateLimiter({
    name: 'Gemini',
    baseRateLimit: 2000, // 30 req/min = 2000ms
    maxRateLimit: 10000,
    minRateLimit: 500,
    maxConcurrent: 2
  }),
  
  tmdb: new IntelligentRateLimiter({
    name: 'TMDB',
    baseRateLimit: 1500, // 40 req/min = 1500ms
    maxRateLimit: 5000,
    minRateLimit: 250,
    maxConcurrent: 3
  }),
  
  jikan: new IntelligentRateLimiter({
    name: 'Jikan',
    baseRateLimit: 1000, // 60 req/min = 1000ms
    maxRateLimit: 3000,
    minRateLimit: 200,
    maxConcurrent: 2
  })
};

module.exports = {
  IntelligentRateLimiter,
  rateLimiters
};
