#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to delete all LiveTV data from the database
 * 
 * Run with: node scripts/cleanLiveTV.js [options]
 * 
 * Options:
 *   --dry-run    Show what would be deleted without actually deleting
 *   --count      Only show count of documents
 *   --backup     Create a JSON backup before deleting.
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env file
const result = dotenv.config();
if (result.error) {
  console.error('Error loading .env file:', result.error);
  // Continue anyway, as we might have environment variables set another way
}

// Import the LiveTV model
const LiveTV = require('../src/db/models/LiveTV');
const logger = require('../src/utils/logger');

// Process command line arguments
const argv = process.argv.slice(2);
const options = {
  dryRun: argv.includes('--dry-run'),
  countOnly: argv.includes('--count'),
  backup: argv.includes('--backup'),
  confirm: argv.includes('--confirm')
};

// Get MongoDB connection URI from environment variables
// We try multiple possible environment variable names
const MONGODB_URI = process.env.MONGODB_URI || 
                   process.env.MONGO_URI || 
                   process.env.DB_URI || 
                   process.env.DATABASE_URI || 
                   'mongodb://localhost:27017/netstream';

logger.info(`Using MongoDB connection string: ${MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);

async function main() {
  try {
    logger.info('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logger.info('Connected to MongoDB');

    // Get count of documents
    const count = await LiveTV.countDocuments();
    logger.info(`Found ${count} LiveTV documents in the database`);

    if (count === 0) {
      logger.info('No LiveTV documents to delete. Exiting.');
      process.exit(0);
    }

    if (options.countOnly) {
      process.exit(0);
    }

    // Create backup if requested
    if (options.backup) {
      const backupDir = path.join(__dirname, '../backups');
      
      // Create backups directory if it doesn't exist
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }
      
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const backupFile = path.join(backupDir, `livetv_backup_${timestamp}.json`);
      
      logger.info('Creating backup of LiveTV data...');
      
      // Fetch all documents with lean() for better performance
      const allDocuments = await LiveTV.find().lean();
      
      // Write to file
      fs.writeFileSync(
        backupFile, 
        JSON.stringify(allDocuments, null, 2)
      );
      
      logger.info(`Backup created at: ${backupFile}`);
    }

    if (options.dryRun) {
      logger.info('DRY RUN: Would delete all LiveTV documents');
      logger.info('To actually delete, run without --dry-run and with --confirm');
      process.exit(0);
    }

    // Require explicit confirmation for deletion
    if (!options.confirm) {
      logger.warn('SAFETY CHECK: This will delete ALL LiveTV documents!');
      logger.warn('To confirm deletion, run again with --confirm flag');
      process.exit(1);
    }

    // Delete all documents
    logger.info('Deleting all LiveTV documents...');
    const result = await LiveTV.deleteMany({});
    
    logger.info(`Successfully deleted ${result.deletedCount} LiveTV documents`);
    
  } catch (error) {
    logger.error(`Error: ${error.message}`, { stack: error.stack });
    process.exit(1);
  } finally {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      logger.info('Disconnected from MongoDB');
    }
  }
}

main(); 