'use client'
import { createContext, useContext, useState, useRef } from 'react'

const PlayerContext = createContext()

export function PlayerProvider({ children }) {
  const [isPlayerOpen, setIsPlayerOpen] = useState(false)
  const [currentMedia, setCurrentMedia] = useState(null)
  const [playerState, setPlayerState] = useState({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    isFullscreen: false
  })
  const playerRef = useRef(null)

  const openPlayer = (media) => {
    setCurrentMedia(media)
    setIsPlayerOpen(true)
  }

  const closePlayer = () => {
    setIsPlayerOpen(false)
    setCurrentMedia(null)
    setPlayerState({
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      volume: 1,
      isMuted: false,
      isFullscreen: false
    })
  }

  const updatePlayerState = (newState) => {
    setPlayerState(prevState => ({
      ...prevState,
      ...newState
    }))
  }

  return (
    <PlayerContext.Provider
      value={{
        isPlayerOpen,
        currentMedia,
        playerState,
        playerRef,
        openPlayer,
        closePlayer,
        updatePlayerState
      }}
    >
      {children}
    </PlayerContext.Provider>
  )
}

export function usePlayer() {
  const context = useContext(PlayerContext)
  if (!context) {
    throw new Error('usePlayer must be used within a PlayerProvider')
  }
  return context
}
