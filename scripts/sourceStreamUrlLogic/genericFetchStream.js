const fs = require('fs');

/**
 * Fetches direct video URL from generic providers by searching embed page HTML for common video URL patterns.
 */
async function fetchGenericStream(inputUrl, config, cookies, debug = false, customLogStep) { // **Accept customLogStep**
    const html = cookies.eResponse.data;
    const sourceMatch = html.match(/<source[^>]+src=["']([^"']+)["']/i) ||
                       html.match(/file:\s*["']([^"']+)["']/i) ||
                       html.match(/"hls":\s*"([^"]+)"/) ||
                       html.match(/"m3u8":\s*"([^"]+)"/) ||
                       html.match(/"mp4":\s*"([^"]+)"/) ||
                       html.match(/['"]?src["']?:\s*["']([^"']+\.(mp4|m3u8))["']/i);
    if (!sourceMatch) {
        if (debug) {
            const debugFile = `generic_debug_${Date.now()}.html`;
            fs.writeFileSync(debugFile, html);
            customLogStep(`[Generic] Debug HTML saved to: ${debugFile}`); // Use customLogStep
        }
        throw new Error(`No direct source found in embed page${debug ? ` - check ${debugFile}` : ''}`);
    }
    const directUrl = sourceMatch[1];
    customLogStep(`[Generic] Extracted direct source URL: ${directUrl}`); // Use customLogStep
    return directUrl;
}

module.exports = fetchGenericStream;