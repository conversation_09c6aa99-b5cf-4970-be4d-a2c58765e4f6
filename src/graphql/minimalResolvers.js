// File: src/graphql/minimalResolvers.js
// Minimal resolvers to test schema compatibility

const { ObjectId } = require('mongodb');

const minimalResolvers = {
  Query: {
    // Basic item resolver
    item: async (parent, args, context, info) => {
      const { id, type } = args;
      const { db } = context;

      if (!ObjectId.isValid(id)) {
        throw new Error('Invalid ID format');
      }

      const collectionMap = {
        MOVIE: 'movies',
        SERIES: 'series',
        ANIME: 'animes',
        LIVETV: 'livetv'
      };

      const collection = collectionMap[type];
      if (!collection) {
        throw new Error('Unsupported item type');
      }

      const item = await db.collection(collection).findOne({
        _id: new ObjectId(id)
      });

      if (!item) {
        throw new Error('Item not found');
      }

      return {
        ...item,
        id: item._id.toString(),
        __typename: type === 'MOVIE' ? 'Movie' : 
                   type === 'SERIES' ? 'Series' :
                   type === 'ANIME' ? 'Anime' : 'LiveTV'
      };
    },

    // Basic search resolver
    search: async (parent, args, context, info) => {
      const { query, page = 1, limit = 20 } = args;
      const { db } = context;

      const skip = (page - 1) * limit;
      const regexQuery = new RegExp(query, 'i');
      const searchQuery = { title: regexQuery };

      try {
        const [movies, series, animes, livetv] = await Promise.all([
          db.collection('movies').find(searchQuery).skip(skip).limit(limit).toArray(),
          db.collection('series').find(searchQuery).skip(skip).limit(limit).toArray(),
          db.collection('animes').find(searchQuery).skip(skip).limit(limit).toArray(),
          db.collection('livetv').find(searchQuery).skip(skip).limit(limit).toArray()
        ]);

        const items = [
          ...movies.map(m => ({ ...m, id: m._id.toString(), __typename: 'Movie' })),
          ...series.map(s => ({ ...s, id: s._id.toString(), __typename: 'Series' })),
          ...animes.map(a => ({ ...a, id: a._id.toString(), __typename: 'Anime' })),
          ...livetv.map(l => ({ ...l, id: l._id.toString(), __typename: 'LiveTV' }))
        ];

        return { items };
      } catch (error) {
        throw new Error('Search failed');
      }
    },

    // Basic movies resolver
    movies: async (parent, args, context, info) => {
      const { sort, page = 1, limit = 20 } = args;
      const { db } = context;

      const skip = (page - 1) * limit;
      const sortOption = { updatedAt: -1 };

      const movies = await db.collection('movies')
        .find({})
        .sort(sortOption)
        .skip(skip)
        .limit(limit)
        .toArray();

      return movies.map(movie => ({
        ...movie,
        id: movie._id.toString()
      }));
    },

    // Basic series resolver
    series: async (parent, args, context, info) => {
      const { sort, page = 1, limit = 20 } = args;
      const { db } = context;

      const skip = (page - 1) * limit;
      const sortOption = { updatedAt: -1 };

      const series = await db.collection('series')
        .find({})
        .sort(sortOption)
        .skip(skip)
        .limit(limit)
        .toArray();

      return series.map(item => ({
        ...item,
        id: item._id.toString()
      }));
    },

    // Basic anime resolver
    anime: async (parent, args, context, info) => {
      const { sort, page = 1, limit = 20 } = args;
      const { db } = context;

      const skip = (page - 1) * limit;
      const sortOption = { updatedAt: -1 };

      const animes = await db.collection('animes')
        .find({})
        .sort(sortOption)
        .skip(skip)
        .limit(limit)
        .toArray();

      return animes.map(item => ({
        ...item,
        id: item._id.toString()
      }));
    },

    // Basic liveTV resolver
    liveTV: async (parent, args, context, info) => {
      const { page = 1, limit = 20 } = args;
      const { db } = context;

      const skip = (page - 1) * limit;

      const channels = await db.collection('livetv')
        .find({})
        .sort({ title: 1 })
        .skip(skip)
        .limit(limit)
        .toArray();

      return channels.map(channel => ({
        ...channel,
        id: channel._id.toString(),
        __typename: 'LiveTV'
      }));
    },

    // Stub resolvers for other queries
    animes: async () => [],
    stream: async () => null,
    play: async () => ({ url: '' }),
    config: async () => ({
      tmdbApiKey: null,
      wiflixBase: 'wiflix-max.cam',
      frenchAnimeBase: 'french-anime.com',
      witvBase: 'witv.skin'
    }),
    duplicateDetailUrlPaths: async () => [],
    validateAdminToken: async () => ({ isValid: false }),
    latestMovies: async (parent, args, context) => {
      const movies = await context.db.collection('movies').find({}).limit(20).toArray();
      return movies.map(movie => ({ ...movie, id: movie._id.toString() }));
    },
    ancienMovies: async (parent, args, context) => {
      const movies = await context.db.collection('movies').find({}).limit(20).toArray();
      return movies.map(movie => ({ ...movie, id: movie._id.toString() }));
    },
    latestSeries: async (parent, args, context) => {
      const series = await context.db.collection('series').find({}).limit(20).toArray();
      return series.map(item => ({ ...item, id: item._id.toString() }));
    },
    latestAnime: async (parent, args, context) => {
      const animes = await context.db.collection('animes').find({}).limit(20).toArray();
      return animes.map(item => ({ ...item, id: item._id.toString() }));
    },
    animeMovies: async (parent, args, context) => {
      const animes = await context.db.collection('animes').find({}).limit(20).toArray();
      return animes.map(item => ({ ...item, id: item._id.toString() }));
    },
    moviesByGenre: async () => [],
    seriesByGenre: async () => [],
    animeByGenre: async () => [],
    availableGenres: async () => ({ movies: [], series: [], anime: [] }),
    relatedSeasons: async () => [],
    databaseStats: async (parent, args, context) => {
      const { db } = context;
      const [movieCount, seriesCount, animeCount, livetvCount] = await Promise.all([
        db.collection('movies').countDocuments(),
        db.collection('series').countDocuments(),
        db.collection('animes').countDocuments(),
        db.collection('livetv').countDocuments()
      ]);

      return {
        movies: movieCount,
        series: seriesCount,
        anime: animeCount,
        livetv: livetvCount,
        totalItems: movieCount + seriesCount + animeCount + livetvCount
      };
    },
    contentOverview: async () => ({ recentlyAdded: 0, trending: 0, mostWatched: 0, totalViews: 0 }),
    displaySettings: async () => ({ gridItemsEnabled: true })
  },

  Mutation: {
    adminLogin: async () => ({ success: false, message: 'Not implemented' }),
    deleteItem: async () => ({ success: false, message: 'Not implemented' }),
    scrapeItem: async () => ({ success: false, message: 'Not implemented' }),
    updateBaseUrl: async () => ({ success: false, message: 'Not implemented' }),
    scrapeUrlManually: async () => ({ success: false, message: 'Not implemented' }),
    updateDisplaySettings: async () => ({ success: false, message: 'Not implemented' })
  },

  // Interface resolver
  Item: {
    __resolveType(obj, context, info) {
      if (obj.__typename) {
        return obj.__typename;
      }
      
      // Fallback type resolution
      if (obj.animeLanguage || obj.jikan) {
        return 'Anime';
      }
      if (obj.episodes && obj.episodes.length > 0) {
        return 'Series';
      }
      if (obj.streamingUrls && obj.streamingUrls.length > 0) {
        if (obj.cleanedTitle && !obj.season) {
          return 'Movie';
        }
        return 'LiveTV';
      }
      
      return 'Movie';
    }
  }
};

module.exports = minimalResolvers;
