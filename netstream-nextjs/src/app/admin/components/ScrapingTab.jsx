'use client'

import { useState, useEffect } from 'react'
import styles from './admin.module.css'

export default function ScrapingTab() {
  const [scrapingStatus, setScrapingStatus] = useState('idle')
  const [scrapingProgress, setScrapingProgress] = useState(0)
  const [scrapingStats, setScrapingStats] = useState(null)
  const [latestJob, setLatestJob] = useState(null)
  const [logs, setLogs] = useState([])
  const [autoScroll, setAutoScroll] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  // Form state
  const [scrapingMode, setScrapingMode] = useState('latest')
  const [scrapingType, setScrapingType] = useState('all')
  
  // Separate page configurations for each media type
  const [pageConfig, setPageConfig] = useState({
    movies: 2,
    series: 2,
    anime: 2,
    livetv: 4
  })
  
  const [enableEnrichment, setEnableEnrichment] = useState(true)
  const [enableGemini, setEnableGemini] = useState(true)

  useEffect(() => {
    loadScrapingData()
    loadScrapingConfig()
    loadLatestJob()
    
    // Set up polling to check scraping status every 5 seconds
    const statusInterval = setInterval(() => {
      if (scrapingStatus === 'running') {
        checkScrapingStatus()
      }
    }, 5000)

    return () => {
      clearInterval(statusInterval)
      if (window.scrapingProgressInterval) {
        clearInterval(window.scrapingProgressInterval)
      }
    }
  }, [scrapingStatus])

  const loadScrapingData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/scrape/status')
      if (!response.ok) throw new Error(`Scraping status API returned ${response.status}`)
      const data = await response.json()
      setScrapingStats(data.data)
      
      // Check if there's a currently running job
      if (data.data.currentJob) {
        setScrapingStatus('running')
        setScrapingProgress(0) // Will be updated by polling
        addLog('info', `Found running scraping job: ${data.data.currentJob.jobId}`)
        
        // Start progress simulation for running job
        simulateProgress()
      }
    } catch (err) {
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const checkScrapingStatus = async () => {
    try {
      const response = await fetch('/api/scrape/status')
      if (!response.ok) return
      
      const data = await response.json()
      if (data.success && data.data) {
        // Update statistics
        setScrapingStats(data.data)
        
        // Check if job status changed
        if (data.data.currentJob) {
          if (scrapingStatus !== 'running') {
            setScrapingStatus('running')
            addLog('info', `Scraping job resumed: ${data.data.currentJob.jobId}`)
          }
        } else {
          // No running job found, check if we need to update status
          if (scrapingStatus === 'running') {
            // Job might have completed or failed
            const latestJob = await fetch('/api/scrape/latest-job')
            if (latestJob.ok) {
              const jobData = await latestJob.json()
              if (jobData.success && jobData.data) {
                const status = jobData.data.status
                setScrapingStatus(status)
                if (status === 'completed') {
                  setScrapingProgress(100)
                  addLog('success', 'Scraping job completed')
                } else if (status === 'failed') {
                  addLog('error', `Scraping job failed: ${jobData.data.error || 'Unknown error'}`)
                } else if (status === 'stopped') {
                  addLog('warning', 'Scraping job was stopped')
                }
                
                // Clear progress simulation
                if (window.scrapingProgressInterval) {
                  clearInterval(window.scrapingProgressInterval)
                  window.scrapingProgressInterval = null
                }
              }
            }
          }
        }
      }
    } catch (err) {
      console.warn('Failed to check scraping status:', err.message)
    }
  }

  const loadScrapingConfig = async () => {
    try {
      const response = await fetch('/api/config')
      if (!response.ok) throw new Error(`Config API returned ${response.status}`)
      const result = await response.json()
      if (result.success && result.data.scraping) {
        setPageConfig(result.data.scraping.pages || {
          movies: 2,
          series: 2,
          anime: 2,
          livetv: 4
        })
        setEnableEnrichment(result.data.scraping.enrichment !== false)
        setEnableGemini(result.data.scraping.gemini !== false)
      }
    } catch (err) {
      console.warn('Failed to load scraping config:', err.message)
      // Use defaults if config loading fails
    }
  }

  const loadLatestJob = async () => {
    try {
      const response = await fetch('/api/scrape/latest-job')
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setLatestJob(result.data)
        }
      }
    } catch (err) {
      console.warn('Failed to load latest job:', err.message)
    }
  }

  const startScraping = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      // Update UI to show scraping started
      setScrapingStatus('running')
      setScrapingProgress(0)
      addLog('info', 'Starting scraping operation...')

      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: scrapingMode,
          type: scrapingType,
          pages: pageConfig, // Send the page config object
          enrichment: enableEnrichment,
          gemini: enableGemini
        })
      })

      if (!response.ok) {
        throw new Error(`Scraping API returned ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.success) {
        addLog('success', result.message || 'Scraping started successfully')
        setScrapingStatus('running')
        
        // Simulate progress updates
        simulateProgress()
      } else {
        throw new Error(result.message || 'Failed to start scraping')
      }

    } catch (err) {
      setError(err.message)
      addLog('error', `Failed to start scraping: ${err.message}`)
      setScrapingStatus('error')
    } finally {
      setIsLoading(false)
    }
  }

  const stopScraping = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      addLog('info', 'Stopping scraping operation...')

      const response = await fetch('/api/scrape/stop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Stop scraping API returned ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.success) {
        addLog('success', result.message || 'Scraping stopped successfully')
        setScrapingStatus('stopped')
        setScrapingProgress(0)
        
        // Clear any progress simulation
        if (window.scrapingProgressInterval) {
          clearInterval(window.scrapingProgressInterval)
          window.scrapingProgressInterval = null
        }
      } else {
        throw new Error(result.message || 'Failed to stop scraping')
      }

    } catch (err) {
      setError(err.message)
      addLog('error', `Failed to stop scraping: ${err.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const simulateProgress = () => {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 10
      if (progress >= 100) {
        progress = 100
        clearInterval(interval)
        setScrapingStatus('completed')
        setScrapingProgress(100)
        addLog('success', 'Scraping completed successfully')
      } else {
        setScrapingProgress(Math.round(progress))
        addLog('info', `Scraping progress: ${Math.round(progress)}%`)
      }
    }, 2000)
    
    // Store interval reference for stopping
    window.scrapingProgressInterval = interval
  }

  const addLog = (level, message) => {
    const timestamp = new Date().toLocaleTimeString()
    const newLog = {
      id: Date.now(),
      timestamp,
      level,
      message
    }
    setLogs(prev => [...prev, newLog])
  }

  const clearLogs = () => {
    setLogs([])
    addLog('info', 'Logs cleared')
  }

  const toggleAutoScroll = () => {
    setAutoScroll(!autoScroll)
  }

  const saveScrapingConfig = async () => {
    try {
      const config = {
        scraping: {
          pages: pageConfig,
          enrichment: enableEnrichment,
          gemini: enableGemini
        }
      }

      const response = await fetch('/api/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      if (!response.ok) throw new Error(`Config API returned ${response.status}`)
      const result = await response.json()
      
      if (result.success) {
        addLog('success', 'Scraping configuration saved successfully')
      } else {
        throw new Error(result.error || 'Failed to save configuration')
      }
    } catch (err) {
      addLog('error', `Failed to save configuration: ${err.message}`)
    }
  }

  const updatePageConfig = (mediaType, value) => {
    setPageConfig(prev => ({
      ...prev,
      [mediaType]: parseInt(value) || 0
    }))
  }

  return (
    <div>
      {/* Scraping Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-spider text-blue-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Scraping Controls</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Mode</label>
              <select 
                value={scrapingMode}
                onChange={(e) => setScrapingMode(e.target.value)}
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="latest">Latest Content</option>
                <option value="full">Full Scrape</option>
                <option value="update">Update Existing</option>
              </select>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-2">Type</label>
              <select 
                value={scrapingType}
                onChange={(e) => setScrapingType(e.target.value)}
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="movies">Movies Only</option>
                <option value="series">Series Only</option>
                <option value="anime">Anime Only</option>
                <option value="livetv">Live TV Only</option>
              </select>
            </div>
            <button 
              className="button primary w-full" 
              onClick={startScraping}
              disabled={isLoading || scrapingStatus === 'running'}
            >
              <i className={`fas ${isLoading ? 'fa-spinner fa-spin' : 'fa-spider'}`}></i>
              {isLoading ? 'Starting...' : 'Start Scraping'}
            </button>
            {scrapingStatus === 'running' && (
              <button 
                className="button danger w-full mt-2" 
                onClick={stopScraping}
                disabled={isLoading}
              >
                <i className={`fas ${isLoading ? 'fa-spinner fa-spin' : 'fa-stop'}`}></i>
                {isLoading ? 'Stopping...' : 'Stop Scraping'}
              </button>
            )}
          </div>
        </div>

        {/* Scraping Configuration */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-cog text-green-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Page Configuration</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Movies Pages</label>
              <input 
                type="number" 
                value={pageConfig.movies}
                onChange={(e) => updatePageConfig('movies', e.target.value)}
                min="0" 
                max="50"
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-2">Series Pages</label>
              <input 
                type="number" 
                value={pageConfig.series}
                onChange={(e) => updatePageConfig('series', e.target.value)}
                min="0" 
                max="50"
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-2">Anime Pages</label>
              <input 
                type="number" 
                value={pageConfig.anime}
                onChange={(e) => updatePageConfig('anime', e.target.value)}
                min="0" 
                max="50"
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-2">Live TV Pages</label>
              <input 
                type="number" 
                value={pageConfig.livetv}
                onChange={(e) => updatePageConfig('livetv', e.target.value)}
                min="0" 
                max="50"
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Advanced Configuration */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-sliders-h text-purple-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Advanced Settings</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="enable-enrichment"
                checked={enableEnrichment}
                onChange={(e) => setEnableEnrichment(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="enable-enrichment" className="text-sm text-gray-300">
                Enable metadata enrichment
              </label>
            </div>
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="enable-gemini"
                checked={enableGemini}
                onChange={(e) => setEnableGemini(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="enable-gemini" className="text-sm text-gray-300">
                Use Gemini AI
              </label>
            </div>
            <button 
              className="button secondary w-full" 
              onClick={saveScrapingConfig}
            >
              <i className="fas fa-save"></i> Save Config
            </button>
          </div>
        </div>

        {/* Current Status */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-info-circle text-purple-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Current Status</h3>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-400">Status:</span>
              <span className={`text-sm font-medium ${
                scrapingStatus === 'error' ? 'text-red-500' : 
                scrapingStatus === 'running' ? 'text-yellow-500' : 
                scrapingStatus === 'stopped' ? 'text-orange-500' :
                scrapingStatus === 'completed' ? 'text-green-500' :
                'text-gray-500'
              }`}>
                {scrapingStatus.charAt(0).toUpperCase() + scrapingStatus.slice(1)}
              </span>
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Progress:</span>
                <span className="text-sm text-gray-300">{scrapingProgress}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${scrapingProgress}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="bg-gray-800 rounded-lg p-6 mb-6">
        <div className="flex items-center mb-4">
          <i className="fas fa-chart-bar text-red-500 text-2xl mr-4"></i>
          <h3 className="text-lg font-semibold text-white">Statistics</h3>
        </div>
        {isLoading ? (
          <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
        ) : error ? (
          <div className="text-red-500 text-sm">{error}</div>
        ) : scrapingStats ? (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">{scrapingStats.statistics?.totalJobs || 0}</div>
              <div className="text-gray-400 text-sm">Total Jobs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{scrapingStats.statistics?.successfulJobs || 0}</div>
              <div className="text-gray-400 text-sm">Successful</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">{scrapingStats.statistics?.failedJobs || 0}</div>
              <div className="text-gray-400 text-sm">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">{scrapingStats.successRate || '0%'}</div>
              <div className="text-gray-400 text-sm">Success Rate</div>
            </div>
          </div>
        ) : (
          <div className="text-gray-500 text-sm">No data available</div>
        )}
      </div>

      {/* Latest Job Details */}
      {latestJob && (
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-info-circle text-blue-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Latest Job Details</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <span className="text-gray-400 text-sm">Job ID:</span>
              <div className="text-white font-mono text-sm">{latestJob.jobId}</div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Status:</span>
              <div className={`text-sm font-medium ${
                latestJob.status === 'running' ? 'text-yellow-500' :
                latestJob.status === 'completed' ? 'text-green-500' :
                latestJob.status === 'failed' ? 'text-red-500' :
                latestJob.status === 'stopped' ? 'text-orange-500' :
                'text-gray-500'
              }`}>
                {latestJob.status.charAt(0).toUpperCase() + latestJob.status.slice(1)}
              </div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Mode:</span>
              <div className="text-white text-sm capitalize">{latestJob.mode}</div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Type:</span>
              <div className="text-white text-sm capitalize">{latestJob.type}</div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Started:</span>
              <div className="text-white text-sm">
                {latestJob.startedAt ? new Date(latestJob.startedAt).toLocaleString() : 'N/A'}
              </div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Duration:</span>
              <div className="text-white text-sm">{latestJob.duration || 'N/A'}</div>
            </div>
            {latestJob.pages && (
              <div className="md:col-span-2 lg:col-span-3">
                <span className="text-gray-400 text-sm">Page Configuration:</span>
                <div className="grid grid-cols-4 gap-2 mt-1">
                  <div className="text-center">
                    <div className="text-white font-semibold">{latestJob.pages.movies || 0}</div>
                    <div className="text-gray-400 text-xs">Movies</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-semibold">{latestJob.pages.series || 0}</div>
                    <div className="text-gray-400 text-xs">Series</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-semibold">{latestJob.pages.anime || 0}</div>
                    <div className="text-gray-400 text-xs">Anime</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-semibold">{latestJob.pages.livetv || 0}</div>
                    <div className="text-gray-400 text-xs">Live TV</div>
                  </div>
                </div>
              </div>
            )}
            {latestJob.error && (
              <div className="md:col-span-2 lg:col-span-3">
                <span className="text-gray-400 text-sm">Error:</span>
                <div className="text-red-400 text-sm mt-1 bg-red-900 p-2 rounded">
                  {latestJob.error}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Live Logs */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <i className="fas fa-terminal text-blue-400 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Live Logs</h3>
          </div>
          <div className="flex space-x-2">
            <button 
              className="button secondary" 
              onClick={clearLogs}
            >
              <i className="fas fa-trash"></i> Clear Logs
            </button>
            <button 
              className={`button ${autoScroll ? 'primary' : 'secondary'}`}
              onClick={toggleAutoScroll}
            >
              <i className="fas fa-arrow-down"></i> Auto Scroll {autoScroll ? 'ON' : 'OFF'}
            </button>
          </div>
        </div>
        <div className="bg-gray-900 rounded-lg p-4 h-64 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              <i className="fas fa-terminal text-2xl mb-2"></i>
              <p>No logs available</p>
            </div>
          ) : (
            <div className="space-y-1">
              {logs.map((log) => (
                <div 
                  key={log.id}
                  className={`text-sm p-2 rounded ${
                    log.level === 'error' ? 'bg-red-900 text-red-200' :
                    log.level === 'warning' ? 'bg-yellow-900 text-yellow-200' :
                    log.level === 'success' ? 'bg-green-900 text-green-200' :
                    'bg-gray-800 text-gray-300'
                  }`}
                >
                  <span className="text-gray-400">[{log.timestamp}]</span> {log.level.toUpperCase()}: {log.message}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}