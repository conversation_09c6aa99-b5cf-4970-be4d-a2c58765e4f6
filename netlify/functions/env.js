// Create a local copy of the environment configuration for Netlify Functions
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });

// Import constants from the main project
// const constantsPath = path.join(__dirname, '..', '..', 'src', 'config', 'constants.js');
// const constants = require(constantsPath);
// Corrected to require constants.js from the same directory in the Netlify function bundle
const constants = require('./constants.js');

// Log environment variables for debugging
console.log('MongoDB URI exists:', !!process.env.MONGO_URI);
console.log('Environment:', process.env.NODE_ENV);
console.log('MongoDB Connection String Format Valid:', process.env.MONGO_URI && process.env.MONGO_URI.startsWith('mongodb'));

// Export environment configuration
module.exports = {
  mongoUri: process.env.MONGO_URI,
  port: process.env.PORT || 3001,
  tmdbApiKey: process.env.TMDB_API_KEY,
  geminiApiKey: process.env.GEMINI_API_KEY,
  telegramToken: process.env.TELEGRAM_TOKEN,
  scrapeMode: process.env.SCRAPE_MODE || 'full',
  ...constants,
};