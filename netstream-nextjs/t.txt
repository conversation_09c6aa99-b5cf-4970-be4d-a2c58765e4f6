'use client'
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client'
import { setContext } from '@apollo/client/link/context'

const httpLink = createHttpLink({
  uri: (process.env.NEXT_PUBLIC_API_BASE_URL ? process.env.NEXT_PUBLIC_API_BASE_URL : 'http://localhost:3001') + '/graphql',
})

const authLink = setContext((_, { headers }) => {
  // Get admin token if available
  const token = typeof window !== 'undefined' ? localStorage.getItem('adminToken') : null

  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
    }
  }
})

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          movies: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming]
            }
          },
          series: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming]
            }
          },
          anime: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming]
            }
          },
          liveTV: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming]
            }
          }
        }
      }
    }
  }),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all'
    },
    query: {
      errorPolicy: 'all'
    }
  }
})

export { client }

export function ApolloWrapper({ children }) {
  const { ApolloProvider } = require('@apollo/client')
  
  return (
    <ApolloProvider client={client}>
      {children}
    </ApolloProvider>
  )
}