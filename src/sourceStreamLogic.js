const fetch = require('node-fetch');
const https = require('https');
const fs = require('fs'); // For potential debug file writing
const { PROVIDER_CONFIG } = require('./config/constants');

// Import individual fetch methods (adjust paths as needed)
const fetchVidplyStream = require('../scripts/sourceStreamUrlLogic/vidplyFetchStream');
const fetchUqloadStream = require('../scripts/sourceStreamUrlLogic/uqloadFetchStream');
const fetchVoeStream = require('../scripts/sourceStreamUrlLogic/voeFetchStream');
const fetchGenericStream = require('../scripts/sourceStreamUrlLogic/genericFetchStream');
const fetchStreamtapeStream = require('../scripts/sourceStreamUrlLogic/streamtapeFetchStream');

const DEBUG_MODE = false;  // Control debug file creation

// Custom axios instance (using node-fetch, but mimicking axios API)
const fetchWithSSLBypass = async (url, options = {}) => {
    const httpsAgent = new https.Agent({ rejectUnauthorized: false });
    return fetch(url, { ...options, agent: options.agent || (url.startsWith('https') ? httpsAgent : undefined) });
};

// Copied and adapted verifyDirectUrl from sourceStreamFetcher.js
async function verifyDirectUrl(directUrl, referer, cookieHeader = '', extraHeaders = {}, retries = 2) {
    for (let attempt = 0; attempt <= retries; attempt++) {
        try {
            if (typeof directUrl !== 'string' || !directUrl.startsWith('http')) {
                throw new Error('Invalid URL format');
            }

            const headResponse = await fetchWithSSLBypass(directUrl, {
                method: 'HEAD',
                headers: { 'User-Agent': 'Mozilla/5.0', 'Referer': referer, 'Cookie': cookieHeader, ...extraHeaders },
            });
            const contentType = headResponse.headers.get('content-type') || '';
            const isValid = headResponse.status === 200 && (
                contentType.includes('video') ||
                contentType.includes('application/x-mpegURL') ||
                contentType.includes('application/vnd.apple.mpegurl') ||
                directUrl.match(/\.(mp4|m3u8)$/)
            );

            if (!isValid) {
                throw new Error(`Invalid content type: ${contentType}`);
            }
            return {
                url: directUrl,
                size: headResponse.headers.get('content-length') || 'Unknown',
                type: contentType.includes('x-mpegURL') || contentType.includes('mpegurl') || directUrl.includes('m3u8') ? 'HLS' : 'MP4'
            };
        } catch (error) {

            if (attempt < retries && (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT')) {
                await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
                continue;
            }
            return null; // Return null for failed verification
        }
    }
    return null; // Return null after all retries
}

async function getDirectVideoLink(inputUrl, config, cookies) {
    const fetchMethods = [
        { name: 'PassMd5', fn: fetchVidplyStream },
        { name: 'VOE', fn: fetchVoeStream },
        { name: 'Generic', fn: fetchGenericStream },
        { name: 'Uqload', fn: fetchUqloadStream },
        { name: 'Streamtape', fn: fetchStreamtapeStream }
    ];

    for (const method of fetchMethods) {
        try {
             // logger placeholders, replaced with console.log (adapt as needed)
            const directUrl = await method.fn(inputUrl, config, cookies, DEBUG_MODE, console.log, console.error);
            if (directUrl) {
                const verified = await verifyDirectUrl(directUrl, inputUrl, cookies.cookieHeader, config.headers);
                if (verified) {
                    return { ...verified, method: method.name };
                }
            }
        } catch (error) {
            console.error(`Method ${method.name} failed for ${inputUrl}`, error);
        }
    }
    return null;
}


async function fetchSourceStreamUrl(streamingUrl, provider) {
    const config = PROVIDER_CONFIG[provider] || { baseUrl: `https://${streamingUrl.split('/')[2]}`, headers: { 'Referer': `https://${streamingUrl.split('/')[2]}/` } };
    config.provider = provider;

    try {
        const eResponse = await fetchWithSSLBypass(streamingUrl, {
            headers: { 'User-Agent': 'Mozilla/5.0', ...config.headers },
        });


        const setCookieHeader = eResponse.headers.get('set-cookie');
        const cookies = {
            eResponse: {
                data: await eResponse.text(), // Get the response body as text
                request: { res: { responseUrl: eResponse.url } }
            },
            cookieHeader: setCookieHeader ? setCookieHeader.split(';')[0] : 'None'
        };


        const result = await getDirectVideoLink(streamingUrl, config, cookies);
        return result; // Return the result directly
    } catch (error) {
        console.error(`Failed to fetch embed page for ${streamingUrl}`, error);
        return null; // Return null on error
    }
}


module.exports = { fetchSourceStreamUrl };