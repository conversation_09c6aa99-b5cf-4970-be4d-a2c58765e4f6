// Simple GraphQL test to isolate the issue
require("dotenv").config();

const fastify = require('fastify')({
  logger: true
});

const simpleSchema = `
  type Query {
    hello: String
  }
`;

const simpleResolvers = {
  Query: {
    hello: () => 'Hello World!'
  }
};

async function start() {
  try {
    console.log('Testing basic Mercurius registration...');
    
    await fastify.register(require('mercurius'), {
      schema: simpleSchema,
      resolvers: simpleResolvers,
      graphiql: true
    });
    
    console.log('Mercurius registered successfully');
    
    await fastify.listen({ port: 3002, host: 'localhost' });
    console.log('Test server listening on http://localhost:3002/graphql');
    
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
}

start();
