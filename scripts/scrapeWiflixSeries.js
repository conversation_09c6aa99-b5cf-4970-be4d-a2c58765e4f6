// scripts/scrapeWiflixSeries.js - FINAL VERSION (Uses fixed list/detail)
const logger = require('../src/utils/logger');
const cheerio = require('cheerio');
const { saveToDB } = require('./saveToDB');
const { scrapeWiflixDetail } = require('../src/scrapers/sites/wiflix/detail'); // Uses fixed Puppeteer detail
const { scrapeWiflixList } = require('../src/scrapers/sites/wiflix/list');   // Uses fixed Puppeteer list
const { enrichItem, enrichItemWithOptions } = require('../src/enrichment/services/enrichService');
const { WIFLIX_BASE } = require('../src/config/constants');
const Config = require('../src/db/models/Config');
const Series = require('../src/db/models/Series');
const { fetchPageWithPuppeteer } = require('../src/utils/browserUtils'); // No closeBrowser here
const pLimit = require('p-limit');

// --- Updated getTotalPages using Puppeteer ---
async function getTotalPages(baseUrl) {
     try {
        logger.info(`[Wiflix Series] Detecting total pages using Puppeteer for ${baseUrl}`);
        // Use the list scraper's detection
         const listScraper = require('../src/scrapers/sites/wiflix/list'); // Re-require if needed
        return await listScraper.detectTotalWiflixPages(baseUrl);
    } catch (error) {
        logger.error(`[Wiflix Series] Error detecting total pages via Puppeteer for ${baseUrl}: ${error.message}`);
        return 1; // Default to 1 on error
    }
}

async function isDuplicateSeries(detailUrlPath) {
  if (!detailUrlPath) return false;
  const existing = await Series.findOne({ detailUrlPath }).select('_id episodes').lean(); // Select episodes
  return existing; // Return the doc or null
}

async function timeout(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function scrapePage(page, endpoint, saveToDB, isLatestMode = false, processedUrls = new Set()) {
  try {
    // Use the updated list scraper
    const seriesList = await scrapeWiflixList('series', { [endpoint]: page });
    logger.info(`Collected ${seriesList.length} series from ${endpoint}, page ${page} before processing`);

     if(seriesList.length === 0) {
        logger.warn(`No series collected from list page ${page} of ${endpoint}. Skipping detail processing for this page.`);
        return [];
    }

    const seriesToSave = [];
    // Process series sequentially to avoid concurrency issues
    for (const serie of seriesList) {
        try {
            let detailUrlPath;
             try {
                 detailUrlPath = new URL(serie.detailUrl).pathname;
             } catch (urlError) {
                 logger.warn(`[Wiflix Series] Invalid detail URL, cannot create path: ${serie.detailUrl}`);
                 detailUrlPath = serie.detailUrl;
             }

            // Check if URL has already been processed in this scraping session
            if (processedUrls.has(serie.detailUrl)) {
                logger.info(`Skipping already processed URL in this session: ${serie.title} (${serie.detailUrl})`);
                continue;
            }

            // Add URL to processed set
            processedUrls.add(serie.detailUrl);

             let existingSeries = null;
             if (detailUrlPath) {
                 // Use updated isDuplicateSeries which returns the doc or null
                 existingSeries = await isDuplicateSeries(detailUrlPath);
             }

             // Deduplication Check
             if (!isLatestMode && existingSeries) {
                 logger.info(`Skipping duplicate series: ${serie.title} (path: ${detailUrlPath})`);
                 continue; // Skip this series entirely
             }

            logger.info(`Processing series: ${serie.title} (${serie.detailUrl})`);
            // Use the updated detail scraper
            const details = await scrapeWiflixDetail(serie.detailUrl);

            // Combine and Enrich (only if new or latest mode)
            let enriched = serie;
             if (!existingSeries || isLatestMode) {
                const combinedItem = { ...serie, ...details }; // details contains structured streams/episodes
                // Use enrichItemWithOptions to ensure seasons are fetched
                enriched = await enrichItemWithOptions(combinedItem, 'series', {
                    fetchSeasons: true,
                    useAdvanced: process.env.USE_ADVANCED_ENRICHMENT === 'true'
                });
                // Add a delay between enrichment calls to avoid rate limiting
                await timeout(1000);
            }


             // --- Format Episodes (using data from fixed detail scraper) ---
             const formattedEpisodes = (details.episodes || []) // Use details.episodes
                .map(ep => {
                     if (!ep || !ep.episodeNumber) return null;
                     return {
                        episodeNumber: String(ep.episodeNumber),
                        season: String(ep.season || serie.season || '1'),
                        language: ep.language || 'unknown',
                        streamingUrls: (ep.streamingUrls || []).map(stream => {
                             if (!stream || !stream.url) return null;
                             return {
                                url: stream.url,
                                provider: stream.provider || 'unknown',
                                language: stream.language || ep.language || 'unknown',
                                lastChecked: new Date(),
                                isActive: true,
                                sourceStreamUrl: null
                             };
                        }).filter(Boolean)
                     }
                }).filter(Boolean);

             // Episode count check for latest mode
             if (isLatestMode && existingSeries) {
                 const existingEpisodeCount = existingSeries.episodes?.length || 0;

                 // Check if metadata has changed
                 let metadataChanged = false;

                 // Check if TMDB data has changed
                 if (existingSeries.tmdb && enriched.tmdb) {
                     if (existingSeries.tmdb.id !== enriched.tmdb.id) {
                         logger.info(`TMDB ID changed for ${serie.title}: ${existingSeries.tmdb.id} -> ${enriched.tmdb.id}`);
                         metadataChanged = true;
                     }
                 }

                 // Check if seasons data has changed
                 const existingTmdbSeasonsCount = existingSeries.tmdbSeasons?.length || 0;
                 const newTmdbSeasonsCount = enriched.tmdbSeasons?.length || 0;

                 if (existingTmdbSeasonsCount !== newTmdbSeasonsCount) {
                     logger.info(`Seasons count changed for ${serie.title}: TMDB (${existingTmdbSeasonsCount} -> ${newTmdbSeasonsCount})`);
                     metadataChanged = true;
                 }

                 // Only skip if no new episodes, no metadata changes, AND the series already has tmdbSeasons data
                 if (formattedEpisodes.length === existingEpisodeCount && !metadataChanged &&
                     existingSeries.tmdbSeasons && existingSeries.tmdbSeasons.length > 0) {
                      logger.info(`Updating series timestamp (latest mode): ${serie.title} (path: ${detailUrlPath}) - no changes detected, updating timestamp only`);
                      // Update only the updatedAt field
                      try {
                          await Series.updateOne({ detailUrlPath }, { $set: { updatedAt: new Date() } });
                      } catch (err) {
                          logger.error(`Error updating timestamp for ${serie.title}: ${err.message}`);
                      }
                      continue; // Skip saving if no change and already has seasons data
                 }

                 if (formattedEpisodes.length === existingEpisodeCount && metadataChanged) {
                     logger.info(`Updating series (latest mode): ${serie.title} - metadata changes detected, updating database`);
                 } else if (formattedEpisodes.length === existingEpisodeCount) {
                     logger.info(`Updating series (latest mode): ${serie.title} - No new episodes but updating metadata/seasons`);
                 } else {
                     logger.info(`Updating series (latest mode): ${serie.title} - Episode count changed (${existingEpisodeCount} -> ${formattedEpisodes.length})`);
                 }
             }

            const seriesData = {
              title: enriched.title || "Untitled",
              detailUrl: enriched.detailUrl,
              detailUrlPath: detailUrlPath,
              cleanedTitle: enriched.cleanedTitle || enriched.title,
              season: enriched.season || serie.season || '1',
              thumbnail: enriched.thumbnail || enriched.image || details.thumbnail || null,
              image: enriched.image || enriched.thumbnail || details.thumbnail || null,
              episodes: formattedEpisodes, // Assign the correctly formatted episodes
              metadata: enriched.metadata || details.metadata || {},
              tmdb: enriched.tmdb || null,
              tmdbSeasons: enriched.tmdbSeasons || null,
              tmdbSeason: enriched.tmdbSeason || null,
              updatedAt: new Date() // Always set updatedAt to current date
            };
            seriesToSave.push(seriesData);

        } catch (err) {
            logger.error(`Error processing series ${serie?.title || 'UNKNOWN URL'} (${serie?.detailUrl}): ${err.message}`);
        }
    }

    logger.debug(`Page ${page} for ${endpoint} - seriesToSave length: ${seriesToSave.length}`);
    if (seriesToSave.length > 0) {
      logger.info(`Saving/Updating ${seriesToSave.length} series from page ${page} to database`);
      try {
        // Call saveToDB and capture the result
        const saveResult = await saveToDB(seriesToSave, 'series');

        // Log detailed results
        logger.info(`Database operation results for page ${page}: Created: ${saveResult.created}, Updated: ${saveResult.updated}, Errors: ${saveResult.errors}`);

        if (saveResult.created === 0 && saveResult.updated === 0) {
          logger.warn(`No series were created or updated for page ${page} despite having ${seriesToSave.length} items to save`);
        } else {
          logger.info(`Successfully processed/saved ${saveResult.created + saveResult.updated} series from page ${page}`);
        }

        return seriesToSave;
      } catch (dbError) {
        logger.error(`Database error while saving series from page ${page}: ${dbError.message}`, { stack: dbError.stack });
        // Return the series data anyway so the scraping process can continue
        return seriesToSave;
      }
    }
    logger.warn(`No new/updated series to save from page ${page} for ${endpoint}`);
    return [];
  } catch (err) {
    logger.error(`Error scraping series page ${page} for ${endpoint}: ${err.message}`);
    return [];
  }
}

async function scrapeWiflixSeries(pageLimit = 0, saveToDB, isLatestMode = false) {
  const mainStartTime = Date.now();
  try {
    // Add URL deduplication cache
    const processedUrls = new Set();

    const endpoints = ['serie-en-streaming', 'vf', 'vostfr'];
    const allSeriesProcessed = [];

    // Get the latest WIFLIX_BASE from the database
    const wiflixBase = await Config.getValue('WIFLIX_BASE', WIFLIX_BASE);
    logger.info(`[Wiflix Series] Using Wiflix base URL: ${wiflixBase}`);

    for (const endpoint of endpoints) {
      const endpointStartTime = Date.now();
      const baseUrl = `https://${wiflixBase}/${endpoint}`;
       // Use the list scraper's pagination detection
      const listScraper = require('../src/scrapers/sites/wiflix/list'); // Re-require if needed
      const maxPagesDetected = await listScraper.detectTotalWiflixPages(baseUrl);
      const maxPages = pageLimit === -1 ? maxPagesDetected : Math.min(pageLimit || 1, maxPagesDetected);
      const pages = Array.from({ length: maxPages }, (_, i) => i + 1);
      logger.info(`[Wiflix Series] Scraping endpoint "${endpoint}" - Limit: ${pageLimit}, Detected: ${maxPagesDetected}, Scraping: ${maxPages} pages`);

      // Process pages sequentially instead of in batches to avoid concurrency issues
      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        logger.info(`[Wiflix Series] Processing page ${page}/${maxPages} for ${endpoint}`);

        // Pass the URL cache to scrapePage
        const pageResults = await scrapePage(page, endpoint, saveToDB, isLatestMode, processedUrls);
        allSeriesProcessed.push(...pageResults);

        // Add a delay between pages
        if (i < pages.length - 1) {
          logger.info(`[Wiflix Series] Pausing 3s between pages for endpoint ${endpoint}...`);
          await timeout(3000);
        }
      }

      logger.info(`[Wiflix Series] Finished endpoint ${endpoint} in ${(Date.now() - endpointStartTime) / 1000}s`);
      logger.info(`[Wiflix Series] Processed URLs so far: ${processedUrls.size}`);
    }

    logger.info(`[Wiflix Series] Total series processed/saved: ${allSeriesProcessed.length}. Total time: ${(Date.now() - mainStartTime) / 1000}s`);
    logger.info(`[Wiflix Series] Total unique URLs processed: ${processedUrls.size}`);
    return allSeriesProcessed;
  } catch (err) {
    logger.error(`[Wiflix Series] Fatal error in scrapeWiflixSeries: ${err.message}`);
    return [];
  }
  // No finally block here
}

// Direct execution block
if (require.main === module) {
    const mongoose = require('mongoose');
    const env = require('../src/config/env');
    mongoose.connect(env.mongoUri)
        .then(() => {
            logger.info('Connected to DB for standalone Wiflix Series scrape');
            const mode = process.env.SCRAPE_MODE === 'latest';
            scrapeWiflixSeries(env.PAGES_TO_SCRAPE_WIFLIX_SERIES || 1, saveToDB, mode).finally(async () => { // Ensure page limit defaults
                await require('../src/utils/browserUtils').closeBrowser();
                await mongoose.disconnect();
                logger.info('DB disconnected after standalone Wiflix Series scrape');
            });
        })
        .catch(err => {
            logger.error('DB connection error for standalone Wiflix Series scrape', err);
            process.exit(1);
        });
}

module.exports = { scrapeWiflixSeries };