'use client'
import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useLazyQuery } from '@apollo/client'
import { SEARCH_MEDIA } from '@/lib/queries'
import { useDebounce } from 'react-use'
import { getImageUrl } from '@/utils/helpers'

export default function MobileSearchModal() {
  const [isVisible, setIsVisible] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [results, setResults] = useState([])
  const inputRef = useRef(null)
  const router = useRouter()

  const [searchMedia, { loading }] = useLazyQuery(SEARCH_MEDIA, {
    onCompleted: (data) => {
      setResults(data.search?.items || [])
    }
  })

  // Debounced search
  useDebounce(
    () => {
      if (searchQuery.trim().length > 2) {
        searchMedia({ variables: { query: searchQuery.trim() } })
      } else {
        setResults([])
      }
    },
    300,
    [searchQuery]
  )

  useEffect(() => {
    const showMobileSearch = () => {
      setIsVisible(true)
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus()
        }
      }, 100)
    }

    const hideMobileSearch = () => {
      setIsVisible(false)
      setSearchQuery('')
      setResults([])
    }

    // Expose functions globally
    window.showMobileSearch = showMobileSearch
    window.hideMobileSearch = hideMobileSearch

    return () => {
      if (window.showMobileSearch) {
        delete window.showMobileSearch
      }
      if (window.hideMobileSearch) {
        delete window.hideMobileSearch
      }
    }
  }, [])

  const handleResultClick = (item) => {
    const type = item.__typename.toLowerCase()
    // Convert singular to plural for URL routing
    const urlType = type === 'movie' ? 'movies' :
                   type === 'series' ? 'series' :
                   type === 'anime' ? 'anime' : type
    router.push(`/${urlType}/${item.id}`)
    setIsVisible(false)
    setSearchQuery('')
    setResults([])
  }

  const handleSearch = () => {
    const query = searchQuery.trim()
    if (query) {
      // Could implement a general search page here
      console.log('Search for:', query)
      setIsVisible(false)
      setSearchQuery('')
      setResults([])
    }
  }

  if (!isVisible) return null

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center pt-20"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          setIsVisible(false)
          setSearchQuery('')
          setResults([])
        }
      }}
    >
      <div className="bg-gray-800 w-full max-w-md mx-4 rounded-lg shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">Search NetStream</h3>
          <button 
            onClick={() => {
              setIsVisible(false)
              setSearchQuery('')
              setResults([])
            }}
            className="text-gray-400 hover:text-white"
          >
            <i className="fas fa-times text-xl"></i>
          </button>
        </div>

        {/* Search Input */}
        <div className="p-4">
          <div className="relative">
            <input
              ref={inputRef}
              type="text"
              placeholder="Search movies, series, anime..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleSearch()
                }
              }}
              className="w-full pl-10 pr-12 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 text-white placeholder-gray-400"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            {loading ? (
              <i className="fas fa-spinner fa-spin absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            ) : (
              <button 
                onClick={handleSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-400"
              >
                <i className="fas fa-arrow-right"></i>
              </button>
            )}
          </div>
        </div>

        {/* Search Results */}
        {results.length > 0 && (
          <div className="max-h-96 overflow-y-auto">
            {results.map((item) => (
              <button
                key={`${item.__typename}-${item.id}`}
                onClick={() => handleResultClick(item)}
                className="w-full flex items-center p-4 hover:bg-gray-700 transition-colors text-left border-t border-gray-700"
              >
                <img
                  src={getImageUrl(item)}
                  alt={item.title || item.displayTitle}
                  className="w-12 h-16 object-cover rounded mr-3"
                  onError={(e) => {
                    e.target.src = '/default-thumbnail.svg'
                  }}
                />
                <div className="flex-1">
                  <h4 className="font-medium text-white">
                    {item.title || item.displayTitle}
                  </h4>
                  <p className="text-sm text-gray-400">{item.__typename}</p>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
