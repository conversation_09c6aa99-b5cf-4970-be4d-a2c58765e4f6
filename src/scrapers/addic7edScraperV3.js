/**
 * Addic7ed.com Subtitle Scraper V3
 *
 * Based on direct analysis of the Addic7ed.com website structure.
 *
 * Website Structure:
 * - Main page: https://www.addic7ed.com/
 * - Shows page: https://www.addic7ed.com/shows.php
 * - Show page: https://www.addic7ed.com/show/[show_id]
 * - Episode page: https://www.addic7ed.com/serie/[show_name]/[season]/[episode]/[episode_name]
 * - Download link: https://www.addic7ed.com/original/[subtitle_id]/[version]
 *
 * HTML Structure for Episode Pages:
 * - Episode title is in <span class="titulo">
 * - Subtitle tables have class "tabel95"
 * - Version info is in <td class="NewsTitle">
 * - Language cells have class "language"
 * - Download links are <a class="face-button" href="/original/[id]/[version]">
 */

const axios = require('axios');
const cheerio = require('cheerio');
const logger = require('../utils/logger');
const geminiAI = require('../utils/geminiAI');

/**
 * Common headers for all requests to Addic7ed.com
 */
const COMMON_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'Accept-Language': 'en-US,en;q=0.9',
  'Referer': 'https://www.addic7ed.com/',
  'Connection': 'keep-alive',
  'Cache-Control': 'max-age=0'
};

/**
 * Get the URL for a show by ID
 * @param {string} showId - Show ID
 * @returns {string} Show URL
 */
function getShowUrl(showId) {
  return `https://www.addic7ed.com/show/${showId}`;
}

/**
 * Get the URL for an episode by show name or ID
 * @param {string|Object} show - Show name or object with id and name
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @param {string} episodeName - Episode name (optional)
 * @returns {string} Episode URL
 */
function getEpisodeUrl(show, season, episode, episodeName = '') {
  // If show is an object with id, use the show ID URL format
  if (typeof show === 'object' && show.id) {
    // Always use the show ID format as it's the most reliable
    return getShowUrl(show.id);
  }

  // For backward compatibility, support the old format with show name
  // Format the show name for the URL (replace spaces with underscores)
  const showName = typeof show === 'object' ? show.name : show;
  const formattedShowName = showName.replace(/\s+/g, '_');

  // Build the URL
  let url = `https://www.addic7ed.com/serie/${formattedShowName}/${season}/${episode}`;

  // Add episode name if provided
  if (episodeName) {
    const formattedEpisodeName = episodeName.replace(/\s+/g, '_');
    url += `/${formattedEpisodeName}`;
  }

  return url;
}

/**
 * Find episode links on a show page
 * @param {string} html - HTML content of the show page
 * @param {number} season - Season number to find
 * @param {number} episode - Episode number to find
 * @returns {string|null} Episode URL if found, null otherwise
 */
function findEpisodeLinkOnShowPage(html, season, episode) {
  try {
    console.log(`DIRECT LOG: Finding episode link on show page for S${season}E${episode}`);
    const $ = cheerio.load(html);
    let episodeLink = null;

    // Different formats to look for
    const patterns = [
      // Format: 2x01
      `${season}x${episode.toString().padStart(2, '0')}`,
      // Format: 2x1
      `${season}x${episode}`,
      // Format: S02E01
      `S${season.toString().padStart(2, '0')}E${episode.toString().padStart(2, '0')}`,
      // Format: Season 2, Episode 1
      `Season ${season}, Episode ${episode}`
    ];
    console.log(`DIRECT LOG: Looking for patterns: ${JSON.stringify(patterns)}`);

    // Log all links for debugging
    const allLinks = [];
    $('a').each((i, link) => {
      const href = $(link).attr('href');
      const text = $(link).text().trim();
      if (href && text && (href.includes('/serie/') || text.match(/\d+x\d+/) || text.match(/S\d+E\d+/))) {
        allLinks.push({ href, text });
      }
    });
    console.log(`DIRECT LOG: Found ${allLinks.length} potential episode links`);
    if (allLinks.length > 0) {
      console.log(`DIRECT LOG: First 10 links: ${JSON.stringify(allLinks.slice(0, 10))}`);
    }

    // Find all links that might be for episodes
    $('a').each((i, link) => {
      const href = $(link).attr('href');
      const text = $(link).text().trim();

      // Skip if not a link or doesn't have href
      if (!href) return;

      // Check if this is a link to the season/episode we want
      const isMatch = patterns.some(pattern => {
        const textMatch = text.includes(pattern);
        const hrefMatch = href.includes(`/${season}/${episode}`);
        if (textMatch || hrefMatch) {
          console.log(`DIRECT LOG: Found matching link - Text: "${text}", Href: "${href}", Pattern: "${pattern}", TextMatch: ${textMatch}, HrefMatch: ${hrefMatch}`);
        }
        return textMatch || hrefMatch;
      });

      if (isMatch && href.includes('/serie/')) {
        console.log(`DIRECT LOG: Selected episode link: ${href}`);
        episodeLink = href;
        return false; // Break the loop
      }
    });

    console.log(`DIRECT LOG: Final episode link result: ${episodeLink || 'Not found'}`);
    return episodeLink;
  } catch (error) {
    console.log(`DIRECT LOG: Error finding episode link: ${error.message}`);
    logger.error(`Error finding episode link: ${error.message}`);
    return null;
  }
}

/**
 * Fetch the HTML content of a URL
 * @param {string} url - URL to fetch
 * @returns {Promise<string|null>} HTML content or null if failed
 */
async function fetchHtml(url) {
  try {
    logger.info(`Fetching URL: ${url}`);

    const response = await axios.get(url, {
      headers: COMMON_HEADERS,
      timeout: 10000
    });

    if (response.status !== 200) {
      logger.warn(`Failed to fetch URL: ${url}, status: ${response.status}`);
      return null;
    }

    return response.data;
  } catch (error) {
    logger.error(`Error fetching URL: ${url}`, error);
    return null;
  }
}

/**
 * Extract subtitle information from HTML
 * @param {string} html - HTML content
 * @param {string} language - Language to filter by (optional)
 * @returns {Array} Array of subtitle objects
 */
function extractSubtitles(html, language = '') {
  try {
    const $ = cheerio.load(html);
    const subtitles = [];

    // Get the show title
    const showTitle = $('span.titulo').first().text().trim();
    logger.info(`Extracting subtitles for: ${showTitle}`);

    // Find all subtitle tables
    $('table.tabel95').each((i, table) => {
      // Get the version from the NewsTitle class
      const versionElement = $(table).find('td.NewsTitle');
      if (versionElement.length === 0) return;

      const versionText = versionElement.text();
      const versionMatch = versionText.match(/Version\s+(.*?),/);
      const version = versionMatch ? versionMatch[1].trim() : 'Unknown';

      // Find all language cells
      $(table).find('td.language').each((j, langCell) => {
        const langText = $(langCell).text().trim();

        // Skip if language doesn't match (if specified)
        if (language && langText.toLowerCase() !== language.toLowerCase()) {
          return;
        }

        // Find the download link
        const row = $(langCell).closest('tr');
        const downloadLink = row.find('a.face-button').attr('href');

        if (!downloadLink) {
          return;
        }

        // Check if completed
        const statusCell = row.find('td b');
        const status = statusCell.text().trim();

        if (status !== 'Completed') {
          return;
        }

        // Check for hearing impaired
        const hearingImpairedImg = row.find('img[title="Hearing Impaired"]');
        const hearingImpaired = hearingImpairedImg.length > 0;

        // Add to subtitles array
        subtitles.push({
          language: langText,
          version,
          download: `https://www.addic7ed.com${downloadLink}`,
          hearing_impaired: hearingImpaired,
          show_title: showTitle
        });
      });
    });

    logger.info(`Found ${subtitles.length} subtitles`);
    return subtitles;
  } catch (error) {
    logger.error('Error extracting subtitles', error);
    return [];
  }
}

/**
 * Get subtitles for a show
 * @param {string} showName - Show name
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @param {string} language - Language to filter by (optional)
 * @returns {Promise<Array>} Array of subtitle objects
 */
async function getSubtitles(showName, season, episode, language = '') {
  try {
    logger.info(`Getting subtitles for: ${showName}, Season ${season}, Episode ${episode}, Language: ${language || 'all'}`);

    // Try with the original show name
    const url = getEpisodeUrl(showName, season, episode);
    const html = await fetchHtml(url);

    if (html) {
      return extractSubtitles(html, language);
    }

    // Skip alternative names - we'll use Gemini AI instead

    // Try with known episode titles
    const episodeTitle = getKnownEpisodeTitle(showName, season, episode);
    if (episodeTitle) {
      logger.info(`Trying with known episode title: ${episodeTitle}`);
      const episodeUrl = getEpisodeUrl(showName, season, episode, episodeTitle);
      const episodeHtml = await fetchHtml(episodeUrl);

      if (episodeHtml) {
        return extractSubtitles(episodeHtml, language);
      }

      // Skip alternative names with episode title - we'll use Gemini AI instead
    }

    logger.warn(`No subtitles found for ${showName}, Season ${season}, Episode ${episode}`);
    return [];
  } catch (error) {
    logger.error('Error getting subtitles', error);
    return [];
  }
}

/**
 * Generate alternative names for a show
 * @param {string} showName - Original show name
 * @returns {Array<string>} Array of alternative names
 * @deprecated This function is no longer used as we rely on Gemini AI for better matching
 */
function generateAlternativeNames(showName) {
  // Return empty array as we're no longer using alternative names
  // We'll rely on Gemini AI for better matching instead
  return [showName];
}

/**
 * Get known episode title for specific shows
 * @param {string} showName - Show name
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @returns {string|null} Episode title if known, null otherwise
 */
function getKnownEpisodeTitle(showName, season, episode) {
  // Known episode titles for specific shows
  const episodeTitles = {
    'andor': {
      '2-1': 'One_Year_Later',
      '2-2': 'Sagrona_Teema',
      '2-3': 'Harvest',
      '2-4': 'Ever_Been_to_Ghorman?',
      '2-5': 'I_Have_Friends_Everywhere',
      '2-6': 'What_a_Festive_Evening',
      '2-7': 'Messenger',
      '2-8': 'Who_Are_You?',
      '2-9': 'Welcome_to_the_Rebellion',
      '2-10': 'Make_It_Stop',
      '2-11': 'Who_Else_Knows?',
      '2-12': 'Jedha,_Kyber,_Erso'
    },
    'the last of us': {
      '2-1': 'Future_Days',
      '2-2': 'Infected',
      '2-3': 'Wasteland',
      '2-4': 'Please_Hold_My_Hand',
      '2-5': 'Feel_Her_Love',
      '2-6': 'Left_Behind',
      '2-7': 'Kin'
    }
  };

  // Normalize show name for lookup
  const normalizedShow = showName.toLowerCase().replace(/[^\w\s]/g, '').trim();

  // Check if we have episode titles for this show
  if (episodeTitles[normalizedShow]) {
    const key = `${season}-${episode}`;
    return episodeTitles[normalizedShow][key] || null;
  }

  // Try with alternative names
  if (normalizedShow.includes('star wars') && normalizedShow.includes('andor')) {
    const key = `${season}-${episode}`;
    return episodeTitles['andor'][key] || null;
  }

  return null;
}

/**
 * Get subtitles for a show with a specific episode title
 * @param {string} showName - Show name
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @param {string} episodeTitle - Episode title
 * @param {string} language - Language to filter by (optional)
 * @returns {Promise<Array>} Array of subtitle objects
 */
async function getSubtitlesWithEpisodeTitle(showName, season, episode, episodeTitle, language = '') {
  try {
    logger.info(`Getting subtitles with episode title: ${showName}, Season ${season}, Episode ${episode}, Title: ${episodeTitle}, Language: ${language || 'all'}`);

    // Try with the original show name and episode title
    const url = getEpisodeUrl(showName, season, episode, episodeTitle);
    logger.info(`Trying URL: ${url}`);
    const html = await fetchHtml(url);

    if (html) {
      return extractSubtitles(html, language);
    }

    // Skip alternative names - we'll use Gemini AI instead

    // If no results with episode title, fall back to standard search
    logger.info(`No results with episode title, falling back to standard search`);
    return getSubtitles(showName, season, episode, language);
  } catch (error) {
    logger.error(`Error getting subtitles with episode title: ${error.message}`);
    return [];
  }
}

/**
 * Get subtitles using Gemini AI to find the best match on Addic7ed
 * @param {string} showName - Show name
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @param {string} language - Language to filter by (optional)
 * @returns {Promise<Array>} Array of subtitle objects
 */
async function getSubtitlesWithGeminiAI(showName, season, episode, language = '') {
  try {
    console.log(`DIRECT LOG: Getting subtitles with Gemini AI: ${showName}, Season ${season}, Episode ${episode}, Language: ${language || 'all'}`);
    logger.info(`Getting subtitles with Gemini AI: ${showName}, Season ${season}, Episode ${episode}, Language: ${language || 'all'}`);

    // Get the list of shows from Addic7ed with IDs
    const addic7edShows = await geminiAI.getAddic7edShowList(true);
    console.log(`DIRECT LOG: Got ${addic7edShows ? addic7edShows.length : 0} shows from Addic7ed`);

    if (!addic7edShows || addic7edShows.length === 0) {
      console.log(`DIRECT LOG: Could not fetch Addic7ed show list, falling back to standard methods`);
      logger.warn(`Could not fetch Addic7ed show list, falling back to standard methods`);
      return getSubtitles(showName, season, episode, language);
    }

    // Use Gemini AI to find the best match
    console.log(`DIRECT LOG: Asking Gemini AI to find the best match for "${showName}" among ${addic7edShows.length} shows`);
    logger.info(`Asking Gemini AI to find the best match for "${showName}" among ${addic7edShows.length} shows`);
    const bestMatch = await geminiAI.findBestShowMatch(showName, addic7edShows);
    console.log(`DIRECT LOG: Best match result: ${JSON.stringify(bestMatch)}`);

    if (!bestMatch) {
      console.log(`DIRECT LOG: Gemini AI could not find a good match for "${showName}"`);
      logger.warn(`Gemini AI could not find a good match for "${showName}"`);
      return getSubtitles(showName, season, episode, language);
    }

    console.log(`DIRECT LOG: Gemini AI found best match: "${bestMatch.name}" (ID: ${bestMatch.id}) for "${showName}"`);
    logger.info(`Gemini AI found best match: "${bestMatch.name}" (ID: ${bestMatch.id}) for "${showName}"`);

    // First try the show page with ID - this is the most reliable approach
    const showUrl = getShowUrl(bestMatch.id);
    console.log(`DIRECT LOG: Trying show page URL: ${showUrl}`);
    logger.info(`Trying show page URL: ${showUrl}`);
    const showHtml = await fetchHtml(showUrl);
    console.log(`DIRECT LOG: Show HTML fetch result: ${showHtml ? 'Success' : 'Failed'}`);
    console.log(`DIRECT LOG: Show HTML length: ${showHtml ? showHtml.length : 0}`);

    // Debug HTML generation removed

    if (showHtml) {
      // Find the episode link on the show page
      console.log(`DIRECT LOG: Finding episode link for S${season}E${episode}`);
      const episodeLink = findEpisodeLinkOnShowPage(showHtml, season, episode);
      console.log(`DIRECT LOG: Episode link result: ${episodeLink || 'Not found'}`);

      if (episodeLink) {
        console.log(`DIRECT LOG: Found episode link on show page: ${episodeLink}`);
        logger.info(`Found episode link on show page: ${episodeLink}`);
        const fullEpisodeUrl = `https://www.addic7ed.com${episodeLink}`;
        console.log(`DIRECT LOG: Fetching episode HTML from: ${fullEpisodeUrl}`);
        const episodeHtml = await fetchHtml(fullEpisodeUrl);
        console.log(`DIRECT LOG: Episode HTML fetch result: ${episodeHtml ? 'Success' : 'Failed'}`);

        if (episodeHtml) {
          console.log(`DIRECT LOG: Extracting subtitles from episode HTML`);
          const subtitles = extractSubtitles(episodeHtml, language);
          console.log(`DIRECT LOG: Found ${subtitles ? subtitles.length : 0} subtitles`);

          if (subtitles && subtitles.length > 0) {
            console.log(`DIRECT LOG: Found ${subtitles.length} subtitles using show page episode link`);
            logger.info(`Found ${subtitles.length} subtitles using show page episode link`);
            return subtitles;
          }
        }
      } else {
        console.log(`DIRECT LOG: No episode link found on show page for S${season}E${episode}`);
        logger.info(`No episode link found on show page for S${season}E${episode}`);

        // If no specific episode link found, try to extract all available episodes
        try {
          const $ = cheerio.load(showHtml);
          logger.info(`Analyzing show page to find available seasons and episodes`);

          // Log all available seasons and episodes for debugging
          const availableEpisodes = [];
          $('a').each((i, link) => {
            const href = $(link).attr('href');
            const text = $(link).text().trim();

            if (href && href.includes('/serie/') &&
                (text.match(/\d+x\d+/) || text.match(/S\d+E\d+/))) {
              availableEpisodes.push({ href, text });
            }
          });

          if (availableEpisodes.length > 0) {
            logger.info(`Available episodes on show page: ${JSON.stringify(availableEpisodes.map(e => e.text))}`);

            // Try to find the closest episode (e.g., if S2E1 doesn't exist, maybe S1E1 does)
            let closestEpisode = null;

            // First try same season, different episode
            closestEpisode = availableEpisodes.find(e =>
              e.text.includes(`${season}x`) ||
              e.text.includes(`S${season.toString().padStart(2, '0')}E`)
            );

            if (closestEpisode) {
              logger.info(`Found episode in same season: ${closestEpisode.text}`);
              const fullEpisodeUrl = `https://www.addic7ed.com${closestEpisode.href}`;
              const episodeHtml = await fetchHtml(fullEpisodeUrl);

              if (episodeHtml) {
                const subtitles = extractSubtitles(episodeHtml, language);

                if (subtitles && subtitles.length > 0) {
                  logger.info(`Found ${subtitles.length} subtitles using closest episode in same season`);
                  return subtitles;
                }
              }
            }

            // Then try different season, same episode number if possible
            closestEpisode = availableEpisodes.find(e => {
              const epMatch = e.text.match(/(\d+)x(\d+)/) || e.text.match(/S(\d+)E(\d+)/);
              return epMatch && epMatch[2] == episode;
            });

            if (closestEpisode) {
              logger.info(`Found episode with same episode number in different season: ${closestEpisode.text}`);
              const fullEpisodeUrl = `https://www.addic7ed.com${closestEpisode.href}`;
              const episodeHtml = await fetchHtml(fullEpisodeUrl);

              if (episodeHtml) {
                const subtitles = extractSubtitles(episodeHtml, language);

                if (subtitles && subtitles.length > 0) {
                  logger.info(`Found ${subtitles.length} subtitles using episode with same number in different season`);
                  return subtitles;
                }
              }
            }

            // Finally, just try the first available episode
            if (availableEpisodes.length > 0) {
              logger.info(`Trying first available episode: ${availableEpisodes[0].text}`);
              const fullEpisodeUrl = `https://www.addic7ed.com${availableEpisodes[0].href}`;
              const episodeHtml = await fetchHtml(fullEpisodeUrl);

              if (episodeHtml) {
                const subtitles = extractSubtitles(episodeHtml, language);

                if (subtitles && subtitles.length > 0) {
                  logger.info(`Found ${subtitles.length} subtitles using first available episode`);
                  return subtitles;
                }
              }
            }
          } else {
            logger.info(`No episodes found on show page`);
          }
        } catch (parseError) {
          logger.warn(`Error analyzing show page: ${parseError.message}`);
        }
      }
    }

    // If show page approach didn't work, try the old URL format with the best match name
    // This is a fallback for older shows that might not use the new URL structure
    const bestMatchUrl = getEpisodeUrl(bestMatch.name, season, episode);
    logger.info(`Trying URL with Gemini AI match name: ${bestMatchUrl}`);
    const bestMatchHtml = await fetchHtml(bestMatchUrl);

    let subtitles = [];
    if (bestMatchHtml) {
      subtitles = extractSubtitles(bestMatchHtml, language);

      if (subtitles && subtitles.length > 0) {
        logger.info(`Found ${subtitles.length} subtitles using Gemini AI match name`);
        return subtitles;
      }
    }

    // Try with episode title if available
    const episodeTitle = getKnownEpisodeTitle(bestMatch.name, season, episode) ||
                         getKnownEpisodeTitle(showName, season, episode);

    if (episodeTitle) {
      logger.info(`Trying Gemini AI match with episode title: ${episodeTitle}`);
      const episodeUrl = getEpisodeUrl(bestMatch.name, season, episode, episodeTitle);
      logger.info(`Trying URL with episode title: ${episodeUrl}`);
      const episodeHtml = await fetchHtml(episodeUrl);

      if (episodeHtml) {
        subtitles = extractSubtitles(episodeHtml, language);

        if (subtitles && subtitles.length > 0) {
          logger.info(`Found ${subtitles.length} subtitles using Gemini AI match with episode title`);
          return subtitles;
        }
      }
    }

    // If we still haven't found subtitles, try with season 1 as a fallback
    if (season > 1) {
      logger.info(`No subtitles found for season ${season}, trying with season 1 as fallback`);
      const season1Url = getEpisodeUrl(bestMatch.name, 1, episode);
      logger.info(`Trying URL with season 1: ${season1Url}`);
      const season1Html = await fetchHtml(season1Url);

      if (season1Html) {
        subtitles = extractSubtitles(season1Html, language);

        if (subtitles && subtitles.length > 0) {
          logger.info(`Found ${subtitles.length} subtitles using season 1 fallback`);
          return subtitles;
        }
      }

      // Try with episode 1 as a last resort
      if (episode > 1) {
        logger.info(`No subtitles found for episode ${episode}, trying with episode 1 as fallback`);
        const episode1Url = getEpisodeUrl(bestMatch.name, season, 1);
        logger.info(`Trying URL with episode 1: ${episode1Url}`);
        const episode1Html = await fetchHtml(episode1Url);

        if (episode1Html) {
          subtitles = extractSubtitles(episode1Html, language);

          if (subtitles && subtitles.length > 0) {
            logger.info(`Found ${subtitles.length} subtitles using episode 1 fallback`);
            return subtitles;
          }
        }
      }
    }

    // If still no subtitles found, try the standard method as a last resort
    logger.warn(`No subtitles found for ${showName} (best match: ${bestMatch.name}) using Gemini AI, trying standard method`);
    return getSubtitles(showName, season, episode, language);
  } catch (error) {
    logger.error(`Error getting subtitles with Gemini AI: ${error.message}`);
    return getSubtitles(showName, season, episode, language);
  }
}

module.exports = {
  getSubtitles,
  getSubtitlesWithEpisodeTitle,
  getSubtitlesWithGeminiAI,
  extractSubtitles,
  fetchHtml,
  getEpisodeUrl,
  getShowUrl,
  findEpisodeLinkOnShowPage,
  generateAlternativeNames,
  getKnownEpisodeTitle
};
