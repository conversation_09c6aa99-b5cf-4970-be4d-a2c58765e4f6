import React, { useState } from 'react';
import { useApolloClient } from '@apollo/client';
import { GET_STREAM_URL } from '@/lib/queries';

/**
 * Props:
 * - episode: the episode object from media.episodes
 * - tmdbSeasons: array of TMDB seasons (media.tmdbSeasons)
 * - openPlayer: function to open the player
 * - mediaId: the media id
 * - mediaType: 'series' | 'anime'
 * - title: media title
 */
export default function EpisodeCard({ episode, tmdbSeasons, openPlayer, mediaId, mediaType, title }) {
  // Type-safe matching for TMDB season and episode
  let tmdbSeason = Array.isArray(tmdbSeasons)
    ? tmdbSeasons.find(
        s => Number(s.season_number) === Number(episode.season || 1)
      )
    : null;
  let tmdbEp = tmdbSeason && Array.isArray(tmdbSeason.episodes)
    ? tmdbSeason.episodes.find(
        e => Number(e.episode_number) === Number(episode.episodeNumber)
      )
    : null;

  // State for source stream URLs per stream
  const [sourceStates, setSourceStates] = useState({});
  const client = useApolloClient();

  // Fetch direct source URL for a stream
  const fetchSourceUrl = async (stream) => {
    console.log(`[FetchSource] Starting fetch for stream ${stream.id} (Provider: ${stream.provider})`);
    setSourceStates((prev) => ({ ...prev, [stream.id]: { loading: true, error: null } }));
    
    try {
      const { data } = await client.query({
        query: GET_STREAM_URL,
        variables: {
          itemId: mediaId,
          type: mediaType.toUpperCase(),
          streamId: stream.id,
        },
        fetchPolicy: 'network-only', // Always fetch fresh data
      });

      console.log(`[FetchSource] GraphQL response for stream ${stream.id}:`, data);
      
      const url = data?.stream?.sourceStreamUrl;
      const method = data?.stream?.method;
      const size = data?.stream?.size;
      const type = data?.stream?.type;

      if (url) {
        console.log(`[FetchSource] Direct source URL found for stream ${stream.id}:`, {
          url,
          method,
          size,
          type
        });
        
        setSourceStates((prev) => ({
          ...prev,
          [stream.id]: {
            url,
            method,
            size,
            type,
            loading: false,
            error: null,
            lastFetched: new Date().toISOString()
          }
        }));
      } else {
        console.warn(`[FetchSource] No direct source found for stream ${stream.id}`);
        setSourceStates((prev) => ({
          ...prev,
          [stream.id]: {
            loading: false,
            error: 'No direct source found. Try again later.',
            lastFetched: new Date().toISOString()
          }
        }));
      }
    } catch (err) {
      console.error(`[FetchSource] Error fetching direct source for stream ${stream.id}:`, err);
      setSourceStates((prev) => ({
        ...prev,
        [stream.id]: {
          loading: false,
          error: err.message || 'Error fetching source. Try again.',
          lastFetched: new Date().toISOString()
        }
      }));
    }
  };

  // Play direct source
  const playSource = (stream) => {
    const state = sourceStates[stream.id];
    if (state && state.url) {
      console.log(`[PlaySource] Playing direct source for stream ${stream.id}:`, {
        url: state.url,
        method: state.method,
        type: state.type,
        size: state.size
      });
      
      openPlayer({
        id: mediaId,
        type: mediaType || 'series',
        title: `${title} - Episode ${episode.episodeNumber}`,
        episodeNumber: episode.episodeNumber,
        streamId: stream.id,
        language: stream.language,
        directSourceUrl: state.url,
        method: state.method,
        size: state.size,
        type: state.type
      });
    } else {
      console.warn(`[PlaySource] No direct source URL to play for stream ${stream.id}`);
    }
  };

  // Debug logging for each render
  React.useEffect(() => {
    console.log('[EpisodeCard]', {
      episode,
      tmdbSeasons,
      tmdbSeason,
      tmdbEp,
      sourceStates
    });
  });

  return (
    <div className="bg-gray-800 rounded-lg overflow-hidden">
      {/* Episode Header */}
      <div className="p-4">
        <div className="flex items-start space-x-4">
          <div className="w-24 h-16 bg-gray-700 rounded overflow-hidden flex-shrink-0">
            {tmdbEp && tmdbEp.still_path ? (
              <img
                src={`https://image.tmdb.org/t/p/w300${tmdbEp.still_path}`}
                alt={`Episode ${episode.episodeNumber} still`}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <i className="fas fa-play-circle text-cyan-400 text-xl"></i>
              </div>
            )}
          </div>
          <div className="flex-1">
            <h3 className="text-white font-semibold flex items-center">
              <span className="text-cyan-400 mr-2">{episode.episodeNumber}</span>
              {tmdbEp && tmdbEp.name
                ? tmdbEp.name
                : episode.title || `Episode ${episode.episodeNumber}`}
            </h3>
            <div className="flex items-center space-x-4 text-sm text-gray-400 mt-1">
              {tmdbEp && tmdbEp.air_date && (
                <span className="flex items-center">
                  <i className="fas fa-calendar-alt text-cyan-400 mr-1"></i>
                  {new Date(tmdbEp.air_date).toLocaleDateString()}
                </span>
              )}
              {tmdbEp && tmdbEp.vote_average > 0 && (
                <span className="flex items-center">
                  <i className="fas fa-star text-yellow-400 mr-1"></i>
                  {tmdbEp.vote_average.toFixed(1)}/10
                </span>
              )}
              {episode.season && (
                <span>Season {episode.season}</span>
              )}
              {episode.language && (
                <span className="flex items-center">
                  <i className="fas fa-language text-cyan-400 mr-1"></i>
                  {episode.language}
                </span>
              )}
            </div>
            {tmdbEp && tmdbEp.overview && (
              <p className="text-gray-300 text-sm mt-2">
                {tmdbEp.overview}
              </p>
            )}
          </div>
        </div>
      </div>
      {/* Providers Section */}
      {episode.streamingUrls && episode.streamingUrls.length > 0 && (
        <div className="bg-gray-900 p-4 border-t border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {episode.streamingUrls.map((stream, streamIndex) => {
              const state = sourceStates[stream.id] || {};
              return (
                <div key={stream.id || streamIndex} className="bg-gray-800 rounded p-3 flex items-center justify-between gap-2">
                  <div className="flex-1">
                    <div className="text-cyan-400 font-medium flex items-center">
                      <i className="fas fa-tv mr-2"></i>
                      {stream.provider || `Source ${streamIndex + 1}`}
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      {stream.language && <span className="mr-2"><i className="fas fa-language mr-1"></i>{stream.language}</span>}
                      {stream.quality && <span><i className="fas fa-video mr-1"></i>{stream.quality}</span>}
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      openPlayer({
                        id: mediaId,
                        type: mediaType || 'series',
                        title: `${title} - Episode ${episode.episodeNumber}`,
                        episodeNumber: episode.episodeNumber,
                        streamId: stream.id,
                        language: stream.language,
                        streamMethod: 'iframe',
                        videoUrl: stream.url
                      })
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded text-sm transition-colors"
                  >
                    <i className="fas fa-play mr-1"></i>
                    Play
                  </button>
                  <button
                    onClick={() => state.url ? playSource(stream) : fetchSourceUrl(stream)}
                    className={`ml-2 px-3 py-1.5 rounded text-sm transition-colors ${state.url ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-700 hover:bg-gray-800 text-cyan-400'} ${state.loading ? 'opacity-50 cursor-wait' : ''}`}
                    disabled={state.loading}
                  >
                    {state.loading ? (
                      <span><i className="fas fa-spinner fa-spin mr-1"></i>Fetching...</span>
                    ) : state.url ? (
                      <span><i className="fas fa-link mr-1"></i>Play Source</span>
                    ) : (
                      <span><i className="fas fa-link mr-1"></i>Fetch Source</span>
                    )}
                  </button>
                  {state.error && (
                    <span className="text-red-500 text-xs ml-2">{state.error}</span>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
} 