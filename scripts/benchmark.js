// File: scripts/benchmark.js
// Performance benchmark script for Fastify migration
// Compares performance metrics and validates improvements

const autocannon = require('autocannon');
const { performance } = require('perf_hooks');

class NetStreamBenchmark {
  constructor() {
    this.results = {
      fastify: {},
      express: {},
      comparison: {}
    };
    
    this.testConfig = {
      duration: 30, // 30 seconds
      connections: 50,
      pipelining: 1,
      timeout: 10
    };
  }

  async runBenchmarks() {
    console.log('🚀 Starting NetStream Performance Benchmarks\n');
    
    try {
      // Test Fastify server
      console.log('📊 Testing Fastify Server...');
      await this.benchmarkFastifyServer();
      
      // Test Express server (if available)
      console.log('\n📊 Testing Express Server...');
      await this.benchmarkExpressServer();
      
      // Compare results
      console.log('\n📈 Generating Performance Comparison...');
      this.generateComparison();
      
      // Generate report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Benchmark failed:', error);
      process.exit(1);
    }
  }

  async benchmarkFastifyServer() {
    const fastifyPort = process.env.FASTIFY_PORT || 3001;
    const baseUrl = `http://localhost:${fastifyPort}`;
    
    console.log(`   Testing Fastify server at ${baseUrl}`);
    
    // Test different endpoints
    const endpoints = [
      { name: 'Health Check', url: '/health' },
      { name: 'API Info', url: '/api' },
      { name: 'GraphQL Query', url: '/graphql', method: 'POST', body: JSON.stringify({
        query: '{ databaseStats { totalItems } }'
      }), headers: { 'content-type': 'application/json' } },
      { name: 'Cache Stats', url: '/cache/stats' },
      { name: 'Performance', url: '/performance' }
    ];

    this.results.fastify = {};
    
    for (const endpoint of endpoints) {
      console.log(`     - ${endpoint.name}...`);
      
      const config = {
        url: `${baseUrl}${endpoint.url}`,
        duration: this.testConfig.duration,
        connections: this.testConfig.connections,
        pipelining: this.testConfig.pipelining,
        timeout: this.testConfig.timeout,
        ...endpoint
      };
      
      try {
        const result = await autocannon(config);
        this.results.fastify[endpoint.name] = this.processResult(result);
      } catch (error) {
        console.warn(`       Warning: ${endpoint.name} test failed:`, error.message);
        this.results.fastify[endpoint.name] = { error: error.message };
      }
    }
  }

  async benchmarkExpressServer() {
    const expressPort = process.env.EXPRESS_PORT || 3000;
    const baseUrl = `http://localhost:${expressPort}`;
    
    console.log(`   Testing Express server at ${baseUrl}`);
    
    // Check if Express server is running
    try {
      const response = await fetch(`${baseUrl}/health`);
      if (!response.ok) {
        throw new Error('Express server not responding');
      }
    } catch (error) {
      console.warn('   Express server not available, skipping comparison');
      this.results.express = { error: 'Server not available' };
      return;
    }

    // Test same endpoints as Fastify
    const endpoints = [
      { name: 'Health Check', url: '/health' },
      { name: 'GraphQL Query', url: '/graphql', method: 'POST', body: JSON.stringify({
        query: '{ databaseStats { totalItems } }'
      }), headers: { 'content-type': 'application/json' } }
    ];

    this.results.express = {};
    
    for (const endpoint of endpoints) {
      console.log(`     - ${endpoint.name}...`);
      
      const config = {
        url: `${baseUrl}${endpoint.url}`,
        duration: this.testConfig.duration,
        connections: this.testConfig.connections,
        pipelining: this.testConfig.pipelining,
        timeout: this.testConfig.timeout,
        ...endpoint
      };
      
      try {
        const result = await autocannon(config);
        this.results.express[endpoint.name] = this.processResult(result);
      } catch (error) {
        console.warn(`       Warning: ${endpoint.name} test failed:`, error.message);
        this.results.express[endpoint.name] = { error: error.message };
      }
    }
  }

  processResult(result) {
    return {
      requestsPerSecond: Math.round(result.requests.average),
      latency: {
        average: Math.round(result.latency.average),
        p50: Math.round(result.latency.p50),
        p90: Math.round(result.latency.p90),
        p99: Math.round(result.latency.p99)
      },
      throughput: Math.round(result.throughput.average),
      errors: result.errors,
      timeouts: result.timeouts,
      duration: result.duration
    };
  }

  generateComparison() {
    const comparison = {};
    
    // Compare common endpoints
    const commonEndpoints = Object.keys(this.results.fastify).filter(
      endpoint => this.results.express[endpoint] && !this.results.express[endpoint].error
    );

    for (const endpoint of commonEndpoints) {
      const fastify = this.results.fastify[endpoint];
      const express = this.results.express[endpoint];
      
      if (fastify.error || express.error) continue;
      
      comparison[endpoint] = {
        requestsPerSecond: {
          fastify: fastify.requestsPerSecond,
          express: express.requestsPerSecond,
          improvement: this.calculateImprovement(express.requestsPerSecond, fastify.requestsPerSecond)
        },
        latency: {
          fastify: fastify.latency.average,
          express: express.latency.average,
          improvement: this.calculateImprovement(express.latency.average, fastify.latency.average, true)
        },
        throughput: {
          fastify: fastify.throughput,
          express: express.throughput,
          improvement: this.calculateImprovement(express.throughput, fastify.throughput)
        }
      };
    }
    
    this.results.comparison = comparison;
  }

  calculateImprovement(oldValue, newValue, lowerIsBetter = false) {
    if (oldValue === 0) return 0;
    
    const improvement = ((newValue - oldValue) / oldValue) * 100;
    return lowerIsBetter ? -improvement : improvement;
  }

  generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('📊 NETSTREAM PERFORMANCE BENCHMARK REPORT');
    console.log('='.repeat(80));
    
    // Fastify Results
    console.log('\n🚀 FASTIFY PERFORMANCE:');
    this.printResults(this.results.fastify);
    
    // Express Results (if available)
    if (!this.results.express.error) {
      console.log('\n🐌 EXPRESS PERFORMANCE:');
      this.printResults(this.results.express);
    }
    
    // Comparison
    if (Object.keys(this.results.comparison).length > 0) {
      console.log('\n📈 PERFORMANCE COMPARISON:');
      this.printComparison();
    }
    
    // Summary
    console.log('\n📋 SUMMARY:');
    this.printSummary();
    
    // Save results
    this.saveResults();
    
    console.log('\n' + '='.repeat(80));
  }

  printResults(results) {
    for (const [endpoint, data] of Object.entries(results)) {
      if (data.error) {
        console.log(`   ${endpoint}: ❌ ${data.error}`);
        continue;
      }
      
      console.log(`   ${endpoint}:`);
      console.log(`     Requests/sec: ${data.requestsPerSecond.toLocaleString()}`);
      console.log(`     Avg Latency:  ${data.latency.average}ms`);
      console.log(`     P99 Latency:  ${data.latency.p99}ms`);
      console.log(`     Throughput:   ${(data.throughput / 1024 / 1024).toFixed(2)} MB/s`);
      console.log(`     Errors:       ${data.errors}`);
      console.log('');
    }
  }

  printComparison() {
    for (const [endpoint, data] of Object.entries(this.results.comparison)) {
      console.log(`   ${endpoint}:`);
      console.log(`     Requests/sec: ${this.formatImprovement(data.requestsPerSecond.improvement)}`);
      console.log(`     Latency:      ${this.formatImprovement(data.latency.improvement)}`);
      console.log(`     Throughput:   ${this.formatImprovement(data.throughput.improvement)}`);
      console.log('');
    }
  }

  formatImprovement(improvement) {
    const sign = improvement > 0 ? '+' : '';
    const color = improvement > 0 ? '🟢' : improvement < 0 ? '🔴' : '⚪';
    return `${color} ${sign}${improvement.toFixed(1)}%`;
  }

  printSummary() {
    const fastifyAvg = this.calculateAverageRPS(this.results.fastify);
    const expressAvg = this.calculateAverageRPS(this.results.express);
    
    console.log(`   Fastify Average RPS: ${fastifyAvg.toLocaleString()}`);
    
    if (expressAvg > 0) {
      console.log(`   Express Average RPS: ${expressAvg.toLocaleString()}`);
      const overallImprovement = this.calculateImprovement(expressAvg, fastifyAvg);
      console.log(`   Overall Improvement: ${this.formatImprovement(overallImprovement)}`);
    }
    
    console.log(`   Test Duration: ${this.testConfig.duration}s`);
    console.log(`   Connections: ${this.testConfig.connections}`);
  }

  calculateAverageRPS(results) {
    const validResults = Object.values(results).filter(r => !r.error && r.requestsPerSecond);
    if (validResults.length === 0) return 0;
    
    const total = validResults.reduce((sum, r) => sum + r.requestsPerSecond, 0);
    return Math.round(total / validResults.length);
  }

  saveResults() {
    const fs = require('fs');
    const path = require('path');
    
    const reportData = {
      timestamp: new Date().toISOString(),
      testConfig: this.testConfig,
      results: this.results,
      summary: {
        fastifyAvgRPS: this.calculateAverageRPS(this.results.fastify),
        expressAvgRPS: this.calculateAverageRPS(this.results.express)
      }
    };
    
    try {
      const reportPath = path.join(process.cwd(), 'benchmark-report.json');
      fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
      console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    } catch (error) {
      console.warn('Failed to save benchmark report:', error.message);
    }
  }
}

// Run benchmarks if called directly
if (require.main === module) {
  const benchmark = new NetStreamBenchmark();
  benchmark.runBenchmarks().catch(console.error);
}

module.exports = NetStreamBenchmark;
