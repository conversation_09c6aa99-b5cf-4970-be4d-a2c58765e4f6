// src/db/models/Config.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const logger = require('../../utils/logger');

const ConfigSchema = new Schema({
    key: { 
        type: String, 
        required: true,
        unique: true,
        index: true
    },
    value: { 
        type: String, 
        required: true 
    },
    lastUpdated: { 
        type: Date, 
        default: Date.now 
    }
}, {
    collection: 'config',
    timestamps: true
});

// Static method to get a config value
ConfigSchema.statics.getValue = async function(key, defaultValue = null) {
    try {
        const config = await this.findOne({ key });
        return config ? config.value : defaultValue;
    } catch (error) {
        logger.error(`Error getting config value for ${key}: ${error.message}`);
        return defaultValue;
    }
};

// Static method to set a config value
ConfigSchema.statics.setValue = async function(key, value) {
    try {
        const result = await this.findOneAndUpdate(
            { key },
            { value, lastUpdated: new Date() },
            { upsert: true, new: true }
        );
        logger.info(`Updated config ${key} to ${value}`);
        return result;
    } catch (error) {
        logger.error(`Error setting config value for ${key}: ${error.message}`);
        throw error;
    }
};

// Static method to get all config values
ConfigSchema.statics.getAllValues = async function() {
    try {
        const configs = await this.find({});
        return configs.reduce((acc, config) => {
            acc[config.key] = config.value;
            return acc;
        }, {});
    } catch (error) {
        logger.error(`Error getting all config values: ${error.message}`);
        return {};
    }
};

// Initialize the config with default values if they don't exist
ConfigSchema.statics.initializeDefaults = async function(defaults) {
    try {
        for (const [key, value] of Object.entries(defaults)) {
            await this.findOneAndUpdate(
                { key },
                { $setOnInsert: { key, value, lastUpdated: new Date() } },
                { upsert: true, new: false }
            );
        }
        logger.info('Config defaults initialized');
    } catch (error) {
        logger.error(`Error initializing config defaults: ${error.message}`);
    }
};

const Config = mongoose.model('Config', ConfigSchema);

module.exports = Config;
