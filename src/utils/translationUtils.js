// File: src/utils/translationUtils.js
const logger = require('./logger');
const { GoogleGenerativeAI } = require("@google/generative-ai");
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

// Import enrichment config to get API rate limit
const { getConfig } = require('../enrichment/config/enrichmentConfig');
const config = getConfig();

// Initialize Gemini model
let geminiModel = null;
let geminiQuotaExceeded = false;
let lastGeminiCallTime = 0;
const GEMINI_MIN_INTERVAL_MS = config.GEMINI_RATE_LIMIT_MS || 2000; // Default: 30 req/min = 2000ms

// Sleep function for rate limiting
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Initialize the Gemini model
 */
function initGemini() {
  if (!GEMINI_API_KEY) {
    logger.warn('Translation Utils: No Gemini API key provided. Translation features will be limited.');
    return;
  }

  try {
    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    geminiModel = genAI.getGenerativeModel({ model: "gemini-2.0-flash-lite" });
    logger.info('Translation Utils: Gemini AI initialized.');
  } catch (error) {
    logger.error(`Translation Utils: Failed to initialize Gemini AI: ${error.message}`);
  }
}

/**
 * Check if an error is related to Gemini quota
 * @param {Error} error - The error to check
 * @returns {boolean} - Whether the error is related to quota
 */
function isGeminiQuotaError(error) {
  return error.message?.includes('quota') ||
         error.message?.includes('rate limit') ||
         error.message?.includes('429') ||
         error.status === 429;
}

/**
 * Handle Gemini quota exceeded
 */
function handleGeminiQuotaExceeded() {
  geminiQuotaExceeded = true;
  logger.error('Translation Utils: Gemini API quota exceeded. Translation features will be limited.');
}

/**
 * Translate a title from French to English using Gemini AI
 * @param {string} frenchTitle - The French title to translate
 * @returns {Promise<string|null>} - The English translation or null if translation failed
 */
async function translateToEnglish(frenchTitle) {
  if (!frenchTitle) return null;
  if (!geminiModel) initGemini();
  if (!geminiModel || geminiQuotaExceeded) return null;

  // Rate limiting
  const now = Date.now();
  const elapsed = now - lastGeminiCallTime;
  if (lastGeminiCallTime > 0 && elapsed < GEMINI_MIN_INTERVAL_MS) {
    const wait = GEMINI_MIN_INTERVAL_MS - elapsed;
    logger.debug(`API rate limit: Waiting ${wait}ms before translation call...`);
    await sleep(wait);
  }

  const prompt = `Translate the following French title to English. Return ONLY the translated title without any explanation or additional text.

French title: "${frenchTitle}"

English translation:`;

  logger.info(`Translation Utils: Translating "${frenchTitle}" to English...`);

  try {
    lastGeminiCallTime = Date.now();
    const result = await geminiModel.generateContent(prompt);
    const response = await result.response;

    if (!response?.text) {
      logger.warn(`Translation Utils: Gemini returned no valid content for title '${frenchTitle}'. Block Reason: ${response?.promptFeedback?.blockReason || "N/A"}.`);
      return null;
    }

    const translation = response.text().trim();
    const cleanedTranslation = translation.replace(/^["']|["']$/g, "").trim();

    logger.info(`Translation Utils: Translated "${frenchTitle}" to "${cleanedTranslation}"`);
    return cleanedTranslation;
  } catch (error) {
    // Check for quota exceeded errors
    if (isGeminiQuotaError(error)) {
      handleGeminiQuotaExceeded();
      return null;
    }

    logger.error(`Translation Utils: Gemini API call failed translating '${frenchTitle}'. Status: ${error.status || "N/A"}, Message: ${error.message}`);
    return null;
  }
}

module.exports = {
  translateToEnglish,
  isGeminiAvailable: () => !!geminiModel && !geminiQuotaExceeded
};
