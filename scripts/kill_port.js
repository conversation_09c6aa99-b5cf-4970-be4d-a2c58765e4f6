// scripts/kill_port.js
// <PERSON>ript to find and kill processes using a specific port

const { exec } = require('child_process');
const logger = require('../src/utils/logger');

/**
 * Find and kill processes using a specific port
 * @param {number} port - The port to free up
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
async function killProcessOnPort(port) {
  return new Promise((resolve) => {
    logger.info(`Attempting to kill process using port ${port}...`);

    // Try different commands based on platform
    let commands = [];

    if (process.platform === 'win32') {
      // Windows commands
      commands = [
        `netstat -ano | findstr :${port}`,
        `for /f "tokens=5" %a in ('netstat -aon ^| findstr :${port} ^| findstr LISTENING') do taskkill /F /PID %a`
      ];
    } else {
      // Unix/Linux commands
      commands = [
        // Try lsof first
        `lsof -i :${port} | grep LISTEN`,
        // Direct kill command that doesn't require parsing
        `fuser -k ${port}/tcp`,
        // Alternative using ss (newer alternative to netstat)
        `ss -lptn 'sport = :${port}' | grep LISTEN | awk '{print $6}' | cut -d',' -f2 | cut -d'=' -f2 | xargs kill -9`
      ];
    }

    // Try the first command to find the PID
    exec(commands[0], (error, stdout) => {
      if (error || !stdout.trim()) {
        logger.warn(`No process found using port ${port} with first method, trying alternative methods...`);

        // Try direct kill commands
        if (commands.length > 1) {
          exec(commands[1], (directError, directStdout) => {
            if (directError) {
              logger.warn(`Alternative method 1 failed: ${directError.message}`);

              // Try the third command if available
              if (commands.length > 2) {
                exec(commands[2], (thirdError) => {
                  if (thirdError) {
                    logger.warn(`Alternative method 2 failed: ${thirdError.message}`);
                    resolve(false);
                  } else {
                    logger.info(`Successfully freed port ${port} using alternative method 2`);
                    resolve(true);
                  }
                });
              } else {
                resolve(false);
              }
            } else {
              logger.info(`Successfully freed port ${port} using alternative method 1`);
              resolve(true);
            }
          });
        } else {
          resolve(false);
        }
        return;
      }

      try {
        // Extract PID from the output
        let pid;
        if (process.platform === 'win32') {
          // Windows format: TCP    127.0.0.1:3001         0.0.0.0:0              LISTENING       12345
          const match = stdout.match(/LISTENING\s+(\d+)/);
          pid = match ? match[1] : null;
        } else {
          // Unix format: node    12345 user   17u  IPv6 123456      0t0  TCP *:3001 (LISTEN)
          const lines = stdout.split('\n').filter(line => line.trim());
          if (lines.length > 0) {
            const parts = lines[0].trim().split(/\s+/);
            pid = parts[1];
          }
        }

        if (!pid) {
          logger.warn(`Could not extract PID for port ${port}, trying alternative methods...`);

          // Try direct kill commands
          if (commands.length > 1) {
            exec(commands[1], (directError) => {
              if (directError) {
                logger.warn(`Alternative method failed: ${directError.message}`);
                resolve(false);
              } else {
                logger.info(`Successfully freed port ${port} using alternative method`);
                resolve(true);
              }
            });
          } else {
            resolve(false);
          }
          return;
        }

        // Kill the process
        const killCommand = process.platform === 'win32'
          ? `taskkill /F /PID ${pid}`
          : `kill -9 ${pid}`;

        exec(killCommand, (killError) => {
          if (killError) {
            logger.error(`Failed to kill process ${pid} on port ${port}: ${killError.message}`);

            // Try alternative methods
            if (commands.length > 1) {
              exec(commands[1], (directError) => {
                if (directError) {
                  logger.warn(`Alternative method failed: ${directError.message}`);
                  resolve(false);
                } else {
                  logger.info(`Successfully freed port ${port} using alternative method`);
                  resolve(true);
                }
              });
            } else {
              resolve(false);
            }
          } else {
            logger.info(`Successfully killed process ${pid} using port ${port}`);
            resolve(true);
          }
        });
      } catch (parseError) {
        logger.error(`Error parsing process info for port ${port}: ${parseError.message}`);

        // Try direct kill commands as fallback
        if (commands.length > 1) {
          exec(commands[1], (directError) => {
            if (directError) {
              logger.warn(`Alternative method failed: ${directError.message}`);
              resolve(false);
            } else {
              logger.info(`Successfully freed port ${port} using alternative method`);
              resolve(true);
            }
          });
        } else {
          resolve(false);
        }
      }
    });
  });
}

// If this script is run directly
if (require.main === module) {
  const port = process.argv[2] || 3001;

  killProcessOnPort(port)
    .then(success => {
      if (success) {
        console.log(`Successfully freed up port ${port}`);
        process.exit(0);
      } else {
        console.log(`Could not free up port ${port}`);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error(`Error: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { killProcessOnPort };
