// OmniDev reporting for duty.

// Purpose: Reads JSON files containing extracted OneUpload URLs, extracts file codes,
// and uses the OneUpload API to clone these files into the user's account.
// Tracks successfully cloned files to avoid duplicates on re-runs (unless forced).

// Usage:
//   node scripts/cloneOneUploadFiles.js                  # Process all JSONs in input dir, appends state
//   node scripts/cloneOneUploadFiles.js --Movie          # Process only output_movies.json, appends state
//   node scripts/cloneOneUploadFiles.js --fldId 12345    # Clone into specific folder ID, appends state
//   node scripts/cloneOneUploadFiles.js --forceClone     # Re-clones files even if previously logged
//   node scripts/cloneOneUploadFiles.js --inputDir ./results # Specify input JSON directory

// ==================================================================================
//                              DEPENDENCIES
// ==================================================================================
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
// const https = require('https'); // Not strictly needed if not using custom agent, fetch handles HTTPS
const dotenv = require('dotenv');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
// Assuming logger.js is in ../src/utils relative to this script file
const logger = require('../src/utils/logger.js'); // Adjust path if needed

// ==================================================================================
//                              CONFIGURATION
// ==================================================================================
dotenv.config({ path: path.resolve(__dirname, '../.env') }); // Load .env from parent dir

const { ONEUPLOAD_API_KEY } = process.env;

// --- Script Configuration ---
const CLONE_API_URL = 'https://oneupload.to/api/file/clone';
const DELAY_BETWEEN_CLONES_MS = 1000; // 1 second delay between API calls (adjust if needed)
const STATE_FILE_NAME = 'cloned_files.log'; // Simple log file to track cloned original file_codes

// --- Command Line Arguments ---
const argv = yargs(hideBin(process.argv))
    .option('Movie', { type: 'boolean', description: 'Process output_movies.json' })
    .option('Series', { type: 'boolean', description: 'Process output_series.json' })
    .option('Anime', { type: 'boolean', description: 'Process output_anime.json' })
    .option('inputDir', { type: 'string', default: './provider_url_output', description: 'Directory containing JSON input files' })
    .option('fldId', { type: 'number', description: 'Target Folder ID in your OneUpload account (optional)' })
    .option('forceClone', { type: 'boolean', default: false, description: 'Clone files even if logged as previously cloned.' })
    .help().alias('h', 'help')
    .argv;

// Determine which JSON files to process
const processAll = !argv.Movie && !argv.Series && !argv.Anime;
const filesToProcessConfig = [];
const baseInputDir = path.resolve(argv.inputDir);
const stateFilePath = path.join(baseInputDir, STATE_FILE_NAME); // Place state file in input dir

if (processAll || argv.Movie) filesToProcessConfig.push({ name: 'Movie', jsonFile: path.join(baseInputDir, 'output_movies.json') });
if (processAll || argv.Series) filesToProcessConfig.push({ name: 'Series', jsonFile: path.join(baseInputDir, 'output_series.json') });
if (processAll || argv.Anime) filesToProcessConfig.push({ name: 'Anime', jsonFile: path.join(baseInputDir, 'output_anime.json') });
// Add other types (e.g., LiveTV) here if needed

const TARGET_FOLDER_ID = argv.fldId;
const FORCE_CLONE = argv.forceClone;

// ==================================================================================
//                              UTILITY FUNCTIONS
// ==================================================================================
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// *** ADDED MISSING FUNCTION DEFINITION ***
/**
 * Ensures the specified directory exists. Creates it recursively if not.
 * Exits the process if creation fails.
 * @param {string} dirPath The path to the directory.
 */
const ensureDirectoryExists = (dirPath) => {
    if (!fs.existsSync(dirPath)) {
        logger.info(`Directory '${dirPath}' does not exist. Creating...`);
        try {
            fs.mkdirSync(dirPath, { recursive: true });
            logger.info(`Directory created: ${dirPath}`);
        } catch (error) {
            logger.error(`Failed to create directory '${dirPath}': ${error.message}`);
            process.exit(1); // Exit if we can't create the directory needed for state/output
        }
    } else {
        logger.debug(`Directory already exists: ${dirPath}`);
    }
};
// *** END OF ADDED FUNCTION ***


/**
 * Extracts the OneUpload file code from a URL.
 * Handles URLs like https://oneupload.to/w4jmnnpp71fn or https://oneupload.to/embed-w4jmnnpp71fn
 * @param {string} url The OneUpload URL
 * @returns {string|null} The file code or null if not found.
 */
const extractFileCode = (url) => { /* ... (unchanged) ... */
    if (!url) return null;
    try {
        const urlObj = new URL(url);
        if (!urlObj.hostname.includes('oneupload.to')) {
            logger.warn(`URL ${url} does not seem to be a OneUpload URL.`);
            return null;
        }
        const pathParts = urlObj.pathname.split('/').filter(part => part !== '');
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart) { return lastPart.startsWith('embed-') ? lastPart.substring(6) : lastPart; }
    } catch (e) { logger.error(`Error parsing URL ${url} to extract file code: ${e.message}`); }
    return null;
};

/**
 * Loads the set of already cloned original file codes from the state file.
 * @param {string} filePath Path to the state file.
 * @returns {Set<string>}
 */
const loadClonedState = (filePath) => { /* ... (unchanged) ... */
    const clonedCodes = new Set();
    if (!fs.existsSync(filePath)) { logger.info(`State file ${filePath} not found. Assuming no files cloned previously.`); return clonedCodes; }
    try {
        const lines = fs.readFileSync(filePath, 'utf-8').split('\n');
        for (const line of lines) { const code = line.trim(); if (code) { clonedCodes.add(code); } }
        logger.info(`Loaded ${clonedCodes.size} previously cloned file codes from ${filePath}`);
    } catch (error) { logger.error(`Error reading state file ${filePath}: ${error.message}`); }
    return clonedCodes;
};

/**
 * Appends a successfully cloned original file code to the state file.
 * @param {string} filePath Path to the state file.
 * @param {string} originalFileCode The file code that was successfully cloned.
 */
const appendClonedState = (filePath, originalFileCode) => { /* ... (unchanged) ... */
    if (!originalFileCode) return;
    logger.debug(`Appending ${originalFileCode} to state file ${filePath}`);
    try { fs.appendFileSync(filePath, `${originalFileCode}\n`, 'utf-8'); }
    catch (error) { logger.error(`Error appending to state file ${filePath}: ${error.message}`); }
};

/**
 * Performs the API call to clone a file.
 * @param {string} apiKey
 * @param {string} fileCodeToClone
 * @param {number|undefined} targetFolderId
 * @returns {Promise<{success: boolean, data: object|null, error: string|null}>}
 */
const cloneFileApiCall = async (apiKey, fileCodeToClone, targetFolderId) => { /* ... (unchanged) ... */
    const params = new URLSearchParams({ key: apiKey, file_code: fileCodeToClone, });
    if (targetFolderId !== undefined && targetFolderId !== null) { params.append('fld_id', targetFolderId.toString()); }
    const url = `${CLONE_API_URL}?${params.toString()}`;
    logger.info(`Attempting to clone file code: ${fileCodeToClone} ${targetFolderId ? `into folder ${targetFolderId}`: ''}...`);
    logger.debug(`API URL: ${url}`);
    try {
        const response = await fetch(url, { method: 'GET' });
        if (!response.ok) {
            logger.error(`API call failed for ${fileCodeToClone}. Status: ${response.status} ${response.statusText}`);
            try { const errorBody = await response.json(); logger.error(`API Error Body: ${JSON.stringify(errorBody)}`); return { success: false, data: null, error: `HTTP ${response.status}: ${errorBody.msg || response.statusText}` }; } catch (e) { /* Ignore */ }
            return { success: false, data: null, error: `HTTP ${response.status} ${response.statusText}` };
        }
        const data = await response.json(); logger.debug(`API Response for ${fileCodeToClone}: ${JSON.stringify(data)}`);
        if (data.status === 200 && data.msg === "OK" && data.result?.filecode) { logger.info(` -> SUCCESS: Cloned ${fileCodeToClone} -> New file code: ${data.result.filecode}, URL: ${data.result.url}`); return { success: true, data: data.result, error: null }; }
        else { logger.warn(` -> FAILED: Cloning ${fileCodeToClone}. API Message: ${data.msg || 'Unknown error'}`); return { success: false, data: null, error: `API Error: ${data.msg || 'Unknown error'}` }; }
    } catch (error) { logger.error(`Network or other error during API call for ${fileCodeToClone}: ${error.message}`); return { success: false, data: null, error: error.message }; }
};

// ==================================================================================
//                              CORE PROCESSING LOGIC (Unchanged)
// ==================================================================================

/**
 * Reads a JSON file and processes each entry to clone the file via API.
 * @param {object} config - Config object { name, jsonFile }
 * @param {Set<string>} alreadyClonedSet - Set of original file codes already cloned.
 */
async function processJsonFile(config, alreadyClonedSet) { /* ... (unchanged) ... */
    const { name, jsonFile } = config;
    logger.info(`\n--- Processing JSON file for ${name}: ${jsonFile} ---`);
    if (!fs.existsSync(jsonFile)) { logger.warn(`Input file not found: ${jsonFile}. Skipping.`); return; }
    let dataArray = [];
    try {
        const fileContent = fs.readFileSync(jsonFile, 'utf-8'); if (fileContent.trim() === '') { logger.warn(`Input file ${jsonFile} is empty. Skipping.`); return; }
        dataArray = JSON.parse(fileContent); if (!Array.isArray(dataArray)) { logger.error(`Content of ${jsonFile} is not a JSON array. Skipping.`); return; }
        logger.info(`Found ${dataArray.length} entries in ${jsonFile}`);
    } catch (error) { logger.error(`Error reading or parsing JSON file ${jsonFile}: ${error.message}`); return; }
    let successCount = 0, skippedCount = 0, errorCount = 0;
    for (const item of dataArray) {
        const originalUrl = item?.processedUrl?.originalUrl; const extractedUrl = item?.processedUrl?.extractedProviderUrl;
        if (!extractedUrl) { logger.debug(`Skipping item with missing extractedProviderUrl (Doc ID: ${item?.documentId})`); continue; }
        const fileCodeToClone = extractFileCode(extractedUrl);
        if (!fileCodeToClone) { logger.warn(`Could not extract file code from URL: ${extractedUrl}. Skipping.`); continue; }
        if (!FORCE_CLONE && alreadyClonedSet.has(fileCodeToClone)) { logger.debug(`Skipping already cloned file code: ${fileCodeToClone} (Original URL: ${originalUrl})`); skippedCount++; continue; }
        const cloneResult = await cloneFileApiCall(ONEUPLOAD_API_KEY, fileCodeToClone, TARGET_FOLDER_ID);
        if (cloneResult.success) { successCount++; if (!FORCE_CLONE) { appendClonedState(stateFilePath, fileCodeToClone); alreadyClonedSet.add(fileCodeToClone); } }
        else { errorCount++; }
        logger.debug(`Waiting ${DELAY_BETWEEN_CLONES_MS}ms...`); await sleep(DELAY_BETWEEN_CLONES_MS);
    }
    logger.info(`--- Finished processing ${name} ---`); logger.info(`Summary: Cloned: ${successCount}, Skipped (already cloned): ${skippedCount}, Errors: ${errorCount}`);
}


// ==================================================================================
//                                  MAIN EXECUTION (Unchanged)
// ==================================================================================

async function main() {
    console.log("==========================================================");
    console.log("          Starting OneUpload File Cloning Script          ");
    console.log("==========================================================");

    if (!ONEUPLOAD_API_KEY) {
        logger.error("ONEUPLOAD_API_KEY not found in environment variables. Please check your .env file.");
        process.exit(1);
    }

    if (filesToProcessConfig.length === 0) {
        logger.warn("No JSON input files specified for processing. Use flags like --Movie, --Series, or run without flags for all.");
        console.log("==========================================================");
        return;
    }

    const mode = FORCE_CLONE ? "FORCE CLONE (Ignoring State)" : "STANDARD (Checking State)";
    logger.info(`RUNNING IN ${mode} MODE`);
    logger.info(`Processing JSON files for: [${filesToProcessConfig.map(c => c.name).join(', ')}]`);
    logger.info(`Input directory: ${baseInputDir}`);
    if (TARGET_FOLDER_ID) {
        logger.info(`Target Folder ID: ${TARGET_FOLDER_ID}`);
    }
    logger.info(`State file: ${stateFilePath}`);

    // *** ensureDirectoryExists CALL WAS MISSING ***
    ensureDirectoryExists(baseInputDir); // Ensure dir exists for state file

    // Load existing cloned state (unless forcing)
    let alreadyClonedSet = new Set();
    if (!FORCE_CLONE) {
        alreadyClonedSet = loadClonedState(stateFilePath);
    } else {
        logger.info("Force clone enabled, ignoring previous state.");
        // Optionally, clear the state file if forcing a complete re-clone from scratch
        // try { if (fs.existsSync(stateFilePath)) fs.unlinkSync(stateFilePath); logger.info("Cleared existing state file due to --forceClone."); }
        // catch (e) { logger.error(`Could not clear state file ${stateFilePath}: ${e.message}`); }
    }

    // Process each specified JSON file
    for (const config of filesToProcessConfig) {
        await processJsonFile(config, alreadyClonedSet);
    }

    logger.info("\n--- All specified JSON files processed ---");

    console.log("==========================================================");
    console.log("                     Script Finished                      ");
    console.log("==========================================================");
}

// Run the main function
main().catch(error => {
    logger.error(`Unhandled error in main execution: ${error.message}`);
    logger.error(error.stack);
    process.exit(1); // Exit on unhandled error
});