'use client'
import { useState, useEffect, useCallback } from 'react'
import toast from 'react-hot-toast'

export function useWishlist() {
  const [wishlistItems, setWishlistItems] = useState([])
  const [groupedWishlist, setGroupedWishlist] = useState({
    movies: [],
    series: [],
    anime: []
  })

  // Load wishlist from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('wishlist')
      if (saved) {
        try {
          const items = JSON.parse(saved)
          setWishlistItems(items)
          groupItems(items)
        } catch (error) {
          console.error('Error loading wishlist:', error)
        }
      }
    }
  }, [])

  const groupItems = useCallback((items) => {
    const grouped = {
      movies: items.filter(item => item.type === 'movie'),
      series: items.filter(item => item.type === 'series'),
      anime: items.filter(item => item.type === 'anime')
    }
    setGroupedWishlist(grouped)
  }, [])

  const saveToStorage = useCallback((items) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('wishlist', JSON.stringify(items))
    }
  }, [])

  const addToWishlist = useCallback(async (item) => {
    try {
      const newItem = {
        id: item.id,
        title: item.title || item.displayTitle,
        thumbnail: item.thumbnail || item.image,
        type: item.type,
        year: item.year || item.metadata?.year,
        addedAt: new Date().toISOString()
      }

      setWishlistItems(prev => {
        const updated = [...prev, newItem]
        saveToStorage(updated)
        groupItems(updated)
        return updated
      })

      toast.success(`Added "${newItem.title}" to wishlist`)
    } catch (error) {
      console.error('Error adding to wishlist:', error)
      toast.error('Failed to add to wishlist')
    }
  }, [saveToStorage, groupItems])

  const removeFromWishlist = useCallback(async (id, type) => {
    try {
      setWishlistItems(prev => {
        const updated = prev.filter(item => !(item.id === id && item.type === type))
        saveToStorage(updated)
        groupItems(updated)
        return updated
      })

      toast.success('Removed from wishlist')
    } catch (error) {
      console.error('Error removing from wishlist:', error)
      toast.error('Failed to remove from wishlist')
    }
  }, [saveToStorage, groupItems])

  const isInWishlist = useCallback((id, type) => {
    return wishlistItems.some(item => item.id === id && item.type === type)
  }, [wishlistItems])

  const clearWishlist = useCallback(() => {
    setWishlistItems([])
    setGroupedWishlist({ movies: [], series: [], anime: [] })
    if (typeof window !== 'undefined') {
      localStorage.removeItem('wishlist')
    }
    toast.success('Wishlist cleared')
  }, [])

  return {
    wishlistItems,
    groupedWishlist,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    clearWishlist
  }
}
