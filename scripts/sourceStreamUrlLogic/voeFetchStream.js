const axios = require('axios');
const fs = require('fs');

/**
 * Fetches direct video URL from VOE providers by checking for Base64-encoded URLs or using the VOE API.
 */
async function fetchVoeStream(inputUrl, config, cookies, debug = false, customLogStep, customLogError) { // **Accept customLogStep, customLogError**
    let html = cookies.eResponse.data;
    let finalUrl = cookies.eResponse.request.res.responseUrl || inputUrl;
    customLogStep(`[VOE] Processing URL: ${inputUrl} -> ${finalUrl}`); // Use customLogStep
    if (inputUrl !== finalUrl) {
        customLogStep(`[VOE] Redirect detected: ${inputUrl} -> ${finalUrl}`); // Use customLogStep
    }

    const redirectedDomain = finalUrl.match(/https?:\/\/([^\/]+)/)?.[1];
    if (redirectedDomain && config.altDomains?.includes(redirectedDomain)) {
        config.baseUrl = `https://${redirectedDomain}`;
        config.headers['Referer'] = config.baseUrl + '/';
        customLogStep(`[VOE] Updated baseUrl due to redirect: ${config.baseUrl}`); // Use customLogStep
        customLogStep(`[VOE] baseUrl updated`, { newBaseUrl: config.baseUrl, redirectedDomain }); // Use customLogStep
    }

    const sourcePatterns = [
        /"hls":\s*"([^"]+)"/,
        /"mp4":\s*"([^"]+)"/,
        /"m3u8":\s*"([^"]+)"/,
        /file:\s*["']([^"']+\.(?:mp4|m3u8))["']/i,
        /src:\s*["']([^"']+\.(?:mp4|m3u8))["']/i,
        /['"]?(?:hls|mp4|m3u8)['"]?:\s*["']([^"']+)["']/i,
        /(https?:\/\/[^\s"']+\.(?:mp4|m3u8)[^\s"']*)/i
    ];

    for (const pattern of sourcePatterns) {
        const sourceMatch = html.match(pattern);
        if (sourceMatch) {
            let directUrl = sourceMatch[1].replace(/\\\//g, '/');
            if (/^[A-Za-z0-9+/=]+$/.test(directUrl) && directUrl.length > 50) {
                try {
                    directUrl = Buffer.from(directUrl, 'base64').toString('utf8');
                    customLogStep(`[VOE] Decoded Base64 URL: ${sourceMatch[1]} -> ${directUrl}`); // Use customLogStep
                } catch (e) {
                    customLogError(`[VOE] Base64 decoding failed: ${e.message}`); // Use customLogError
                    console.error(`[VOE] Base64 decoding failed: ${e.message}`); // Keep console.error for dev visibility
                    continue;
                }
            }
            customLogStep(`[VOE] Extracted direct source URL: ${directUrl}`); // Use customLogStep
            return directUrl;
        }
    }

    const id = finalUrl.split('/e/')[1];
    if (!id) throw new Error('Could not extract video ID from URL');
    const apiUrl = `${config.baseUrl}/api/source/${id}`;
    customLogStep(`[VOE] Fetching API: ${apiUrl}`); // Use customLogStep

    try {
        const apiResponse = await axios.post(apiUrl, { r: finalUrl, d: redirectedDomain || 'magasavor.net' }, {
            headers: { ...config.headers, 'Cookie': cookies.cookieHeader, 'Content-Type': 'application/x-www-form-urlencoded' },
            timeout: 10000
        });
        const data = apiResponse.data;
        if (data.success && data.data && data.data.length > 0) {
            const directUrl = data.data[0].file;
            customLogStep(`[VOE] Extracted from API: ${directUrl}`); // Use customLogStep
            return directUrl;
        }
    } catch (e) {
        customLogError(`[VOE] API request failed: ${e.message}`); // Use customLogError
        console.error(`[VOE] API request failed: ${e.message}`); // Keep console.error for dev visibility
    }

    if (debug) {
        const debugFile = `voe_debug_${Date.now()}.html`;
        fs.writeFileSync(debugFile, html);
        customLogStep(`[VOE] Debug HTML saved to: ${debugFile}`); // Use customLogStep
    }
    throw new Error(`No video source found in embed page or API${debug ? ` - check ${debugFile}` : ''}`);
}

module.exports = fetchVoeStream;