'use client'
import { createContext, useContext, useState, useEffect } from 'react'

const UserContext = createContext()

export function UserProvider({ children }) {
  const [user, setUser] = useState(null)
  const [preferences, setPreferences] = useState({
    theme: 'dark',
    language: 'en',
    autoplay: true,
    quality: 'auto'
  })

  useEffect(() => {
    // Load user preferences from localStorage
    if (typeof window !== 'undefined') {
      const savedPreferences = localStorage.getItem('userPreferences')
      if (savedPreferences) {
        setPreferences(JSON.parse(savedPreferences))
      }
    }
  }, [])

  const updatePreferences = (newPreferences) => {
    const updated = { ...preferences, ...newPreferences }
    setPreferences(updated)
    if (typeof window !== 'undefined') {
      localStorage.setItem('userPreferences', JSON.stringify(updated))
    }
  }

  const value = {
    user,
    setUser,
    preferences,
    updatePreferences
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (!context) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
