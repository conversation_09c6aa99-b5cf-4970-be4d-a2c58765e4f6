"use client"
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useWishlist } from '@/hooks/useWishlist'
import WishlistCarousels from '@/components/features/WishlistCarousels'

// Share the same cache across components
let userCache = null
let lastFetchTime = 0
const CACHE_DURATION = 60000 // 1 minute cache

export default function ProfilePage() {
  const [user, setUser] = useState(userCache)
  const [loading, setLoading] = useState(!userCache)
  const [ipAddress, setIpAddress] = useState('')
  const [selectedIcon, setSelectedIcon] = useState(null)
  const [email, setEmail] = useState(userCache?.email || '')
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [updateStatus, setUpdateStatus] = useState('')
  const router = useRouter()
  const { groupedWishlist } = useWishlist()

  useEffect(() => {
    async function fetchUser() {
      // Check if we have a valid cache
      const now = Date.now()
      if (userCache && now - lastFetchTime < CACHE_DURATION) {
        setUser(userCache)
        setEmail(userCache.email || '')
        setLoading(false)
        return
      }

      try {
        const res = await fetch('/api/auth/[...auth]', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'me' }),
          credentials: 'include'
        })
        const data = await res.json()
        if (data.user) {
          userCache = data.user
          lastFetchTime = now
          setUser(data.user)
          setEmail(data.user.email || '')
        }
      } catch (error) {
        console.error('Error fetching user:', error)
        userCache = null
      } finally {
        setLoading(false)
      }
    }
    fetchUser()
  }, [])

  useEffect(() => {
    async function fetchIpAddress() {
      try {
        const res = await fetch('https://api.ipify.org?format=json')
        const data = await res.json()
        setIpAddress(data.ip)
      } catch (error) {
        console.error('Failed to fetch IP address:', error)
        setIpAddress('Unavailable')
      }
    }
    fetchIpAddress()
  }, [])

  const handleLogout = async () => {
    document.cookie = 'token=; Max-Age=0; path=/;'
    userCache = null // Clear cache on logout
    lastFetchTime = 0
    router.replace('/login')
  }

  const handleIconSelect = async (icon) => {
    setSelectedIcon(icon)
    try {
      const res = await fetch('/api/auth/[...auth]', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'updateProfileIcon', icon }),
        credentials: 'include'
      })
      const data = await res.json()
      if (data.success) {
        userCache = data.user // Update cache after successful change
        lastFetchTime = Date.now()
        setUpdateStatus('Profile icon updated successfully.')
      } else {
        setUpdateStatus(data.error || 'Failed to update profile icon.')
      }
    } catch (error) {
      console.error('Error updating icon:', error)
      setUpdateStatus('Error updating profile icon.')
    }
  }

  const handleEmailUpdate = async () => {
    if (!email) {
      setUpdateStatus('Email is required.')
      return
    }
    try {
      const res = await fetch('/api/auth/[...auth]', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'updateEmail', email }),
        credentials: 'include'
      })
      const data = await res.json()
      if (data.success) {
        userCache = data.user // Update cache after successful change
        lastFetchTime = Date.now()
        setUser(data.user)
        setUpdateStatus('Email updated successfully.')
      } else {
        setUpdateStatus(data.error || 'Failed to update email.')
      }
    } catch (error) {
      console.error('Error updating email:', error)
      setUpdateStatus('Error updating email.')
    }
  }

  const handlePasswordUpdate = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      setUpdateStatus('All password fields are required.')
      return
    }
    if (newPassword !== confirmPassword) {
      setUpdateStatus('New passwords do not match.')
      return
    }
    try {
      const res = await fetch('/api/auth/[...auth]', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'updatePassword', currentPassword, newPassword }),
        credentials: 'include'
      })
      const data = await res.json()
      if (data.success) {
        setUpdateStatus('Password updated successfully.')
        setCurrentPassword('')
        setNewPassword('')
        setConfirmPassword('')
      } else {
        setUpdateStatus(data.error || 'Failed to update password.')
      }
    } catch (error) {
      console.error('Error updating password:', error)
      setUpdateStatus('Error updating password.')
    }
  }

  if (loading) return <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">Loading...</div>

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold mb-8 text-center">User Profile</h2>
        {user ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1 bg-gray-800 p-6 rounded-lg">
              <div className="flex flex-col items-center mb-4">
                {selectedIcon || user.profileIcon ? (
                  <div className="w-24 h-24 rounded-full bg-gray-700 flex items-center justify-center mb-2">
                    <i className={`${selectedIcon || user.profileIcon} text-4xl text-white`}></i>
                  </div>
                ) : (
                  <div className="w-24 h-24 rounded-full bg-gray-700 flex items-center justify-center mb-2">
                    <span className="text-xl">{user.name?.charAt(0) || 'U'}</span>
                  </div>
                )}
                <div className="mt-2 text-sm text-gray-400">
                  <p className="mb-1">Select an Icon:</p>
                  <div className="grid grid-cols-3 gap-2">
                    {['fas fa-user', 'fas fa-cat', 'fas fa-dog', 'fas fa-fish', 'fas fa-crow', 'fas fa-hippo'].map(icon => (
                      <button
                        key={icon}
                        onClick={() => handleIconSelect(icon)}
                        className={`p-2 rounded-full ${selectedIcon === icon ? 'bg-blue-600' : 'bg-gray-700'} hover:bg-gray-500`}
                      >
                        <i className={`${icon} text-white`}></i>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
              <div className="text-center mb-4">
                <div className="mb-2"><strong>Name:</strong> {user.name}</div>
                <div className="mb-2"><strong>Email:</strong> {user.email}</div>
                <div className="mb-2"><strong>Role:</strong> {user.role}</div>
                <div className="mb-2"><strong>IP Address:</strong> {ipAddress || 'Loading...'}</div>
              </div>
              <button onClick={handleLogout} className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors">Logout</button>
            </div>
            <div className="md:col-span-2 bg-gray-800 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4">Update Information</h3>
              {updateStatus && (
                <div className={`mb-4 p-2 rounded ${updateStatus.includes('success') ? 'bg-green-700' : 'bg-red-700'}`}>
                  {updateStatus}
                </div>
              )}
              <div className="mb-6">
                <h4 className="text-lg font-medium mb-2">Change Email</h4>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="New Email"
                  className="w-full p-2 mb-2 bg-gray-700 rounded text-white"
                />
                <button
                  onClick={handleEmailUpdate}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
                >
                  Update Email
                </button>
              </div>
              <div className="mb-6">
                <h4 className="text-lg font-medium mb-2">Change Password</h4>
                <input
                  type="password"
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  placeholder="Current Password"
                  className="w-full p-2 mb-2 bg-gray-700 rounded text-white"
                />
                <input
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="New Password"
                  className="w-full p-2 mb-2 bg-gray-700 rounded text-white"
                />
                <input
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm New Password"
                  className="w-full p-2 mb-2 bg-gray-700 rounded text-white"
                />
                <button
                  onClick={handlePasswordUpdate}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
                >
                  Update Password
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-red-500 text-center">User not found or not logged in.</div>
        )}
        {user && Object.values(groupedWishlist).some(items => items.length > 0) && (
          <div className="mt-8">
            <h3 className="text-2xl font-bold mb-4">My Wishlist</h3>
            <WishlistCarousels />
          </div>
        )}
      </div>
    </div>
  )
}
