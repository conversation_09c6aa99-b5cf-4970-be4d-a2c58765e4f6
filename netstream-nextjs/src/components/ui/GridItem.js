'use client'
import { useState } from 'react'
import Link from 'next/link'
import { getThumbnailUrl, getTitleWithState, formatRating, getYear } from '@/utils/helpers'
import WishlistButton from '@/components/features/WishlistButton'

export default function GridItem({ item, type }) {
  const [imageError, setImageError] = useState(false)

  const imageUrl = getThumbnailUrl(item)
  const title = getTitleWithState(item, type)
  const rating = formatRating(item)
  const year = getYear(item)

  // Ensure URL uses plural form for routing
  const getUrlType = (type) => {
    const lowerType = type.toLowerCase()
    if (lowerType === 'movie') return 'movies'
    if (lowerType === 'serie') return 'series'
    if (lowerType === 'anima') return 'anime'
    return lowerType // Already plural or other types like 'livetv'
  }

  return (
    <Link href={`/${getUrlType(type)}/${item.id}`}>
      <div className="group cursor-pointer">
        <div className="relative overflow-hidden rounded-lg bg-gray-800 aspect-[2/3]">
          <img
            src={imageError ? (item.__typename?.toLowerCase() === 'anime' ? '/default-banner.jpg' : '/default-thumbnail.jpg') : imageUrl}
            alt={title}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            loading="lazy"
            onError={() => setImageError(true)}
          />
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 flex items-center justify-center">
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <i className="fas fa-play text-white text-2xl"></i>
            </div>
          </div>

          {/* Rating Badge */}
          {rating !== 'N/A' && (
            <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-yellow-400 px-2 py-1 rounded text-xs font-semibold">
              <i className="fas fa-star mr-1"></i>
              {rating}
            </div>
          )}

          {/* Wishlist Button */}
          <div className="absolute top-2 right-2">
            <WishlistButton item={item} type={type} />
          </div>
        </div>

        {/* Info */}
        <div className="mt-2">
          <h3 
            className="font-semibold text-white text-sm line-clamp-2 group-hover:text-blue-400 transition-colors"
            dangerouslySetInnerHTML={{ __html: title }}
          />
          <div className="flex items-center justify-between mt-1">
            <span className="text-gray-400 text-xs">{year}</span>
            <span className="text-gray-500 text-xs capitalize">{type}</span>
          </div>
        </div>
      </div>
    </Link>
  )
}
