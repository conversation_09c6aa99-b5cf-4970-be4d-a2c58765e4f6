'use client'
import { useEffect, useState } from 'react'
import { useQuery } from '@apollo/client'
import { GET_ITEM_BY_ID } from '@/lib/queries'

export function EditItemModal({ itemId, itemType, isOpen, onClose, onSave }) {
  const [editedItem, setEditedItem] = useState(null)
  const { data, loading, error } = useQuery(GET_ITEM_BY_ID, {
    variables: { id: itemId, type: itemType?.toUpperCase() },
    skip: !itemId || !itemType
  })

  useEffect(() => {
    if (data?.item) {
      setEditedItem(data.item)
    }
  }, [data])

  if (!isOpen) return null
  if (loading) return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 p-6 rounded-lg">
        <p className="text-white">Loading...</p>
      </div>
    </div>
  )

  if (error) return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 p-6 rounded-lg">
        <p className="text-red-500">Error loading item data</p>
      </div>
    </div>
  )

  const handleChange = (section, field, value) => {
    setEditedItem(prev => ({
      ...prev,
      [section]: typeof prev[section] === 'object' ? {
        ...prev[section],
        [field]: value
      } : value
    }))
  }

  const handleNestedChange = (section, subsection, field, value) => {
    setEditedItem(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section]?.[subsection],
          [field]: value
        }
      }
    }))
  }

  const renderTextField = (label, value, onChange) => (
    <div className="form-group mb-4">
      <label className="block text-gray-300 text-sm mb-1">{label}</label>
      <input
        type="text"
        className="w-full bg-gray-700 text-white rounded px-3 py-2"
        value={value || ''}
        onChange={e => onChange(e.target.value)}
      />
    </div>
  )

  const renderTextArea = (label, value, onChange) => (
    <div className="form-group mb-4">
      <label className="block text-gray-300 text-sm mb-1">{label}</label>
      <textarea
        className="w-full bg-gray-700 text-white rounded px-3 py-2"
        value={value || ''}
        onChange={e => onChange(e.target.value)}
        rows="3"
      />
    </div>
  )

  const renderArrayField = (label, array = [], onChange) => (
    <div className="form-group mb-4">
      <label className="block text-gray-300 text-sm mb-1">{label}</label>
      <input
        type="text"
        className="w-full bg-gray-700 text-white rounded px-3 py-2"
        value={Array.isArray(array) ? array.join(', ') : ''}
        onChange={e => onChange(e.target.value.split(',').map(item => item.trim()))}
      />
      <p className="text-gray-500 text-xs mt-1">Separate items with commas</p>
    </div>
  )

  const renderSection = (title, fields) => (
    <div className="border-t border-gray-700 pt-4 mt-4">
      <h3 className="text-lg font-semibold text-white mb-4">{title}</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {fields}
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
        <div className="sticky top-0 bg-gray-800 p-4 border-b border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">Edit {itemType}</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="p-6">
          {/* Basic Information */}
          {renderSection("Basic Information", <>
            {renderTextField("Title", editedItem?.title,
              value => handleChange("title", null, value))}
            {renderTextField("Display Title", editedItem?.displayTitle,
              value => handleChange("displayTitle", null, value))}
            {renderTextField("Detail URL", editedItem?.detailUrl,
              value => handleChange("detailUrl", null, value))}
            {renderTextField("Detail URL Path", editedItem?.detailUrlPath,
              value => handleChange("detailUrlPath", null, value))}
            {renderTextField("Thumbnail", editedItem?.thumbnail,
              value => handleChange("thumbnail", null, value))}
            {renderTextField("Image", editedItem?.image,
              value => handleChange("image", null, value))}
            {(itemType === 'Series' || itemType === 'Anime') &&
              renderTextField("Season", editedItem?.season,
                value => handleChange("season", null, value))}
          </>)}

          {/* Metadata Section */}
          {editedItem?.metadata && renderSection("Metadata", <>
            {renderTextArea("Synopsis", editedItem.metadata.synopsis,
              value => handleChange("metadata", "synopsis", value))}
            {renderArrayField("Actors", editedItem.metadata.actors,
              value => handleChange("metadata", "actors", value))}
            {renderTextField("Year", editedItem.metadata.year,
              value => handleChange("metadata", "year", value))}
            {renderTextField("Genre", editedItem.metadata.genre,
              value => handleChange("metadata", "genre", value))}
            {renderTextField("Origin", editedItem.metadata.origin,
              value => handleChange("metadata", "origin", value))}
            {renderTextField("Creator", editedItem.metadata.creator,
              value => handleChange("metadata", "creator", value))}
            {renderTextField("Duration", editedItem.metadata.duration,
              value => handleChange("metadata", "duration", value))}
          </>)}

          {/* TMDB Section */}
          {editedItem?.tmdb && renderSection("TMDB Data", <>
            {renderTextField("TMDB ID", editedItem.tmdb.id,
              value => handleChange("tmdb", "id", value))}
            {renderTextField("TMDB Title", editedItem.tmdb.title,
              value => handleChange("tmdb", "title", value))}
            {renderTextField("Original Title", editedItem.tmdb.original_title,
              value => handleChange("tmdb", "original_title", value))}
            {renderTextArea("Overview", editedItem.tmdb.overview,
              value => handleChange("tmdb", "overview", value))}
            {renderTextField("Release Date", editedItem.tmdb.release_date,
              value => handleChange("tmdb", "release_date", value))}
            {renderTextField("Poster Path", editedItem.tmdb.poster_path,
              value => handleChange("tmdb", "poster_path", value))}
            {renderTextField("Backdrop Path", editedItem.tmdb.backdrop_path,
              value => handleChange("tmdb", "backdrop_path", value))}
            {renderTextField("Vote Average", editedItem.tmdb.vote_average,
              value => handleChange("tmdb", "vote_average", value))}
            {renderTextField("Vote Count", editedItem.tmdb.vote_count,
              value => handleChange("tmdb", "vote_count", value))}
            {renderArrayField("Genres", editedItem.tmdb.genres,
              value => handleChange("tmdb", "genres", value))}
            {itemType !== 'Movie' && <>
              {renderTextField("Number of Seasons", editedItem.tmdb.number_of_seasons,
                value => handleChange("tmdb", "number_of_seasons", value))}
              {renderTextField("Number of Episodes", editedItem.tmdb.number_of_episodes,
                value => handleChange("tmdb", "number_of_episodes", value))}
              {renderTextField("Status", editedItem.tmdb.status,
                value => handleChange("tmdb", "status", value))}
              {renderTextField("In Production", editedItem.tmdb.in_production,
                value => handleChange("tmdb", "in_production", value))}
            </>}
          </>)}

          {/* Streaming URLs Section */}
          {editedItem?.streamingUrls && renderSection("Streaming URLs",
            <div className="col-span-2">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-700">
                  <thead>
                    <tr>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Provider</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">URL</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Language</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Size</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {editedItem.streamingUrls.map((url, index) => (
                      <tr key={url.id || index}>
                        <td className="px-4 py-2">
                          <input
                            type="text"
                            className="w-full bg-gray-700 text-white rounded px-2 py-1"
                            value={url.provider || ''}
                            onChange={e => {
                              const newUrls = [...editedItem.streamingUrls]
                              newUrls[index].provider = e.target.value
                              handleChange("streamingUrls", null, newUrls)
                            }}
                          />
                        </td>
                        <td className="px-4 py-2">
                          <input
                            type="text"
                            className="w-full bg-gray-700 text-white rounded px-2 py-1"
                            value={url.url || ''}
                            onChange={e => {
                              const newUrls = [...editedItem.streamingUrls]
                              newUrls[index].url = e.target.value
                              handleChange("streamingUrls", null, newUrls)
                            }}
                          />
                        </td>
                        <td className="px-4 py-2">
                          <select
                            className="w-full bg-gray-700 text-white rounded px-2 py-1"
                            value={url.language || ''}
                            onChange={e => {
                              const newUrls = [...editedItem.streamingUrls]
                              newUrls[index].language = e.target.value
                              handleChange("streamingUrls", null, newUrls)
                            }}
                          >
                            <option value="VF">VF</option>
                            <option value="VOSTFR">VOSTFR</option>
                            <option value="unknown">Unknown</option>
                          </select>
                        </td>
                        <td className="px-4 py-2">
                          <input
                            type="text"
                            className="w-full bg-gray-700 text-white rounded px-2 py-1"
                            value={url.size || ''}
                            onChange={e => {
                              const newUrls = [...editedItem.streamingUrls]
                              newUrls[index].size = e.target.value
                              handleChange("streamingUrls", null, newUrls)
                            }}
                          />
                        </td>
                        <td className="px-4 py-2">
                          <select
                            className="w-full bg-gray-700 text-white rounded px-2 py-1"
                            value={url.isActive ? 'true' : 'false'}
                            onChange={e => {
                              const newUrls = [...editedItem.streamingUrls]
                              newUrls[index].isActive = e.target.value === 'true'
                              handleChange("streamingUrls", null, newUrls)
                            }}
                          >
                            <option value="true">Active</option>
                            <option value="false">Inactive</option>
                          </select>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Episodes Section */}
          {(itemType === 'Series' || itemType === 'Anime') && editedItem?.episodes && renderSection("Episodes",
            <div className="col-span-2">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-700">
                  <thead>
                    <tr>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Episode</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Season</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Language</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Streaming URLs</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {editedItem.episodes.map((episode, index) => (
                      <tr key={index}>
                        <td className="px-4 py-2">
                          <input
                            type="text"
                            className="w-full bg-gray-700 text-white rounded px-2 py-1"
                            value={episode.episodeNumber || ''}
                            onChange={e => {
                              const newEpisodes = [...editedItem.episodes]
                              newEpisodes[index].episodeNumber = e.target.value
                              handleChange("episodes", null, newEpisodes)
                            }}
                          />
                        </td>
                        <td className="px-4 py-2">
                          <input
                            type="text"
                            className="w-full bg-gray-700 text-white rounded px-2 py-1"
                            value={episode.season || ''}
                            onChange={e => {
                              const newEpisodes = [...editedItem.episodes]
                              newEpisodes[index].season = e.target.value
                              handleChange("episodes", null, newEpisodes)
                            }}
                          />
                        </td>
                        <td className="px-4 py-2">
                          <select
                            className="w-full bg-gray-700 text-white rounded px-2 py-1"
                            value={episode.language || ''}
                            onChange={e => {
                              const newEpisodes = [...editedItem.episodes]
                              newEpisodes[index].language = e.target.value
                              handleChange("episodes", null, newEpisodes)
                            }}
                          >
                            <option value="VF">VF</option>
                            <option value="VOSTFR">VOSTFR</option>
                            <option value="unknown">Unknown</option>
                          </select>
                        </td>
                        <td className="px-4 py-2">
                          <button
                            className="px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                            onClick={() => {
                              // Open a modal to edit streaming URLs for this episode
                              // TODO: Implement episode streaming URLs editor
                            }}
                          >
                            Edit URLs ({episode.streamingUrls?.length || 0})
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Jikan Section for Anime */}
          {itemType === 'Anime' && editedItem?.jikan && renderSection("Jikan Data", <>
            {renderTextField("MAL ID", editedItem.jikan.mal_id,
              value => handleChange("jikan", "mal_id", value))}
            {renderTextField("English Title", editedItem.jikan.title?.english,
              value => handleNestedChange("jikan", "title", "english", value))}
            {renderTextField("Japanese Title", editedItem.jikan.title?.japanese,
              value => handleNestedChange("jikan", "title", "japanese", value))}
            {renderTextField("Default Title", editedItem.jikan.title?.default,
              value => handleNestedChange("jikan", "title", "default", value))}
            {renderTextField("Type", editedItem.jikan.type,
              value => handleChange("jikan", "type", value))}
            {renderTextField("Source", editedItem.jikan.source,
              value => handleChange("jikan", "source", value))}
            {renderTextField("Episodes", editedItem.jikan.episodes,
              value => handleChange("jikan", "episodes", value))}
            {renderTextField("Status", editedItem.jikan.status,
              value => handleChange("jikan", "status", value))}
            {renderTextField("Score", editedItem.jikan.score,
              value => handleChange("jikan", "score", value))}
            {renderTextField("Rank", editedItem.jikan.rank,
              value => handleChange("jikan", "rank", value))}
            {renderTextField("Popularity", editedItem.jikan.popularity,
              value => handleChange("jikan", "popularity", value))}
            {renderTextField("Members", editedItem.jikan.members,
              value => handleChange("jikan", "members", value))}
            {renderTextField("Favorites", editedItem.jikan.favorites,
              value => handleChange("jikan", "favorites", value))}
            {renderTextArea("Synopsis", editedItem.jikan.synopsis,
              value => handleChange("jikan", "synopsis", value))}
            {renderTextArea("Background", editedItem.jikan.background,
              value => handleChange("jikan", "background", value))}
            {renderTextField("Season", editedItem.jikan.season,
              value => handleChange("jikan", "season", value))}
            {renderTextField("Year", editedItem.jikan.year,
              value => handleChange("jikan", "year", value))}
          </>)}
        </div>

        <div className="sticky bottom-0 bg-gray-800 p-4 border-t border-gray-700 flex justify-end gap-2">
          <button
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => onSave(editedItem)}
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  )
}
