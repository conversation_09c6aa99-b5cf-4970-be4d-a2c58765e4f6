/* Admin Panel Tabs */
.config-card {
  @apply bg-gray-800 rounded-lg p-6 mb-6;
}

.config-card-header {
  @apply flex items-center justify-between mb-4;
}

.config-card-header h3 {
  @apply text-lg font-semibold text-white flex items-center;
}

.config-card-header h3 i {
  @apply mr-2 text-blue-500;
}

.config-card-body {
  @apply space-y-4;
}

.config-item {
  @apply flex flex-col space-y-2;
}

.config-item label {
  @apply text-sm text-gray-300;
}

.input-group {
  @apply flex space-x-2;
}

.input-group input {
  @apply flex-1 bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

/* Button Styles */
.button {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  color: #ffffff;
  border: none;
  padding: 14px 28px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-height: 48px;
  box-sizing: border-box;
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.button:hover::before {
  left: 100%;
}

.button:hover {
  background: linear-gradient(135deg, #29b6f6 0%, #0288d1 100%);
  transform: translateY(-2px);
  box-shadow: 0 12px 28px rgba(79, 195, 247, 0.4);
}

.primary {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
}

.primary:hover {
  background: linear-gradient(135deg, #29b6f6 0%, #0288d1 100%);
  box-shadow: 0 12px 28px rgba(79, 195, 247, 0.4);
}

.secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.secondary:hover {
  background: linear-gradient(135deg, #495057 0%, #343a40 100%);
  box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
}

.warning {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.warning:hover {
  background: linear-gradient(135deg, #f57c00 0%, #e65100 100%);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.3);
}

.danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.danger:hover {
  background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
  box-shadow: 0 8px 20px rgba(244, 67, 54, 0.3);
}

.button.compact {
  @apply px-3 py-2 text-sm;
}

.button.full-width {
  @apply w-full;
}

/* System Tab */
.system-controls {
  @apply flex flex-wrap gap-4 mb-6;
}

.system-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.compact-stats-grid {
  @apply grid grid-cols-2 gap-4;
}

.compact-stat {
  @apply bg-gray-700 rounded p-4;
}

.compact-stat-value {
  @apply text-2xl font-bold text-white mb-1;
}

.compact-stat-label {
  @apply text-sm text-gray-400;
}

.processes-container {
  @apply space-y-2;
}

.process-item {
  @apply flex items-center justify-between bg-gray-700 p-3 rounded;
}

.process-name {
  @apply text-white;
}

.process-status {
  @apply text-sm;
}

.process-status.success {
  @apply text-green-500;
}

.process-status.error {
  @apply text-red-500;
}

.process-memory {
  @apply text-sm text-gray-400;
}

/* Logs Tab */
.logs-management {
  @apply space-y-6;
}

.logs-controls {
  @apply flex flex-wrap gap-4 items-center mb-4;
}

.logs-controls select {
  @apply bg-gray-700 text-white rounded px-3 py-2;
}

.logs-container {
  @apply bg-gray-800 rounded-lg;
}

.logs-display {
  @apply h-[600px] overflow-y-auto p-4 space-y-2;
}

.log-entry {
  @apply flex flex-wrap gap-2 text-sm p-2 rounded bg-gray-700;
}

.log-timestamp {
  @apply text-gray-400;
}

.log-level {
  @apply px-2 py-0.5 rounded text-xs font-medium;
}

.log-level.error {
  @apply bg-red-900 text-red-200;
}

.log-level.warning {
  @apply bg-yellow-900 text-yellow-200;
}

.log-level.info {
  @apply bg-blue-900 text-blue-200;
}

.log-level.debug {
  @apply bg-gray-600 text-gray-200;
}

.log-source {
  @apply text-gray-400;
}

.log-message {
  @apply text-white;
}

.log-error-details {
  @apply mt-2 p-2 bg-gray-900 rounded text-red-300 font-mono text-xs w-full overflow-x-auto;
}

.logs-pagination {
  @apply flex items-center justify-between mt-4;
}

.websocket-status {
  @apply flex items-center gap-2 px-3 py-1 rounded text-sm;
}

.websocket-status.connected {
  @apply bg-green-900 text-green-200;
}

.websocket-status.disconnected {
  @apply bg-red-900 text-red-200;
}

.websocket-status i {
  @apply text-xs;
}

/* Messages */
.config-message,
.system-message,
.logs-message {
  @apply fixed bottom-4 right-4 px-4 py-2 rounded shadow-lg text-sm z-50;
}

.config-message.success,
.system-message.success,
.logs-message.success {
  @apply bg-green-600 text-white;
}

.config-message.error,
.system-message.error,
.logs-message.error {
  @apply bg-red-600 text-white;
}

.config-message.info,
.system-message.info,
.logs-message.info {
  @apply bg-blue-600 text-white;
}

/* Loading states */
.loading {
  @apply flex items-center justify-center p-8 text-gray-400;
}

.loading i {
  @apply text-2xl animate-spin mr-2;
}

/* System Card Styles (for SystemTab and PerformanceTab) */
.system-management {
  @apply space-y-6;
}

.system-card {
  @apply bg-gray-800 rounded-lg p-6 mb-6 shadow;
  border: 2px solid #3b82f6; /* Add a blue border for visual confirmation */
}

.system-card-header {
  @apply flex items-center justify-between mb-4;
}

.system-card-header h3 {
  @apply text-lg font-semibold text-white flex items-center;
}

.system-card-header h3 i {
  @apply mr-2 text-blue-500;
}

.system-card-body {
  @apply space-y-4;
}

/* Ensure system-grid is consistent */
.system-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}
