import { NextResponse } from 'next/server'

// Extract the base URL from API_URL for server-side requests
const GRAPHQL_ENDPOINT = process.env.API_URL || 'http://netstream-backend-container:3001/graphql'

const LATEST_QUERIES = {
  movies: `
    query GetLatestMovies($limit: Int, $page: Int, $excludeAncien: Boolean) {
      latestMovies(limit: $limit, page: $page, excludeAncien: $excludeAncien) {
        id
        title
        displayTitle
        thumbnail
        image
        metadata {
          year
          synopsis
        }
        tmdb {
          id
          title
          overview
          poster_path
          release_date
          vote_average
          vote_count
          genres
        }
      }
    }
  `,
  series: `
    query GetLatestSeries($limit: Int, $page: Int) {
      latestSeries(limit: $limit, page: $page) {
        id
        title
        displayTitle
        thumbnail
        image
        season
        episodes {
          episodeNumber
          streamingUrls {
            language
          }
        }
        metadata {
          year
          synopsis
        }
        tmdb {
          id
          title
          overview
          poster_path
          release_date
          vote_average
          vote_count
          genres
        }
      }
    }
  `,
  anime: `
    query GetLatestAnime($limit: Int, $page: Int) {
      latestAnime(limit: $limit, page: $page) {
        id
        title
        displayTitle
        thumbnail
        image
        animeLanguage
        season
        episodes {
          episodeNumber
        }
        metadata {
          year
          synopsis
        }
        jikan {
          mal_id
          title {
            default
            english
            japanese
          }
          images {
            jpg {
              image_url
              large_image_url
            }
          }
          score
          aired {
            from
          }
          genres {
            name
          }
        }
      }
    }
  `
}

export async function GET(request, { params }) {
  let type
  try {
    const resolvedParams = await params
    type = resolvedParams.type

    console.log(`🔄 Latest API: Fetching latest ${type}...`)

    if (!LATEST_QUERIES[type]) {
      console.log(`❌ Latest API: Invalid type: ${type}`)
      return NextResponse.json(
        { success: false, error: `Invalid type: ${type}` },
        { status: 400 }
      )
    }

    const variables = {
      limit: 20,
      page: 1
    }

    // Add excludeAncien for movies
    if (type === 'movies') {
      variables.excludeAncien = true
    }

    console.log(`📡 Latest API: Making GraphQL request for ${type} with variables:`, variables)

    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: LATEST_QUERIES[type],
        variables
      })
    })

    console.log(`📡 Latest API: GraphQL response status for ${type}:`, response.status)

    if (!response.ok) {
      throw new Error(`GraphQL request failed: ${response.status}`)
    }

    const result = await response.json()
    console.log(`📦 Latest API: GraphQL result for ${type}:`, JSON.stringify(result).substring(0, 200) + '...')

    if (result.errors) {
      console.error(`❌ Latest API: GraphQL errors for ${type}:`, result.errors)
      return NextResponse.json(
        { success: false, error: result.errors[0]?.message || 'GraphQL error' },
        { status: 500 }
      )
    }

    const queryName = `latest${type.charAt(0).toUpperCase() + type.slice(1)}`
    const items = result.data?.[queryName] || []

    console.log(`✅ Latest API: Returning ${items.length} latest ${type} items`)

    return NextResponse.json({
      success: true,
      data: items
    })

  } catch (error) {
    console.error(`💥 Latest API error for ${type || 'unknown'}:`, error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
