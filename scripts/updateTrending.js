const mongoose = require('mongoose');
const axios = require('axios');
const TrendingItem = require('../src/db/models/TrendingItem'); // Adjust path if needed
const { tmdbApiKey, mongoUri } = require('../src/config/env'); // Adjust path if needed

// Function to fetch trending data from TMDb for a specific media type, time window, and page
async function fetchTrendingFromTMDb(mediaType, timeWindow = 'week', page = 1) {
  console.log(`Fetching trending ${mediaType} data from TMDb (time: ${timeWindow}, page ${page})...`);
  const url = `https://api.themoviedb.org/3/trending/${mediaType}/${timeWindow}?api_key=${tmdbApiKey}&page=${page}`;
  console.log('Fetching data with URL:', url);
  try {
    const response = await axios.get(url);
    console.log(`TMDb API request status: ${response.status}`);

    if (response.status !== 200) {
      console.error(`Failed to fetch trending data from TMDb for ${mediaType}/${timeWindow}, status code: ${response.status}`);
      return null;
    }
    const results = response.data?.results || [];
    console.log(`Successfully fetched ${results.length} items for ${mediaType}/${timeWindow} page ${page}.`);
    // console.log('Sample fetched data:', results.slice(0, 2)); // Log sample data
    return results;
  } catch (error) {
    console.error(`Error fetching trending data from TMDb for ${mediaType}/${timeWindow}:`, error.message);
     if (error.response) {
        console.error(`TMDb API Error Status: ${error.response.status}`);
        console.error(`TMDb API Error Data:`, error.response.data);
    }
    return null;
  }
}

// Main function to run the update process
async function runUpdateTrending() {
  console.log('Starting updateTrendingData process for WEEKLY trends (Page 1 Only)...'); // Updated log

  if (!tmdbApiKey) {
    console.error('TMDb API Key missing. Please set TMDB_API_KEY in your environment.');
    return;
  }
  if (!mongoUri) {
    console.error('MongoDB URI missing. Please set MONGO_URI in your environment.');
    return;
  }

  let connection; // Variable to hold the connection state

  try {
    // Connect to Database
    if (mongoose.connection.readyState !== 1) {
      console.log('Connecting to MongoDB...');
      connection = await mongoose.connect(mongoUri);
      console.log('Connected to MongoDB');
    } else {
      console.log('Already connected to MongoDB');
      connection = mongoose; // Use existing connection
    }


    const mediaTypes = ['movie', 'tv'];
    const timeWindow = 'week';

    for (const mediaType of mediaTypes) {
      console.log(`\nProcessing trending data for ${mediaType}/${timeWindow}...`);
      let fetchedItems = [];
      let fetchErrorOccurred = false;

      // --- Fetch ONLY PAGE 1 ---
      console.log(`Fetching page 1 for ${mediaType}/${timeWindow}...`);
      const results = await fetchTrendingFromTMDb(mediaType, timeWindow, 1); // Fetch only page 1

      if (results === null) {
        console.error(`Error fetching page 1 for ${mediaType}/${timeWindow}. Skipping this type.`);
        fetchErrorOccurred = true;
        continue; // Skip to the next media type
      }
      fetchedItems = results; // Assign the results directly
      // --- End of Fetch ONLY PAGE 1 ---

      console.log(`Total trending items fetched for ${mediaType}/${timeWindow}: ${fetchedItems.length}`);

      if (fetchedItems.length > 0) {
        // Prepare items for insertion, matching the schema
        const itemsToInsert = fetchedItems.map((item, index) => {
          // Basic validation for required fields from TMDb
          if (!item.id) { // Removed media_type check as it should always be present on single-type endpoint
             console.warn(`Skipping item due to missing id:`, item);
             return null;
          }
          return {
            tmdbId: item.id,      // Map TMDb 'id' to schema 'tmdbId'
            mediaType: mediaType, // Set based on the current loop
            rank: index,          // Set rank based on the overall position (index 0-19)
            source: 'tmdb',       // Set source
            // fetchedAt will be set by the schema default
          };
        }).filter(item => item !== null); // Remove any null items

        console.log(`Attempting to insert ${itemsToInsert.length} valid items for ${mediaType}...`);
        // console.log('Data sample before insertMany:', JSON.stringify(itemsToInsert.slice(0, 2), null, 2));

        if (itemsToInsert.length > 0) {
            try {
                // Delete existing trending items for this media type from TMDB source
                const deleteResult = await TrendingItem.deleteMany({ source: 'tmdb', mediaType: mediaType });
                console.log(`deleteMany result for ${mediaType}:`, deleteResult);

                // Insert the new items
                const insertManyResult = await TrendingItem.insertMany(itemsToInsert, { ordered: false });
                console.log(`insertMany result for ${mediaType}:`, insertManyResult); // Log the result
                console.log(`Successfully inserted ${insertManyResult.length} trending data for ${mediaType} into the database.`);
            } catch (dbError) {
                // Log detailed validation errors if they occur
                if (dbError.name === 'ValidationError') {
                console.error(`Validation Error inserting trending data for ${mediaType}:`);
                for (const field in dbError.errors) {
                    console.error(`  - ${field}: ${dbError.errors[field].message}`);
                }
                // console.error('Sample data that may have caused validation error:', JSON.stringify(itemsToInsert.slice(0, 5), null, 2));
                } else if (dbError.code === 11000) {
                    console.error(`Duplicate key error during insertMany for ${mediaType}:`, dbError.message);
                } else {
                    console.error(`Database Error updating trending data for ${mediaType}:`, dbError);
                }
            }
        } else {
            console.log(`No valid items to insert for ${mediaType} after filtering.`);
        }
      } else {
        console.log(`No trending items fetched to update in the database for ${mediaType}/${timeWindow}.`);
      }
      console.log(`Finished processing trending data for ${mediaType}/${timeWindow}.`);
    } // End of mediaType loop

    console.log('Finished updateTrendingData process successfully.');

  } catch (error) {
    console.error('Critical Error during updateTrendingData task:', error);
  } finally {
    // Disconnect from MongoDB
    if (connection && connection.disconnect) { // Check if connection exists and has disconnect method
        await connection.disconnect();
        console.log('Disconnected from MongoDB');
    }
  }
}

// Execute the function
runUpdateTrending();
