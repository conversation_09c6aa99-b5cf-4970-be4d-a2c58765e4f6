// OmniDev reporting for duty.

// Purpose: Connects to MongoDB, iterates through collections starting AFTER the last processed ID
// (in continue mode), extracts provider URLs, logs failures, and incrementally updates JSON
// output files. Supports continuing from last ID or overwriting. *FIXED missing validate function*.

// Usage:
//   node scripts/extractProviderUrls_incremental.js [options] # Default: Continue after last processed ID
//   node scripts/extractProviderUrls_incremental.js --overwrite # Start fresh, process all

// ==================================================================================
//                              DEPENDENCIES
// ==================================================================================
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
const https = require('https');
const { JSDOM } = require('jsdom');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const logger = require('../src/utils/logger.js'); // Adjust path if needed

// ==================================================================================
//                              CONFIGURATION
// ==================================================================================
dotenv.config({ path: path.resolve(__dirname, '../.env') });
const { MONGO_URI } = process.env;
const INTERMEDIATE_HOSTNAMES = ['tipfly.xyz'];
const PROCESSING_DELAY_MS = 300;

const FAILURE_LOG_FILE = 'extraction_failures.log';
const LAST_ID_STATE_FILE = 'last_processed_ids.json'; // New state file

const argv = yargs(hideBin(process.argv))
    .option('Movie', { type: 'boolean', description: 'Process Movie collection' })
    .option('Series', { type: 'boolean', description: 'Process Series collection' })
    .option('Anime', { type: 'boolean', description: 'Process Anime collection' })
    .option('outputDir', { type: 'string', default: './provider_url_output', description: 'Directory for JSON output and state files' })
    .option('overwrite', { type: 'boolean', default: false, description: 'Start fresh: Overwrite output JSONs & ignore last processed IDs.' })
    .help().alias('h', 'help')
    .argv;

const processAll = !argv.Movie && !argv.Series && !argv.Anime;
const collectionsToProcess = [];
if (processAll || argv.Movie) collectionsToProcess.push('Movie');
if (processAll || argv.Series) collectionsToProcess.push('Series');
if (processAll || argv.Anime) collectionsToProcess.push('Anime');

const OUTPUT_DIRECTORY = path.resolve(argv.outputDir);
const failureLogPath = path.join(OUTPUT_DIRECTORY, FAILURE_LOG_FILE);
const lastIdStateFilePath = path.join(OUTPUT_DIRECTORY, LAST_ID_STATE_FILE);
const IS_OVERWRITE_MODE = argv.overwrite;

// ==================================================================================
//                              DATABASE MODELS & CONFIG
// ==================================================================================
const Movie = require('../src/db/models/Movie.js');
const Series = require('../src/db/models/Series.js');
const Anime = require('../src/db/models/Anime.js');

const ALL_MODEL_CONFIG = {
    'Movie': { model: Movie, type: 'media', filename: 'output_movies.json' },
    'Series': { model: Series, type: 'series', filename: 'output_series.json' },
    'Anime': { model: Anime, type: 'anime', filename: 'output_anime.json' },
};

const ACTIVE_MODEL_CONFIGS = collectionsToProcess.map(name => {
    const config = ALL_MODEL_CONFIG[name];
    if (!config) return null;
    const outputFilePath = path.join(OUTPUT_DIRECTORY, config.filename);
    return { name, ...config, outputFilePath, lastProcessedId: null };
}).filter(Boolean);

// ==================================================================================
//                              UTILITY FUNCTIONS
// ==================================================================================
const connectDB = async () => { /* ... (unchanged) ... */ if (!MONGO_URI) { logger.error("MONGO_URI not found."); process.exit(1); } try { logger.info('Connecting to MongoDB...'); await mongoose.connect(MONGO_URI); logger.info('MongoDB Connected.'); } catch (err) { logger.error('MongoDB Connection Error:', err.message); process.exit(1); } };
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const ensureDirectoryExists = (dirPath) => { /* ... (unchanged) ... */ if (!fs.existsSync(dirPath)) { logger.info(`Directory '${dirPath}' does not exist. Creating...`); try { fs.mkdirSync(dirPath, { recursive: true }); logger.info(`Directory created: ${dirPath}`); } catch (error) { logger.error(`Failed to create directory '${dirPath}': ${error.message}`); process.exit(1); } } else { logger.debug(`Directory already exists: ${dirPath}`); } };
const initializeJsonOutputFile = (filePath) => { /* ... (unchanged) ... */ logger.info(`Initializing/Clearing output file: ${filePath}`); try { fs.writeFileSync(filePath, '[]', 'utf-8'); logger.info(`Initialized/Cleared ${filePath}`); } catch (error) { logger.error(`Error initializing JSON output file ${filePath}: ${error.message}`); logger.error(error.stack); } };
const appendResultToJsonFile = (filePath, newResult) => { /* ... (unchanged) ... */ logger.debug(`Appending result to ${filePath}`); try { const fileContent = fs.readFileSync(filePath, 'utf-8'); let dataArray = []; try { if (fileContent.trim() === '') { dataArray = []; } else { dataArray = JSON.parse(fileContent); if (!Array.isArray(dataArray)) { logger.warn(`Content of ${filePath} is not a valid JSON array. Resetting.`); dataArray = []; } } } catch (parseError) { logger.warn(`Error parsing existing JSON in ${filePath}. Starting fresh. Error: ${parseError.message}`); dataArray = []; } dataArray.push(newResult); fs.writeFileSync(filePath, JSON.stringify(dataArray, null, 2), 'utf-8'); logger.debug(`Successfully appended result. New length: ${dataArray.length}`); } catch (error) { logger.error(`Error appending result to JSON file ${filePath}: ${error.message}`); logger.error(error.stack); } };
const logFailure = (logFilePath, error, contextData) => { /* ... (unchanged) ... */ try { const logEntry = { timestamp: new Date().toISOString(), error: { message: error.message, name: error.name, }, context: contextData, }; fs.appendFileSync(logFilePath, JSON.stringify(logEntry) + '\n', 'utf-8'); logger.warn(`Logged failure to ${logFilePath}. Context: ${JSON.stringify(contextData.originalUrl || contextData.documentId || contextData.phase)}`); } catch (logError) { logger.error(`FATAL: Could not write to failure log file ${logFilePath}: ${logError.message}`); console.error("--- FAILED TO WRITE TO FAILURE LOG ---"); /* ... */ } };
const fetchWithSSLBypass = async (url, options = {}) => { /* ... (unchanged) ... */ const httpsAgent = new https.Agent({ rejectUnauthorized: false }); const headers = { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 'Referer': options.headers?.Referer || url, 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 'Accept-Language': 'en-US,en;q=0.5', ...options.headers, }; const timeout = options.timeout || 25000; try { return await fetch(url, { method: options.method || 'GET', headers: headers, agent: url.startsWith('https') ? httpsAgent : undefined, redirect: options.redirect || 'follow', timeout: timeout, }); } catch (error) { if (error.name === 'FetchError' || error.code === 'ETIMEOUT' || error.type === 'request-timeout') { logger.warn(`[ProviderExtractor] Network error during fetch for ${url}: ${error.message} (Code: ${error.code}, Type: ${error.type})`); } else { logger.error(`[ProviderExtractor] Unexpected error during fetch setup for ${url}: ${error.message}`); } throw error; } };

// ***** ADDED MISSING FUNCTION DEFINITION *****
/**
 * Validates if an existing file likely contains a JSON array. Very basic check.
 * @param {string} filePath
 * @returns {boolean} True if file exists and looks like a valid JSON array (or empty), false otherwise.
 */
const validateExistingJsonFile = (filePath) => {
     if (!fs.existsSync(filePath)) {
         logger.debug(`Validation: File ${filePath} does not exist.`);
         return false; // Doesn't exist, needs initialization
     }
     try {
        const content = fs.readFileSync(filePath, 'utf-8').trim();
        if (content === '' || (content.startsWith('[') && content.endsWith(']'))) {
            logger.debug(`Validation: File ${filePath} exists and looks like a valid array (or is empty).`);
            return true; // Looks potentially valid or empty
        } else {
            logger.warn(`Validation: Existing file ${filePath} content does not look like a valid JSON array.`);
            return false; // Invalid structure
        }
     } catch (readErr) {
         logger.error(`Validation: Error reading existing file ${filePath}: ${readErr.message}`);
         return false; // Error reading, treat as invalid
     }
};
// ***** END OF ADDED FUNCTION *****


/**
 * Loads the last processed IDs from the state file.
 * @param {string} filePath Path to the last ID state file.
 * @returns {object} An object mapping collection names to their last processed ID string, or empty object on error/not found.
 */
const loadLastIdState = (filePath) => { /* ... (unchanged) ... */ if (!fs.existsSync(filePath)) { logger.info(`Last ID state file not found: ${filePath}. Starting from beginning.`); return {}; } try { const fileContent = fs.readFileSync(filePath, 'utf-8'); if (fileContent.trim() === '') return {}; const data = JSON.parse(fileContent); logger.info(`Loaded last processed IDs from ${filePath}`); return data || {}; } catch (error) { logger.error(`Error reading or parsing last ID state file ${filePath}: ${error.message}. Starting from beginning.`); return {}; } };

/**
 * Saves the current state of last processed IDs to the state file.
 * @param {string} filePath Path to the last ID state file.
 * @param {object} stateData Object containing the current last processed IDs.
 */
const saveLastIdState = (filePath, stateData) => { /* ... (unchanged) ... */ try { fs.writeFileSync(filePath, JSON.stringify(stateData, null, 2), 'utf-8'); logger.debug(`Saved last processed ID state to ${filePath}`); } catch (error) { logger.error(`Error writing last ID state file ${filePath}: ${error.message}`); } };

// ==================================================================================
//                      VIDEO PROVIDER URL EXTRACTOR (Unchanged)
// ==================================================================================
function potentialUrlLooksLikeEmbed(url) { /* ... (unchanged) ... */ if (!url) return false; return url.match(/(\/embed-?|\/e\/|\/v\/|video|watch)/i) || url.match(/\/[a-zA-Z0-9_-]{8,}$/); }
function cleanProviderUrl(url) { /* ... (unchanged) ... */ if (!url) return url; try { const urlObj = new URL(url); if (urlObj.hostname.endsWith('oneupload.to') && urlObj.pathname.startsWith('/embed-')) { const newPathname = urlObj.pathname.replace('/embed-', '/'); const cleanedUrl = `${urlObj.protocol}//${urlObj.host}${newPathname}${urlObj.search}${urlObj.hash}`; logger.debug(`[ProviderExtractor] Applying cleaning rule: removed '/embed-' from ${url} -> ${cleanedUrl}`); return cleanedUrl; } } catch (e) { logger.warn(`[ProviderExtractor] Could not parse URL for cleaning: ${url}. Error: ${e.message}`); return url; } return url; }
async function extractVideoProviderUrl(initialUrl) { /* ... (Error handling improved, URL validation added - unchanged) ... */ logger.info(`[ProviderExtractor] Processing: ${initialUrl}`); if (!initialUrl || typeof initialUrl !== 'string' || (!initialUrl.startsWith('http://') && !initialUrl.startsWith('https://'))) { const error = new Error(`Invalid or non-HTTP(S) URL provided: ${initialUrl}`); logger.error(`[ProviderExtractor] ${error.message}`); throw error; } let finalUrl = initialUrl; let providerEmbedUrl = null; try { const response = await fetchWithSSLBypass(initialUrl, { redirect: 'follow' }); finalUrl = response.url; if (!response.ok) { if (response.status === 404) logger.warn(`[ProviderExtractor] Fetch failed for ${initialUrl} -> ${finalUrl}. Status: 404 Not Found`); else if (response.status >= 500) logger.warn(`[ProviderExtractor] Fetch failed for ${initialUrl} -> ${finalUrl}. Status: ${response.status} ${response.statusText} (Server Error)`); else logger.error(`[ProviderExtractor] Fetch failed for ${initialUrl} -> ${finalUrl}. Status: ${response.status} ${response.statusText}`); return null; } const html = await response.text(); if (!html) { logger.warn(`[ProviderExtractor] Fetch returned empty body for ${initialUrl} (final: ${finalUrl}).`); return null; } const dom = new JSDOM(html, { url: finalUrl }); const document = dom.window.document; const iframe = document.querySelector('iframe'); if (iframe?.src) { try { let iframeSrc = new URL(iframe.src.trim(), finalUrl).toString(); logger.debug(`[ProviderExtractor] Found iframe source: ${iframeSrc}`); if (potentialUrlLooksLikeEmbed(iframeSrc)) { providerEmbedUrl = iframeSrc; logger.debug(`[ProviderExtractor] Using iframe source as potential embed URL.`); } } catch (e) { logger.warn(`[ProviderExtractor] Could not parse or resolve iframe src: ${iframe.src}. Error: ${e.message}`); } } if (!providerEmbedUrl) { const scriptTags = document.querySelectorAll('script'); const jwPlayerSetupRegex = /jwplayer\s*\([^)]+\)\s*\.\s*setup\s*\(\s*(\{[\s\S]*?\})\s*\)\s*;?/i; const imageUrlRegex = /image\s*:\s*["'](https?:\/\/[^"'\s]+)["']/i; const baseLinkRegexes = [ /aboutlink\s*:\s*["'](https?:\/\/[^"'\s]+)["']/i, /logo\s*:\s*\{[^}]*link\s*:\s*["'](https?:\/\/[^"'\s]+)["'][^}]*\}/i, /(?:file|src)\s*:\s*["'](https?:\/\/[^"'\s]+)["']/i ]; for (const script of scriptTags) { const scriptContent = script.textContent; if (!scriptContent) continue; const match = scriptContent.match(jwPlayerSetupRegex); if (match?.[1]) { const setupConfigString = match[1]; logger.debug(`[ProviderExtractor] Found potential JWPlayer setup config.`); const imageMatch = setupConfigString.match(imageUrlRegex); let baseLinkMatch = null, sourcesFileUrl = null, baseLink = null; for (const regex of baseLinkRegexes) { baseLinkMatch = setupConfigString.match(regex); if (baseLinkMatch?.[1]) { if (regex === baseLinkRegexes[2]) sourcesFileUrl = baseLinkMatch[1]; break; } } baseLink = baseLinkMatch?.[1]; if (!baseLink && sourcesFileUrl) { logger.debug(`[ProviderExtractor] Attempting base domain derivation from sources file: ${sourcesFileUrl}`); try { const fileUrl = new URL(sourcesFileUrl); const hostnameParts = fileUrl.hostname.split('.'); if (hostnameParts.length >= 2) { const baseDomain = hostnameParts.slice(-2).join('.'); baseLink = `${fileUrl.protocol}//${baseDomain}/`; logger.debug(`[ProviderExtractor] Using base domain derived from sources file: ${baseLink}`); } } catch (e) { logger.warn(`[ProviderExtractor] Could not parse base domain from sources file URL: ${sourcesFileUrl}. Error: ${e.message}`); } } if (imageMatch?.[1] && baseLink) { const imageUrl = imageMatch[1]; logger.debug(`[ProviderExtractor] Found Image URL: ${imageUrl}`); logger.debug(`[ProviderExtractor] Found Base Link: ${baseLink}`); try { const imagePath = new URL(imageUrl).pathname; const videoId = path.parse(path.basename(imagePath)).name; if (videoId && videoId.length > 3) { const providerBase = baseLink.endsWith('/') ? baseLink : baseLink + '/'; providerEmbedUrl = providerBase + 'embed-' + videoId; logger.debug(`[ProviderExtractor] Constructed provider embed URL via JWPlayer: ${providerEmbedUrl}`); break; } else { logger.warn(`[ProviderExtractor] Could not extract valid video ID from image filename: ${path.basename(imagePath)}`); } } catch (e) { logger.error(`[ProviderExtractor] Error parsing image URL or constructing final URL from JWPlayer: ${e.message}`); } } else { logger.debug(`[ProviderExtractor] Could not find sufficient info (image URL & base link/source) in JWPlayer config.`); } } } } if (!providerEmbedUrl) { const scriptTags = document.querySelectorAll('script'); for (const script of scriptTags) { const scriptContent = script.textContent; if (!scriptContent) continue; const redirectMatch = scriptContent.match(/window\.location\.(?:href|replace)\s*=\s*['"]([^'"]+)['"]/i); if (redirectMatch?.[1]) { try { const absoluteRedirectUrl = new URL(redirectMatch[1], finalUrl).toString(); if (potentialUrlLooksLikeEmbed(absoluteRedirectUrl)) { logger.debug(`[ProviderExtractor] Found potential embed URL in JS redirect: ${absoluteRedirectUrl}`); providerEmbedUrl = absoluteRedirectUrl; break; } } catch (e) { logger.warn(`[ProviderExtractor] Could not parse or resolve JS redirect URL: ${redirectMatch[1]}. Error: ${e.message}`); } } } } if (!providerEmbedUrl && finalUrl !== initialUrl) { logger.debug(`[ProviderExtractor] Checking final URL after redirects: ${finalUrl}`); if (potentialUrlLooksLikeEmbed(finalUrl)) { logger.debug(`[ProviderExtractor] Final URL itself looks like a valid embed URL.`); providerEmbedUrl = finalUrl; } } if (providerEmbedUrl) { const cleanedUrl = cleanProviderUrl(providerEmbedUrl); logger.info(`    => Final Extracted URL: ${cleanedUrl}`); return cleanedUrl; } else { logger.warn(`[ProviderExtractor] No specific provider embed URL found for: ${initialUrl} (final URL was: ${finalUrl})`); return null; } } catch (error) { logger.error(`[ProviderExtractor] Error during processing of ${initialUrl}: ${error.message}`); if (error.name === 'FetchError' || error.code === 'ETIMEOUT' || error.type === 'request-timeout') { logger.warn(`    Type: Network Error`); } else if (JSDOM?.constructor && error instanceof JSDOM.constructor.SyntaxError) { logger.warn(`    Type: HTML Parsing Error for ${finalUrl}`); } else { logger.error(`    Type: Unexpected Error`); } throw error; } }

// ==================================================================================
//                              DATABASE PROCESSING LOGIC (Uses Last Processed ID)
// ==================================================================================
/**
 * Processes a single collection, finds provider URLs, logs failures, and updates JSON output file.
 * Starts query after the last processed ID if provided.
 * @param {object} config - Object containing { name, model, type, outputFilePath, lastProcessedId }
 * @param {object} lastIdData - The global object holding last IDs for all collections.
 */
async function processCollection(config, lastIdData) {
    const { name, model, type, outputFilePath, lastProcessedId } = config;
    logger.info(`\n--- Starting processing for ${name} collection ---`);
    logger.info(`Output will be appended to: ${outputFilePath}`);
    logger.info(`Failures will be logged to: ${failureLogPath}`);
    if (lastProcessedId) { logger.info(`Continuing after last processed ID: ${lastProcessedId}`); }
    else { logger.info(`Processing from the beginning.`); }

    let documentsProcessed = 0; let urlsFound = 0; let urlsExtracted = 0; let urlsFailed = 0; let errorsLogged = 0; let totalResultsWritten = 0;
    let currentLastProcessedId = lastProcessedId; // Track the latest ID processed in this run

    const hostnameRegex = INTERMEDIATE_HOSTNAMES.map(h => new RegExp(h.replace('.', '\\.'), 'i'));
    const urlMatchCriteria = { $or: hostnameRegex.map(regex => ({ 'url': { $regex: regex } })), };
    let query = {}; const baseQueryCriteria = {};
    if (type === 'media') baseQueryCriteria['streamingUrls'] = { $elemMatch: urlMatchCriteria }; else if (type === 'series' || type === 'anime') baseQueryCriteria['episodes.streamingUrls'] = { $elemMatch: urlMatchCriteria }; else { logger.error(`Unsupported model type: ${type}`); return; }
    if (currentLastProcessedId) { try { query = { $and: [ baseQueryCriteria, { _id: { $gt: new mongoose.Types.ObjectId(currentLastProcessedId) } } ] }; } catch (idError) { logger.error(`Invalid lastProcessedId format for ${name}: ${currentLastProcessedId}. Processing all. Error: ${idError.message}`); query = baseQueryCriteria; currentLastProcessedId = null; } }
    else { query = baseQueryCriteria; }
    const projection = { _id: 1, title: 1 }; if (type === 'media') { projection.streamingUrls = 1; } else if (type === 'series' || type === 'anime') { projection.season = 1; projection['episodes._id'] = 1; projection['episodes.episodeNumber'] = 1; projection['episodes.season'] = 1; projection['episodes.streamingUrls'] = 1; }

    logger.info(`[${name}] Querying for documents...`); logger.debug(`[${name}] Effective Query: ${JSON.stringify(query)}`);

    let cursor;
    try { cursor = model.find(query, projection).sort({ _id: 1 }).lean().cursor(); }
    catch (queryError) { logger.error(`[${name}] Failed to create cursor: ${queryError.message}`); logFailure(failureLogPath, queryError, { phase: "query_setup", collection: name, query: query }); return; }

    try { // Outer try for cursor iteration errors
        for await (const doc of cursor) {
            documentsProcessed++; logger.info(`[${name}] Processing document: ${doc.title || doc._id} (ID: ${doc._id})`); let resultsAddedForDoc = 0; let docHasError = false;
            const processStreamUrl = async (streamUrl, context = {}) => {
                const currentContext = { phase: "url_extraction", documentId: doc._id.toString(), documentTitle: doc.title, originalUrl: streamUrl?.url, streamUrlId: streamUrl?._id?.toString(), episodeContext: context };
                let needsProcessing = false; // Define here
                try {
                    if (!streamUrl?.url) { logger.debug(" -> Skip: Missing URL."); return; }
                    needsProcessing = INTERMEDIATE_HOSTNAMES.some(host => streamUrl.url.toLowerCase().includes(host));
                    if (needsProcessing) {
                        urlsFound++;
                        // Removed check against processedUrlSet as query handles start point
                        let logPrefix = ` -> Found URL`; if (context.episodeNumber) logPrefix += ` (Ep: ${context.episodeNumber}, S: ${context.season || 'N/A'})`; logger.info(`${logPrefix}: ${streamUrl.url}`);
                        let finalProviderUrl = null;
                        try { finalProviderUrl = await extractVideoProviderUrl(streamUrl.url); }
                        catch (extractionError) { docHasError = true; errorsLogged++; logFailure(failureLogPath, extractionError, currentContext); return; }
                        if (finalProviderUrl) {
                            urlsExtracted++; resultsAddedForDoc++; const result = { documentId: doc._id.toString(), documentTitle: doc.title, processedUrl: { originalUrl: streamUrl.url, originalUrlId: streamUrl._id?.toString(), hasExistingSourceUrl: !!streamUrl.sourceStreamUrl, extractedProviderUrl: finalProviderUrl }, timestamp: new Date().toISOString() }; if (context.episodeId) { result.episode = { episodeNumber: context.episodeNumber, season: context.season, episodeId: context.episodeId.toString() }; }
                            appendResultToJsonFile(outputFilePath, result); totalResultsWritten++;
                            // No longer need processedUrlSet.add()
                        } else { urlsFailed++; }
                    }
                } catch (processError) { docHasError = true; errorsLogged++; logger.error(`Unexpected error processing stream URL ${streamUrl?.url}: ${processError.message}`); logFailure(failureLogPath, processError, { ...currentContext, phase: "stream_url_processing_logic" }); }
                finally { if (needsProcessing) { await sleep(PROCESSING_DELAY_MS); } }
            };
             try { if (type === 'media') { for (const streamUrl of doc.streamingUrls || []) { await processStreamUrl(streamUrl); } } else if (type === 'series' || type === 'anime') { for (const episode of doc.episodes || []) { for (const streamUrl of episode.streamingUrls || []) { await processStreamUrl(streamUrl, { episodeId: episode._id, episodeNumber: episode.episodeNumber, season: episode.season || doc.season }); } } } }
             catch (iterationError) { docHasError = true; errorsLogged++; logger.error(`Unexpected error during URL iteration for Doc ID ${doc._id}: ${iterationError.message}`); logFailure(failureLogPath, iterationError, { phase: "url_iteration", documentId: doc._id.toString(), documentTitle: doc.title }); }
             logger.info(` -> Finished Doc ID ${doc._id}. Added ${resultsAddedForDoc} results.${docHasError ? ' [ERRORS LOGGED]' : ''}`);
             if (!docHasError) { currentLastProcessedId = doc._id.toString(); lastIdData[name] = currentLastProcessedId; saveLastIdState(lastIdStateFilePath, lastIdData); }
        } // End of cursor loop
    } catch (cursorError) { logger.error(`[${name}] Error iterating cursor: ${cursorError.message}`); logFailure(failureLogPath, cursorError, { phase: "cursor_iteration", collection: name }); if (cursorError.message?.includes("cursor id") && cursorError.message?.includes("not found")) { logger.warn(" -> Suggestion: MongoDB cursor timed out."); } }
    finally { if (cursor && !cursor.closed) { await cursor.close().catch(e => logger.warn(`Failed to close cursor: ${e.message}`)); } }
    logger.info(`\n--- Finished processing loop for ${name}. ---`); logger.info(`Summary for ${name}:`); logger.info(`- Documents Scanned: ${documentsProcessed}`); logger.info(`- URLs Found & Checked: ${urlsFound}`); /* urlsSkipped removed */ logger.info(`- URLs Extracted & Appended: ${urlsExtracted}`); logger.info(`- URLs Failed Extraction (Null): ${urlsFailed}`); logger.info(`- Errors Logged: ${errorsLogged}`); logger.info(`- Total Results Written: ${totalResultsWritten}`); if (currentLastProcessedId && currentLastProcessedId !== lastProcessedId) { logger.info(`- Last Processed ID updated to: ${currentLastProcessedId}`); } else if (!currentLastProcessedId && documentsProcessed > 0) { logger.warn(`- Last Processed ID could not be updated for this run.`); }
} // End of processCollection

// ==================================================================================
//                                  MAIN EXECUTION
// ==================================================================================
async function main() {
    console.log("==========================================================");
    console.log(" Starting Incremental URL Extraction (Last ID) Script ");
    console.log("==========================================================");

    if (ACTIVE_MODEL_CONFIGS.length === 0) { logger.warn("No collections specified."); return; }

    const mode = IS_OVERWRITE_MODE ? "OVERWRITE" : "APPEND/CONTINUE (after last ID)";
    logger.info(`RUNNING IN ${mode} MODE`);
    logger.info(`Processing collections: [${ACTIVE_MODEL_CONFIGS.map(c => c.name).join(', ')}]`);
    logger.info(`Output directory: ${OUTPUT_DIRECTORY}`);
    logger.info(`Failure Log: ${failureLogPath}`);
    logger.info(`Last Processed ID State File: ${lastIdStateFilePath}`);

    if (typeof fetch === 'undefined') { logger.error("node-fetch required."); process.exit(1); }

    ensureDirectoryExists(OUTPUT_DIRECTORY);

    logger.info("\n--- Preparing Output & State Files ---");
    let lastIdData = {};

    if (IS_OVERWRITE_MODE) {
        logger.info("Overwrite mode: Clearing output files and last ID state.");
        try { fs.writeFileSync(lastIdStateFilePath, '{}', 'utf-8'); } catch (e) { logger.error(`Failed to clear last ID state file: ${e.message}`) }
        for (const config of ACTIVE_MODEL_CONFIGS) {
            initializeJsonOutputFile(config.outputFilePath);
            config.lastProcessedId = null;
        }
    } else {
        lastIdData = loadLastIdState(lastIdStateFilePath);
        for (const config of ACTIVE_MODEL_CONFIGS) {
            config.lastProcessedId = lastIdData[config.name] || null;
            // *** Call validateExistingJsonFile HERE ***
            if (!validateExistingJsonFile(config.outputFilePath)) {
                logger.warn(`Output file ${config.outputFilePath} not found or invalid. Initializing.`);
                initializeJsonOutputFile(config.outputFilePath);
            } else {
                logger.info(`Output file ${config.outputFilePath} exists. New results will be appended.`);
            }
        }
    }

    await connectDB();

    for (const config of ACTIVE_MODEL_CONFIGS) { await processCollection(config, lastIdData); }

    logger.info("\n--- All selected collections processed ---");

    try { await mongoose.connection.close(); logger.info("MongoDB connection closed."); }
    catch (closeErr) { logger.error("Error closing MongoDB connection:", closeErr.message); }

    console.log("==========================================================");
    console.log("                     Script Finished                      ");
    console.log("==========================================================");
}

// Run main
main().catch(error => {
    logger.error(`Unhandled error in main execution: ${error.message}`);
    logger.error(error.stack);
    mongoose.connection?.close().finally(() => process.exit(1));
});