@import "tailwindcss";

/* NetStream Custom Styles */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Status indicators for anime */
.status-indicator {
  display: inline-block;
  margin-left: 0.5rem;
  font-size: 0.75rem;
}

.status-indicator.airing {
  color: #10b981;
}

.status-indicator.finished {
  color: #6b7280;
}

/* Player styles */
#player-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: black;
  z-index: 1000;
  display: none;
}

#player-container.active {
  display: block;
}

#player-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

#player-logo {
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  z-index: 10;
}

#player-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px;
  z-index: 10;
}

#player-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.player-button-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.player-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.player-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

#player-progress-container {
  position: relative;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  margin-bottom: 15px;
  cursor: pointer;
}

#player-progress-bar {
  height: 100%;
  background: #3b82f6;
  border-radius: 3px;
  width: 0%;
  transition: width 0.1s;
}

#player-volume-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

#player-volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  position: relative;
  cursor: pointer;
}

#player-volume-level {
  height: 100%;
  background: white;
  border-radius: 2px;
  width: 100%;
}

#player-time-display {
  color: white;
  font-size: 0.9rem;
}

#player-settings-menu {
  position: absolute;
  bottom: 60px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  padding: 10px;
  display: none;
  min-width: 150px;
}

.player-settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  color: white;
  cursor: pointer;
  border-radius: 4px;
}

.player-settings-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Original carousel styles for proper image display */
.carousel-item {
  position: relative;
  flex-shrink: 0;
  width: 200px;
  margin-right: 16px;
  cursor: pointer;
  transition: transform 0.25s ease-out;
}

.carousel-item:hover {
  transform: scale(1.05);
}

.carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  transition: all 0.25s ease-out;
  position: relative;
  top: 0;
  left: 0;
  z-index: 0;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: optimizeQuality;
  -ms-interpolation-mode: bicubic;
}

.carousel-item:hover img {
  transform: scale(1.1);
}

.carousel-item img.thumbnail {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 0;
  position: relative;
  top: 0;
  left: 0;
  z-index: 0;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: optimizeQuality;
  -ms-interpolation-mode: bicubic;
}

.carousel-item img.banner {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 0;
  position: relative;
  top: 0;
  left: 0;
  z-index: 0;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: optimizeQuality;
  -ms-interpolation-mode: bicubic;
}

.carousel-item .title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px 10px 10px;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  border-radius: 0 0 6px 6px;
  opacity: 0;
  transition: opacity 0.25s ease-out;
}

.carousel-item:hover .title {
  opacity: 1;
}

/* Enhanced carousel styling */
.carousel-container {
  position: relative;
}

.carousel-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -20px;
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, rgba(17, 24, 39, 1), transparent);
  z-index: 10;
  pointer-events: none;
}

.carousel-container::after {
  content: '';
  position: absolute;
  top: 0;
  right: -20px;
  width: 20px;
  height: 100%;
  background: linear-gradient(to left, rgba(17, 24, 39, 1), transparent);
  z-index: 10;
  pointer-events: none;
}

/* Smooth scrolling for carousels */
.scrollbar-hide {
  scroll-behavior: smooth;
}

/* Enhanced hover effects */
.group:hover .transform {
  transform: translateY(-4px);
}

/* Backdrop blur support */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Custom gradient backgrounds */
.bg-gradient-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .player-button-group {
    gap: 10px;
  }

  .player-button {
    font-size: 1rem;
    padding: 6px;
  }

  #player-volume-slider {
    width: 60px;
  }

  /* Mobile carousel adjustments */
  .carousel-container::before,
  .carousel-container::after {
    display: none;
  }

  /* Smaller carousel items on mobile */
  .w-56 {
    width: 10rem;
  }

  .h-80 {
    height: 14rem;
  }
}

/* Season/Episode info styling */
.season-episode {
  display: inline-block;
  padding: 3px 6px;
  background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
  border: 1px solid rgba(0, 188, 212, 0.8);
  border-radius: 4px;
  font-size: 0.7em;
  font-weight: 600;
  color: white;
  margin: 2px 1px;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  box-shadow: 0 2px 6px rgba(0, 188, 212, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  backdrop-filter: blur(4px);
  text-transform: uppercase;
  transition: all 0.25s ease-out;
}
