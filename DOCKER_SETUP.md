# NetStream Docker Setup Guide

This guide explains how to run NetStream with Dock<PERSON>, including the new API key management functionality.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- `.env` file configured with your MongoDB URI and other environment variables

## Quick Start

### 1. Build and Start All Services

```bash
# Start all services (Redis, Backend, Frontend)
docker-compose up --build

# Or run in detached mode
docker-compose up --build -d
```

### 2. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **GraphQL Playground**: http://localhost:3001/graphql
- **Redis**: localhost:6379

### 3. Test API Key Management

```bash
# Run the API key management test
node scripts/test-api-keys-docker.js
```

## Services Overview

### Redis (Port 6379)
- Caching service for improved performance
- Persistent data storage with volume mounting
- Health checks enabled

### Backend (Port 3001)
- Fastify-based GraphQL API server
- MongoDB connection for data persistence
- API key management through database
- Health check endpoint at `/health`

### Frontend (Port 3000)
- Next.js application with standalone output
- Communicates with backend via internal Docker network
- Admin panel for configuration management

## API Key Management

### How It Works

1. **Database Storage**: API keys are stored in MongoDB Atlas config collection
2. **Fallback Support**: Falls back to environment variables if database is unavailable
3. **Caching**: 5-minute cache for performance
4. **Admin Interface**: Manage keys through the admin panel config tab

### Updating API Keys

#### Via Admin Panel
1. Go to http://localhost:3000/admin
2. Login with your admin key
3. Navigate to the "Config" tab
4. Update TMDB or Gemini API keys
5. Click "Update" to save

#### Via GraphQL API
```graphql
mutation UpdateApiKey($key: String!, $value: String!, $adminToken: String!) {
  updateApiKey(key: $key, value: $value, adminToken: $adminToken) {
    success
    message
  }
}
```

### Supported API Keys
- `TMDB_API_KEY`: The Movie Database API key
- `GEMINI_API_KEY`: Google Gemini AI API key

## Environment Variables

### Required
```env
MONGO_URI=your_mongodb_connection_string
ADMIN_KEY=your_admin_password
```

### Optional
```env
TMDB_API_KEY=your_tmdb_api_key
GEMINI_API_KEY=your_gemini_api_key
REDIS_URL=redis://redis:6379
NODE_ENV=production
```

## Docker Commands

### Development
```bash
# Build and start services
docker-compose up --build

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild specific service
docker-compose build backend
docker-compose up backend
```

### Production
```bash
# Start in production mode
docker-compose -f docker-compose.yml up -d

# Update and restart
docker-compose pull
docker-compose up --build -d
```

### Troubleshooting
```bash
# Check service health
docker-compose ps

# View specific service logs
docker-compose logs backend
docker-compose logs frontend

# Restart specific service
docker-compose restart backend

# Clean up
docker-compose down -v  # Removes volumes
docker system prune     # Clean up unused containers/images
```

## Health Checks

All services include health checks:

- **Backend**: `curl http://localhost:3001/health`
- **Frontend**: `curl http://localhost:3000`
- **Redis**: `redis-cli ping`

## File Structure

```
NetStream_graphql/
├── docker-compose.yml          # Main compose file
├── docker-compose.test.yml     # Test environment
├── Dockerfile                  # Backend Dockerfile
├── Dockerfile.frontend         # Frontend Dockerfile
├── .env                        # Environment variables
├── scripts/
│   └── test-api-keys-docker.js # API key test script
└── netstream-nextjs/           # Frontend source code
```

## Migration from Environment Variables

The system automatically migrates from environment variables to database storage:

1. On first admin login, environment variables are copied to database
2. Subsequent updates use database values
3. Environment variables serve as fallbacks

## Security Notes

- API keys are masked in the admin interface (shown as ••••••••)
- Database connections use MongoDB Atlas with authentication
- Admin access requires valid admin key
- All services run with health checks and restart policies
