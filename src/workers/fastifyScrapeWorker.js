// File: src/workers/fastifyScrapeWorker.js
// Optimized Scraping Worker for Fastify Migration
// High-performance concurrent scraping with improved error handling

const FastifyJobQueue = require('../jobs/fastifyJobQueue');
const FastifyDbService = require('../db/fastifyDbService');
const { MongoClient } = require('mongodb');

class FastifyScrapeWorker {
  constructor() {
    this.jobQueue = new FastifyJobQueue();
    this.db = null;
    this.dbService = null;
    this.isInitialized = false;
    
    // Worker configuration
    this.config = {
      concurrency: {
        scrapeMovies: 3,
        scrapeSeries: 3,
        scrapeAnime: 2,
        enrichMetadata: 5,
        updateTrending: 1
      },
      retryAttempts: 3,
      retryDelay: 5000
    };
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Connect to database
      const mongoClient = new MongoClient(process.env.MONGO_URI, {
        maxPoolSize: 20,
        minPoolSize: 2,
        maxIdleTimeMS: 30000,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000
      });

      await mongoClient.connect();
      this.db = mongoClient.db();
      this.dbService = new FastifyDbService(this.db);

      console.log('Scrape worker database connected');

      // Initialize job processors
      await this.setupJobProcessors();
      
      this.isInitialized = true;
      console.log('Fastify scrape worker initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize scrape worker:', error);
      throw error;
    }
  }

  async setupJobProcessors() {
    // Movie scraping worker
    this.jobQueue.createWorker('scrape', async (job) => {
      return await this.processJob(job);
    }, {
      concurrency: 10,
      maxStalledCount: 1,
      stalledInterval: 30000
    });

    // Metadata enrichment worker
    this.jobQueue.createWorker('metadata', async (job) => {
      return await this.processMetadataJob(job);
    }, {
      concurrency: 8,
      maxStalledCount: 1,
      stalledInterval: 30000
    });

    // Trending update worker
    this.jobQueue.createWorker('trending', async (job) => {
      return await this.processTrendingJob(job);
    }, {
      concurrency: 2,
      maxStalledCount: 1,
      stalledInterval: 60000
    });

    console.log('Job processors setup completed');
  }

  async processJob(job) {
    const { type, data } = job.data;
    const startTime = Date.now();

    try {
      let result;
      
      switch (type) {
        case 'scrapeMovies':
          result = await this.scrapeMoviesOptimized(data);
          break;
        case 'scrapeSeries':
          result = await this.scrapeSeriesOptimized(data);
          break;
        case 'scrapeAnime':
          result = await this.scrapeAnimeOptimized(data);
          break;
        case 'scrapeLiveTV':
          result = await this.scrapeLiveTVOptimized(data);
          break;
        default:
          throw new Error(`Unknown job type: ${type}`);
      }

      const duration = Date.now() - startTime;
      console.log(`Job ${job.id} (${type}) completed in ${duration}ms`);
      
      return {
        success: true,
        result,
        duration,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`Job ${job.id} (${type}) failed after ${duration}ms:`, error);
      
      throw {
        message: error.message,
        stack: error.stack,
        duration,
        timestamp: new Date().toISOString()
      };
    }
  }

  async processMetadataJob(job) {
    const { type, data } = job.data;
    const startTime = Date.now();

    try {
      let result;
      
      switch (type) {
        case 'enrichTMDB':
          result = await this.enrichTMDBMetadata(data);
          break;
        case 'enrichJikan':
          result = await this.enrichJikanMetadata(data);
          break;
        case 'enrichGemini':
          result = await this.enrichGeminiMetadata(data);
          break;
        default:
          throw new Error(`Unknown metadata job type: ${type}`);
      }

      const duration = Date.now() - startTime;
      console.log(`Metadata job ${job.id} (${type}) completed in ${duration}ms`);
      
      return {
        success: true,
        result,
        duration,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`Metadata job ${job.id} (${type}) failed after ${duration}ms:`, error);
      
      throw {
        message: error.message,
        stack: error.stack,
        duration,
        timestamp: new Date().toISOString()
      };
    }
  }

  async processTrendingJob(job) {
    const { type, data } = job.data;
    const startTime = Date.now();

    try {
      let result;
      
      switch (type) {
        case 'updateTrendingMovies':
          result = await this.updateTrendingMovies(data);
          break;
        case 'updateTrendingSeries':
          result = await this.updateTrendingSeries(data);
          break;
        case 'updateTrendingAnime':
          result = await this.updateTrendingAnime(data);
          break;
        default:
          throw new Error(`Unknown trending job type: ${type}`);
      }

      const duration = Date.now() - startTime;
      console.log(`Trending job ${job.id} (${type}) completed in ${duration}ms`);
      
      return {
        success: true,
        result,
        duration,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`Trending job ${job.id} (${type}) failed after ${duration}ms:`, error);
      
      throw {
        message: error.message,
        stack: error.stack,
        duration,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Optimized scraping methods (stubs for now)
  async scrapeMoviesOptimized(data) {
    // Import and use existing scraping logic with optimizations
    const { scrapeMovies } = require('../scrapers/movieScraper');
    return await scrapeMovies(data, this.dbService);
  }

  async scrapeSeriesOptimized(data) {
    const { scrapeSeries } = require('../scrapers/seriesScraper');
    return await scrapeSeries(data, this.dbService);
  }

  async scrapeAnimeOptimized(data) {
    const { scrapeAnime } = require('../scrapers/animeScraper');
    return await scrapeAnime(data, this.dbService);
  }

  async scrapeLiveTVOptimized(data) {
    const { scrapeLiveTV } = require('../scrapers/livetvScraper');
    return await scrapeLiveTV(data, this.dbService);
  }

  // Metadata enrichment methods
  async enrichTMDBMetadata(data) {
    const { enrichWithTMDB } = require('../metadata/tmdbEnricher');
    return await enrichWithTMDB(data, this.dbService);
  }

  async enrichJikanMetadata(data) {
    const { enrichWithJikan } = require('../metadata/jikanEnricher');
    return await enrichWithJikan(data, this.dbService);
  }

  async enrichGeminiMetadata(data) {
    const { enrichWithGemini } = require('../metadata/geminiEnricher');
    return await enrichWithGemini(data, this.dbService);
  }

  // Trending update methods
  async updateTrendingMovies(data) {
    const { updateTrendingMovies } = require('../trending/trendingUpdater');
    return await updateTrendingMovies(data, this.dbService);
  }

  async updateTrendingSeries(data) {
    const { updateTrendingSeries } = require('../trending/trendingUpdater');
    return await updateTrendingSeries(data, this.dbService);
  }

  async updateTrendingAnime(data) {
    const { updateTrendingAnime } = require('../trending/trendingUpdater');
    return await updateTrendingAnime(data, this.dbService);
  }

  // Job management methods
  async addScrapeJob(type, data, options = {}) {
    return await this.jobQueue.addJob('scrape', type, data, {
      priority: options.priority || 0,
      delay: options.delay || 0,
      attempts: this.config.retryAttempts,
      backoff: {
        type: 'exponential',
        delay: this.config.retryDelay
      }
    });
  }

  async addMetadataJob(type, data, options = {}) {
    return await this.jobQueue.addJob('metadata', type, data, {
      priority: options.priority || 0,
      delay: options.delay || 0,
      attempts: this.config.retryAttempts,
      backoff: {
        type: 'exponential',
        delay: this.config.retryDelay
      }
    });
  }

  async addTrendingJob(type, data, options = {}) {
    return await this.jobQueue.addJob('trending', type, data, {
      priority: options.priority || 0,
      delay: options.delay || 0,
      attempts: this.config.retryAttempts,
      backoff: {
        type: 'exponential',
        delay: this.config.retryDelay
      }
    });
  }

  // Schedule recurring jobs
  async scheduleRecurringJobs() {
    // Schedule trending updates every 6 hours
    await this.jobQueue.addRecurringJob(
      'trending',
      'updateTrendingMovies',
      {},
      '0 */6 * * *'
    );

    await this.jobQueue.addRecurringJob(
      'trending',
      'updateTrendingSeries',
      {},
      '0 */6 * * *'
    );

    await this.jobQueue.addRecurringJob(
      'trending',
      'updateTrendingAnime',
      {},
      '0 */6 * * *'
    );

    console.log('Recurring jobs scheduled');
  }

  // Get worker statistics
  async getStats() {
    const [scrapeStats, metadataStats, trendingStats] = await Promise.all([
      this.jobQueue.getQueueStats('scrape'),
      this.jobQueue.getQueueStats('metadata'),
      this.jobQueue.getQueueStats('trending')
    ]);

    return {
      scrape: scrapeStats,
      metadata: metadataStats,
      trending: trendingStats,
      health: await this.jobQueue.healthCheck()
    };
  }

  // Graceful shutdown
  async shutdown() {
    console.log('Shutting down scrape worker...');
    await this.jobQueue.close();
    console.log('Scrape worker shutdown complete');
  }
}

module.exports = FastifyScrapeWorker;
