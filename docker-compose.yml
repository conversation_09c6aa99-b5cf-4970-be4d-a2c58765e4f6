version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: netstream-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  backend:
    build: .
    container_name: netstream-backend-container
    ports:
      - "3001:3001"
    environment:
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

volumes:
  redis_data:
