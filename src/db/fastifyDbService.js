// File: src/db/fastifyDbService.js
// Optimized Database Service for Fastify Migration
// Replaces Mongoose with native MongoDB driver for better performance

const { ObjectId } = require('mongodb');

class FastifyDbService {
  constructor(db) {
    this.db = db;
    this.collections = {
      movies: db.collection('movies'),
      series: db.collection('series'),
      animes: db.collection('animes'),
      livetv: db.collection('livetv'),
      trending: db.collection('trending_items'),
      config: db.collection('config'),
      admin: db.collection('admin')
    };
  }

  // Generic CRUD operations with optimizations
  async findById(collection, id, projection = {}) {
    if (!ObjectId.isValid(id)) {
      throw new Error('Invalid ObjectId format');
    }
    
    return await this.collections[collection].findOne(
      { _id: new ObjectId(id) },
      { projection }
    );
  }

  async findMany(collection, query = {}, options = {}) {
    const {
      sort = { updatedAt: -1 },
      limit = 20,
      skip = 0,
      projection = {}
    } = options;

    const cursor = this.collections[collection]
      .find(query, { projection })
      .sort(sort)
      .skip(skip)
      .limit(limit);

    return await cursor.toArray();
  }

  async findWithAggregation(collection, pipeline) {
    return await this.collections[collection].aggregate(pipeline).toArray();
  }

  async count(collection, query = {}) {
    return await this.collections[collection].countDocuments(query);
  }

  async insertOne(collection, document) {
    const now = new Date();
    const docWithTimestamps = {
      ...document,
      createdAt: now,
      updatedAt: now
    };
    
    const result = await this.collections[collection].insertOne(docWithTimestamps);
    return { ...docWithTimestamps, _id: result.insertedId };
  }

  async updateOne(collection, filter, update, options = {}) {
    const updateDoc = {
      $set: {
        ...update,
        updatedAt: new Date()
      }
    };

    return await this.collections[collection].updateOne(filter, updateDoc, options);
  }

  async deleteOne(collection, filter) {
    return await this.collections[collection].deleteOne(filter);
  }

  async upsert(collection, filter, document) {
    const now = new Date();
    const updateDoc = {
      $set: {
        ...document,
        updatedAt: now
      },
      $setOnInsert: {
        createdAt: now
      }
    };

    return await this.collections[collection].updateOne(
      filter,
      updateDoc,
      { upsert: true }
    );
  }

  // Optimized search with text index
  async textSearch(collections, query, options = {}) {
    const {
      limit = 20,
      skip = 0,
      projection = {}
    } = options;

    const searchPromises = collections.map(async (collectionName) => {
      try {
        const results = await this.collections[collectionName]
          .find(
            { $text: { $search: query } },
            { 
              projection: { ...projection, score: { $meta: 'textScore' } }
            }
          )
          .sort({ score: { $meta: 'textScore' }, updatedAt: -1 })
          .skip(skip)
          .limit(limit)
          .toArray();

        return results.map(item => ({
          ...item,
          __typename: this.getTypeName(collectionName)
        }));
      } catch (error) {
        // Fallback to regex search if text index doesn't exist
        const regexQuery = new RegExp(query, 'i');
        const fallbackQuery = {
          $or: [
            { title: regexQuery },
            { cleanedTitle: regexQuery },
            { 'metadata.actors': regexQuery },
            { 'tmdb.title': regexQuery }
          ]
        };

        const results = await this.collections[collectionName]
          .find(fallbackQuery, { projection })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit)
          .toArray();

        return results.map(item => ({
          ...item,
          __typename: this.getTypeName(collectionName)
        }));
      }
    });

    const allResults = await Promise.all(searchPromises);
    return allResults.flat();
  }

  // Optimized trending queries
  async getTrendingItems(mediaType, page = 1, limit = 20) {
    const skip = (page - 1) * limit;
    
    // Get trending IDs from trending collection
    const trendingIds = await this.collections.trending
      .find(
        { mediaType, source: 'tmdb' },
        { projection: { tmdbId: 1, rank: 1 } }
      )
      .sort({ rank: 1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    if (trendingIds.length === 0) {
      // Fallback to latest items
      const collectionName = mediaType === 'movie' ? 'movies' : 
                           mediaType === 'tv' ? 'series' : 'animes';
      return await this.findMany(collectionName, {}, { limit, skip });
    }

    // Get actual items using aggregation for better performance
    const tmdbIds = trendingIds.map(item => item.tmdbId);
    const rankMap = new Map(trendingIds.map(item => [item.tmdbId, item.rank]));

    const collectionName = mediaType === 'movie' ? 'movies' : 
                         mediaType === 'tv' ? 'series' : 'animes';

    const items = await this.collections[collectionName]
      .find({ 'tmdb.id': { $in: tmdbIds } })
      .toArray();

    // Sort by original trending rank
    return items.sort((a, b) => {
      const rankA = rankMap.get(a.tmdb?.id) ?? Infinity;
      const rankB = rankMap.get(b.tmdb?.id) ?? Infinity;
      return rankA - rankB;
    });
  }

  // Genre-based queries with caching optimization
  async getItemsByGenre(collection, genre, options = {}) {
    const {
      limit = 20,
      skip = 0,
      sort = { updatedAt: -1 }
    } = options;

    return await this.collections[collection]
      .find(
        { 'tmdb.genres': genre },
        { projection: this.getBasicProjection() }
      )
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .toArray();
  }

  // Get available genres efficiently
  async getAvailableGenres() {
    const pipeline = [
      { $unwind: '$tmdb.genres' },
      { $group: { _id: '$tmdb.genres' } },
      { $sort: { _id: 1 } },
      { $project: { _id: 0, genre: '$_id' } }
    ];

    const [movieGenres, seriesGenres, animeGenres] = await Promise.all([
      this.collections.movies.aggregate(pipeline).toArray(),
      this.collections.series.aggregate(pipeline).toArray(),
      this.collections.animes.aggregate(pipeline).toArray()
    ]);

    return {
      movies: movieGenres.map(g => g.genre).filter(Boolean),
      series: seriesGenres.map(g => g.genre).filter(Boolean),
      anime: animeGenres.map(g => g.genre).filter(Boolean)
    };
  }

  // Optimized latest queries with exclusions
  async getLatestMovies(excludeAncien = true, page = 1, limit = 20) {
    const skip = (page - 1) * limit;
    const query = excludeAncien ? 
      { detailUrl: { $not: { $regex: 'film-ancien', $options: 'i' } } } : {};

    return await this.findMany('movies', query, { 
      limit, 
      skip, 
      projection: this.getBasicProjection() 
    });
  }

  async getAncienMovies(page = 1, limit = 20) {
    const skip = (page - 1) * limit;
    const query = { detailUrl: { $regex: 'film-ancien', $options: 'i' } };

    return await this.findMany('movies', query, { 
      limit, 
      skip, 
      projection: this.getBasicProjection() 
    });
  }

  // Database statistics
  async getDatabaseStats() {
    const [movieCount, seriesCount, animeCount, livetvCount] = await Promise.all([
      this.count('movies'),
      this.count('series'),
      this.count('animes'),
      this.count('livetv')
    ]);

    return {
      movies: movieCount,
      series: seriesCount,
      anime: animeCount,
      livetv: livetvCount,
      totalItems: movieCount + seriesCount + animeCount + livetvCount
    };
  }

  // Helper methods
  getTypeName(collectionName) {
    const typeMap = {
      movies: 'Movie',
      series: 'Series',
      animes: 'Anime',
      livetv: 'LiveTV'
    };
    return typeMap[collectionName] || 'Unknown';
  }

  getBasicProjection() {
    return {
      title: 1,
      displayTitle: 1,
      thumbnail: 1,
      image: 1,
      detailUrl: 1,
      detailUrlPath: 1,
      tmdb: 1,
      jikan: 1,
      metadata: 1,
      updatedAt: 1,
      createdAt: 1
    };
  }

  // Ensure indexes for performance
  async ensureIndexes() {
    const indexPromises = [];

    // Text search indexes
    indexPromises.push(
      this.collections.movies.createIndex({
        title: 'text',
        cleanedTitle: 'text',
        'metadata.actors': 'text',
        'tmdb.title': 'text'
      }, { 
        weights: { title: 10, cleanedTitle: 8, 'tmdb.title': 6, 'metadata.actors': 2 },
        name: 'text_search_index'
      })
    );

    // Performance indexes
    const performanceIndexes = [
      { collection: 'movies', index: { updatedAt: -1 } },
      { collection: 'movies', index: { 'tmdb.id': 1 } },
      { collection: 'movies', index: { 'tmdb.genres': 1 } },
      { collection: 'movies', index: { detailUrl: 1 } },
      { collection: 'series', index: { updatedAt: -1 } },
      { collection: 'series', index: { 'tmdb.id': 1 } },
      { collection: 'series', index: { 'tmdb.genres': 1 } },
      { collection: 'animes', index: { updatedAt: -1 } },
      { collection: 'animes', index: { 'jikan.mal_id': 1 } },
      { collection: 'trending', index: { mediaType: 1, rank: 1 } }
    ];

    performanceIndexes.forEach(({ collection, index }) => {
      indexPromises.push(this.collections[collection].createIndex(index));
    });

    try {
      await Promise.all(indexPromises);
      console.log('Database indexes ensured successfully');
    } catch (error) {
      console.warn('Some indexes may already exist:', error.message);
    }
  }
}

module.exports = FastifyDbService;
