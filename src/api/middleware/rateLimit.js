const logger = require('../../utils/logger');

logger.info('Loading rateLimit module');

const requests = new Map();

function rateLimit(req, res, next) {
  const ip = req.ip;
  const now = Date.now();
  const windowMs = 10 * 1000;
  const maxRequests = 40;

  if (!requests.has(ip)) requests.set(ip, []);
  const userRequests = requests.get(ip).filter(t => now - t < windowMs);

  if (userRequests.length >= maxRequests) {
    logger.warn(`Rate limit exceeded for IP ${ip}`);
    return res.status(429).json({ error: 'Too Many Requests' });
  }

  userRequests.push(now);
  requests.set(ip, userRequests);
  logger.info(`Request from ${ip} to ${req.path}`);
  next();
}

module.exports = rateLimit;  // Single function export