/**
 * Addic7ed.com Subtitle Scraper V2
 * 
 * Based on direct analysis of the Addic7ed.com website structure.
 * 
 * Website Structure:
 * - Main page: https://www.addic7ed.com/
 * - Show page: https://www.addic7ed.com/show/[show_id]
 * - Season page: https://www.addic7ed.com/show/[show_id]/[season]
 * - Episode page: https://www.addic7ed.com/show/[show_id]/[season]/[episode]
 * - Episode page (by name): https://www.addic7ed.com/serie/[show_name]/[season]/[episode]/[episode_name]
 * - Download link: https://www.addic7ed.com/original/[subtitle_id]/[version]
 * 
 * Download links format:
 * - /original/195225/1 (where 195225 is the subtitle ID and 1 is the version)
 * 
 * Subtitle tables:
 * - Each version has a table with class "tabel95"
 * - Each language has a row with class "language"
 * - Download links are in <a> tags with class "buttonDownload"
 */

const fetch = require('node-fetch');
const cheerio = require('cheerio');
const logger = require('../utils/logger');

/**
 * Common headers for all requests to Addic7ed.com
 */
const COMMON_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'Accept-Language': 'en-US,en;q=0.9',
  'Referer': 'https://www.addic7ed.com/',
  'Connection': 'keep-alive',
  'Cache-Control': 'max-age=0'
};

/**
 * Get the URL for an episode by show name
 * @param {string} showName - Show name
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @param {string} episodeName - Episode name (optional)
 * @returns {string} Episode URL
 */
function getEpisodeUrl(showName, season, episode, episodeName = '') {
  // Format the show name for the URL (replace spaces with underscores)
  const formattedShowName = showName.replace(/\s+/g, '_');
  
  // Build the URL
  let url = `https://www.addic7ed.com/serie/${formattedShowName}/${season}/${episode}`;
  
  // Add episode name if provided
  if (episodeName) {
    const formattedEpisodeName = episodeName.replace(/\s+/g, '_');
    url += `/${formattedEpisodeName}`;
  }
  
  return url;
}

/**
 * Fetch the HTML content of a URL
 * @param {string} url - URL to fetch
 * @returns {Promise<string|null>} HTML content or null if failed
 */
async function fetchHtml(url) {
  try {
    logger.info(`Fetching URL: ${url}`);
    
    const response = await fetch(url, {
      headers: COMMON_HEADERS,
      timeout: 10000
    });
    
    if (!response.ok) {
      logger.warn(`Failed to fetch URL: ${url}, status: ${response.status}`);
      return null;
    }
    
    const html = await response.text();
    return html;
  } catch (error) {
    logger.error(`Error fetching URL: ${url}`, error);
    return null;
  }
}

/**
 * Extract subtitle information from HTML
 * @param {string} html - HTML content
 * @param {string} language - Language to filter by (optional)
 * @returns {Array} Array of subtitle objects
 */
function extractSubtitles(html, language = '') {
  try {
    const $ = cheerio.load(html);
    const subtitles = [];
    
    // Get the show title
    const showTitle = $('span.titulo').first().text().trim();
    logger.info(`Extracting subtitles for: ${showTitle}`);
    
    // Find all subtitle tables
    $('table.tabel95').each((i, table) => {
      // Skip the first table which is usually the header
      if (i === 0) return;
      
      // Get the version from the NewsTitle class
      const versionElement = $(table).find('td.NewsTitle');
      if (versionElement.length === 0) return;
      
      const versionText = versionElement.text();
      const versionMatch = versionText.match(/Version\s+(.*?),/);
      const version = versionMatch ? versionMatch[1].trim() : 'Unknown';
      
      // Find all language rows
      $(table).find('td.language').each((j, langCell) => {
        const langText = $(langCell).text().trim();
        
        // Skip if language doesn't match (if specified)
        if (language && langText.toLowerCase() !== language.toLowerCase()) {
          return;
        }
        
        // Find the download link
        const row = $(langCell).closest('tr');
        const downloadLink = row.find('a.buttonDownload').attr('href');
        
        if (!downloadLink) {
          return;
        }
        
        // Check if completed
        const statusCell = row.find('td b');
        const status = statusCell.text().trim();
        
        if (status !== 'Completed') {
          return;
        }
        
        // Check for hearing impaired
        const hearingImpairedImg = row.find('img[title="Hearing Impaired"]');
        const hearingImpaired = hearingImpairedImg.length > 0;
        
        // Get download count and sequence count
        const statsText = row.find('td.newsDate').text();
        const downloadsMatch = statsText.match(/(\d+)\s+Downloads/);
        const sequencesMatch = statsText.match(/(\d+)\s+sequences/);
        
        const downloads = downloadsMatch ? parseInt(downloadsMatch[1], 10) : 0;
        const sequences = sequencesMatch ? parseInt(sequencesMatch[1], 10) : 0;
        
        // Add to subtitles array
        subtitles.push({
          language: langText,
          version,
          download: `https://www.addic7ed.com${downloadLink}`,
          hearing_impaired: hearingImpaired,
          downloads,
          sequences,
          show_title: showTitle
        });
      });
    });
    
    logger.info(`Found ${subtitles.length} subtitles`);
    return subtitles;
  } catch (error) {
    logger.error('Error extracting subtitles', error);
    return [];
  }
}

/**
 * Get subtitles for a show
 * @param {string} showName - Show name
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @param {string} language - Language to filter by (optional)
 * @returns {Promise<Array>} Array of subtitle objects
 */
async function getSubtitles(showName, season, episode, language = '') {
  try {
    logger.info(`Getting subtitles for: ${showName}, Season ${season}, Episode ${episode}, Language: ${language || 'all'}`);
    
    // Try with the original show name
    const url = getEpisodeUrl(showName, season, episode);
    const html = await fetchHtml(url);
    
    if (html) {
      // Check if the page contains subtitle tables
      if (html.includes('tabel95') && html.includes('buttonDownload')) {
        return extractSubtitles(html, language);
      }
    }
    
    // Try with alternative show names
    const alternativeNames = generateAlternativeNames(showName);
    
    for (const altName of alternativeNames) {
      if (altName === showName) continue;
      
      logger.info(`Trying alternative name: ${altName}`);
      const altUrl = getEpisodeUrl(altName, season, episode);
      const altHtml = await fetchHtml(altUrl);
      
      if (altHtml && altHtml.includes('tabel95') && altHtml.includes('buttonDownload')) {
        return extractSubtitles(altHtml, language);
      }
    }
    
    logger.warn(`No subtitles found for ${showName}, Season ${season}, Episode ${episode}`);
    return [];
  } catch (error) {
    logger.error('Error getting subtitles', error);
    return [];
  }
}

/**
 * Generate alternative names for a show
 * @param {string} showName - Original show name
 * @returns {Array<string>} Array of alternative names
 */
function generateAlternativeNames(showName) {
  const alternatives = [showName];
  
  // Remove "The" from the beginning
  if (showName.toLowerCase().startsWith('the ')) {
    alternatives.push(showName.substring(4));
  }
  
  // Add "The" to the beginning
  if (!showName.toLowerCase().startsWith('the ')) {
    alternatives.push(`The ${showName}`);
  }
  
  // Replace spaces with dots
  alternatives.push(showName.replace(/\s+/g, '.'));
  
  // Replace spaces with hyphens
  alternatives.push(showName.replace(/\s+/g, '-'));
  
  // Remove special characters
  alternatives.push(showName.replace(/[^\w\s]/g, ''));
  
  // Convert to lowercase
  alternatives.push(showName.toLowerCase());
  
  // Remove duplicate alternatives
  return [...new Set(alternatives)];
}

module.exports = {
  getSubtitles,
  extractSubtitles,
  fetchHtml,
  getEpisodeUrl,
  generateAlternativeNames
};
