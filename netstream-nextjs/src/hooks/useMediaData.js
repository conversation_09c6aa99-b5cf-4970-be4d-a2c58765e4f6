'use client'
import { useState, useEffect, useCallback } from 'react'
import { useQuery } from '@apollo/client'
import { GET_MOVIES, GET_SERIES, GET_ANIME, GET_LIVETV } from '@/lib/queries'

const QUERY_MAP = {
  movies: GET_MOVIES,
  series: GET_SERIES,
  anime: GET_ANIME,
  livetv: GET_LIVETV
}

export function useMediaData(type, options = {}) {
  const [data, setData] = useState([])
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)

  const {
    limit = 20,
    sort = 'LATEST',
    ...queryOptions
  } = options

  const query = QUERY_MAP[type]

  const { data: queryData, loading, error, fetchMore } = useQuery(query, {
    variables: {
      limit,
      page: 1,
      sort
    },
    notifyOnNetworkStatusChange: true,
    errorPolicy: 'all',
    ...queryOptions
  })

  useEffect(() => {
    if (queryData) {
      const newData = queryData[type] || []
      setData(newData)
      setHasMore(newData.length === limit)
      setPage(1)
    }
  }, [queryData, type, limit])

  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return

    try {
      const nextPage = page + 1
      const { data: moreData } = await fetchMore({
        variables: {
          page: nextPage,
          limit,
          sort
        }
      })

      const newItems = moreData[type] || []

      if (newItems.length > 0) {
        setData(prev => [...prev, ...newItems])
        setPage(nextPage)
        setHasMore(newItems.length === limit)
      } else {
        setHasMore(false)
      }
    } catch (err) {
      console.error('Error loading more data:', err)
      setHasMore(false)
    }
  }, [fetchMore, hasMore, loading, page, limit, sort, type])

  const refresh = useCallback(() => {
    setData([])
    setPage(1)
    setHasMore(true)
  }, [])

  return {
    data,
    loading,
    error,
    hasMore,
    loadMore,
    refresh
  }
}
