// File: scripts/check_db.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');
const Movie = require('../src/db/models/Movie');

async function checkDatabase() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Check for series with tmdbSeasons
    const seriesWithSeasons = await Series.findOne(
      { tmdbSeasons: { $exists: true, $ne: [] } },
      { title: 1, tmdbSeasons: { $slice: 1 } }
    );
    
    if (seriesWithSeasons) {
      logger.info(`Found series with tmdbSeasons: ${seriesWithSeasons.title}`);
      logger.info(`First season: ${JSON.stringify(seriesWithSeasons.tmdbSeasons[0])}`);
    } else {
      logger.warn('No series found with tmdbSeasons');
      
      // Check for any series
      const anySeries = await Series.findOne({}, { title: 1, tmdb: 1 });
      if (anySeries) {
        logger.info(`Found series without tmdbSeasons: ${anySeries.title}`);
        logger.info(`TMDB data: ${JSON.stringify(anySeries.tmdb)}`);
      } else {
        logger.warn('No series found at all');
      }
    }

    // Check for anime with jikanSeasons
    const animeWithSeasons = await Anime.findOne(
      { jikanSeasons: { $exists: true, $ne: [] } },
      { title: 1, jikanSeasons: { $slice: 1 } }
    );
    
    if (animeWithSeasons) {
      logger.info(`Found anime with jikanSeasons: ${animeWithSeasons.title}`);
      logger.info(`First season: ${JSON.stringify(animeWithSeasons.jikanSeasons[0])}`);
    } else {
      logger.warn('No anime found with jikanSeasons');
    }

    // Check for specific series mentioned in the logs
    const walkingDead = await Series.findOne(
      { title: { $regex: /Walking Dead.*Dead City/i } },
      { title: 1, tmdbSeasons: 1, tmdb: 1 }
    );
    
    if (walkingDead) {
      logger.info(`Found Walking Dead: Dead City`);
      logger.info(`TMDB data: ${JSON.stringify(walkingDead.tmdb)}`);
      logger.info(`Has tmdbSeasons: ${walkingDead.tmdbSeasons ? walkingDead.tmdbSeasons.length : 'No'}`);
    } else {
      logger.warn('Walking Dead: Dead City not found');
    }

    const lastOfUs = await Series.findOne(
      { title: { $regex: /Last Of Us/i } },
      { title: 1, tmdbSeasons: 1, tmdb: 1 }
    );
    
    if (lastOfUs) {
      logger.info(`Found The Last Of Us`);
      logger.info(`TMDB data: ${JSON.stringify(lastOfUs.tmdb)}`);
      logger.info(`Has tmdbSeasons: ${lastOfUs.tmdbSeasons ? lastOfUs.tmdbSeasons.length : 'No'}`);
    } else {
      logger.warn('The Last Of Us not found');
    }

    // Check for HPI and Power Book III
    const hpi = await Series.findOne(
      { title: 'HPI' },
      { title: 1, tmdbSeasons: 1, tmdb: 1 }
    );
    
    if (hpi) {
      logger.info(`Found HPI`);
      logger.info(`TMDB data: ${JSON.stringify(hpi.tmdb)}`);
      logger.info(`Has tmdbSeasons: ${hpi.tmdbSeasons ? hpi.tmdbSeasons.length : 'No'}`);
    } else {
      logger.warn('HPI not found');
    }

    const powerBook = await Series.findOne(
      { title: { $regex: /Power Book III/i } },
      { title: 1, tmdbSeasons: 1, tmdb: 1 }
    );
    
    if (powerBook) {
      logger.info(`Found Power Book III`);
      logger.info(`TMDB data: ${JSON.stringify(powerBook.tmdb)}`);
      logger.info(`Has tmdbSeasons: ${powerBook.tmdbSeasons ? powerBook.tmdbSeasons.length : 'No'}`);
    } else {
      logger.warn('Power Book III not found');
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error checking database: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

checkDatabase();
