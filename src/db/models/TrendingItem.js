// File: src/db/models/TrendingItem.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const TrendingItemSchema = new Schema({
  tmdbId: {
    type: Number,
    required: true,
    index: true,
    description: "The Movie Database ID (TMDb ID)"
  },
  mediaType: {
    type: String,
    enum: ['movie', 'tv'], // Corresponds to TMDb media types ('tv' covers Series/Anime)
    required: true,
    index: true,
    description: "The type of media (movie or tv)"
  },
  rank: {
    type: Number,
    required: true,
    description: "The 0-based rank/position in the TMDb trending list"
  },
  source: {
    type: String,
    default: 'tmdb', // Source of the trending data (can be extended later)
    index: true
  },
  fetchedAt: {
    type: Date,
    default: Date.now,
    // Automatically remove documents older than 24 hours using MongoDB's TTL feature
    // Ensure this index is created on your MongoDB collection:
    // db.trendingitems.createIndex({ "fetchedAt": 1 }, { expireAfterSeconds: 86400 }) // 86400 seconds = 24 hours
    expires: '24h'
  }
}, {
  // Disable default timestamps if using 'fetchedAt' as the primary time field
  timestamps: false
});

// Compound index for efficient querying and sorting by rank
TrendingItemSchema.index({ mediaType: 1, source: 1, rank: 1 });
// Index for TTL cleanup (must be created manually or via Mongoose syncIndexes if enabled)
TrendingItemSchema.index({ fetchedAt: 1 }, { expireAfterSeconds: 24 * 60 * 60 }); // 24 hours TTL

module.exports = mongoose.model('TrendingItem', TrendingItemSchema);