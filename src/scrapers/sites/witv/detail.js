const axios = require('axios');
const cheerio = require('cheerio');
const { WITV_BASE } = require('../../../config/constants');
const logger = require('../../../utils/logger');
const Config = require('../../../db/models/Config');

async function scrapeWitvDetail(detailUrl) {
  try {
    // Get the latest WITV_BASE from the database
    const witvBase = await Config.getValue('WITV_BASE', WITV_BASE);
    logger.info(`Using WiTV base URL for detail scraping: ${witvBase}`);

    // Create an axios instance that maintains cookies
    const axiosInstance = axios.create({
      withCredentials: true,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,fr;q=0.8',
        'Referer': `https://${witvBase}/chaines-live/`,
        'Origin': `https://${witvBase}`
      }
    });

    // Store cookies between requests
    let cookies = '';

    // First request to get the channel page and cookies
    const response = await axiosInstance.get(detailUrl);
    if (response.headers['set-cookie']) {
      cookies = response.headers['set-cookie'].map(cookie => cookie.split(';')[0]).join('; ');
      logger.info(`Got cookies from channel page: ${cookies}`);
    }

    const $ = cheerio.load(response.data);

    const image = $('.ann-full_poster img.xfieldimage.poster').attr('src') || $('.channel-image').attr('src') || '';
    const streamingUrls = [];

    // Look for the iframe or player container
    const iframeSrc = $('.iframe-container iframe').attr('src') ||
                     $('.player-container iframe').attr('src') ||
                     $('iframe[src*="player"]').attr('src');

    if (iframeSrc) {
      const playerUrl = iframeSrc.startsWith('http') ? iframeSrc : `https://${witvBase}${iframeSrc}`;
      logger.info(`Found player URL: ${playerUrl}`);

      // Second fetch to get the player page with cookies
      const playerResponse = await axiosInstance.get(playerUrl, {
        headers: {
          'Cookie': cookies,
          'Referer': detailUrl
        }
      });

      // Update cookies if new ones are set
      if (playerResponse.headers['set-cookie']) {
        const newCookies = playerResponse.headers['set-cookie'].map(cookie => cookie.split(';')[0]).join('; ');
        cookies = cookies ? `${cookies}; ${newCookies}` : newCookies;
        logger.info(`Updated cookies from player page: ${cookies}`);
      }

      const player$ = cheerio.load(playerResponse.data);

      // Try different patterns to find the stream URL
      let streamUrl = null;

      // Pattern 1: streamUrl variable
      const scriptText = player$('script:contains("streamUrl")').text();
      const streamMatch = scriptText.match(/streamUrl\s*=\s*"([^"]+)"/);
      if (streamMatch && streamMatch[1]) {
        streamUrl = streamMatch[1];
        logger.info(`Found stream URL using pattern 1: ${streamUrl}`);
      }

      // Pattern 2: source tag
      if (!streamUrl) {
        const sourceTag = player$('source[src]');
        if (sourceTag.length > 0) {
          streamUrl = sourceTag.attr('src');
          logger.info(`Found stream URL using pattern 2: ${streamUrl}`);
        }
      }

      // Pattern 3: video tag
      if (!streamUrl) {
        const videoTag = player$('video[src]');
        if (videoTag.length > 0) {
          streamUrl = videoTag.attr('src');
          logger.info(`Found stream URL using pattern 3: ${streamUrl}`);
        }
      }

      // Pattern 4: iframe src
      if (!streamUrl) {
        const innerIframe = player$('iframe[src*=".m3u8"]');
        if (innerIframe.length > 0) {
          streamUrl = innerIframe.attr('src');
          logger.info(`Found stream URL using pattern 4: ${streamUrl}`);
        }
      }

      // Pattern 5: Look for any m3u8 URL in the HTML
      if (!streamUrl) {
        const m3u8Match = playerResponse.data.match(/(https?:\/\/[^"'\s]+\.m3u8[^"'\s]*)/);
        if (m3u8Match && m3u8Match[1]) {
          streamUrl = m3u8Match[1];
          logger.info(`Found stream URL using pattern 5: ${streamUrl}`);
        }
      }

      if (streamUrl) {
        // For witv URLs, we need to get the token
        if (streamUrl.includes(witvBase) || streamUrl.includes('play.witv')) {
          try {
            // Make a request to the stream URL to get the token
            logger.info(`Making request to stream URL to get token: ${streamUrl}`);
            const streamResponse = await axiosInstance.get(streamUrl, {
              headers: {
                'Cookie': cookies,
                'Referer': playerUrl,
                'Origin': new URL(playerUrl).origin,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
              },
              maxRedirects: 0,
              validateStatus: status => status >= 200 && status < 400
            });

            // Check if we got a redirect with a token
            if (streamResponse.status === 302 && streamResponse.headers.location) {
              const redirectUrl = streamResponse.headers.location;
              logger.info(`Got redirect URL with token: ${redirectUrl}`);

              // Update the stream URL with the token
              streamUrl = redirectUrl;
            }

            // Update cookies if new ones are set
            if (streamResponse.headers['set-cookie']) {
              const newCookies = streamResponse.headers['set-cookie'].map(cookie => cookie.split(';')[0]).join('; ');
              cookies = cookies ? `${cookies}; ${newCookies}` : newCookies;
              logger.info(`Updated cookies from stream URL: ${cookies}`);
            }
          } catch (error) {
            // If we get a redirect error, try to extract the location header
            if (error.response && error.response.status === 302 && error.response.headers.location) {
              const redirectUrl = error.response.headers.location;
              logger.info(`Got redirect URL with token from error: ${redirectUrl}`);

              // Update the stream URL with the token
              streamUrl = redirectUrl;

              // Update cookies if new ones are set
              if (error.response.headers['set-cookie']) {
                const newCookies = error.response.headers['set-cookie'].map(cookie => cookie.split(';')[0]).join('; ');
                cookies = cookies ? `${cookies}; ${newCookies}` : newCookies;
                logger.info(`Updated cookies from stream URL error: ${cookies}`);
              }
            } else {
              logger.warn(`Error getting token for stream URL: ${error.message}`);
            }
          }
        }

        // Add the stream URL with cookies
        streamingUrls.push({
          url: streamUrl,
          provider: 'witv',
          lastChecked: new Date(),
          isActive: true,
          // Store cookies for authentication
          headers: {
            'Cookie': cookies,
            'Referer': playerUrl,
            'Origin': new URL(playerUrl).origin,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        });
      } else {
        logger.warn(`No stream URL found in ${playerUrl}`);
        // Fallback to player URL
        streamingUrls.push({
          url: playerUrl,
          provider: 'witv',
          lastChecked: new Date(),
          isActive: true,
          method: 'iframe'
        });
      }
    }

    logger.info(`Scraped WiTV detail page: ${detailUrl} | Streams: ${streamingUrls.length}`);
    return {
      image: image.startsWith('http') ? image : `https://${witvBase}${image}`,
      streamingUrls
    };
  } catch (error) {
    logger.error(`WiTV detail scrape error for ${detailUrl}: ${error.message}`);
    return { image: '', streamingUrls: [] };
  }
}

module.exports = { scrapeWitvDetail };