
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for NetStream_graphql/src/graphql</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> NetStream_graphql/src/graphql</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">3.72% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>32/858</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0.21% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/476</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0.86% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>2/231</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">3.95% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>32/809</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="dataLoaders.js"><a href="dataLoaders.js.html">dataLoaders.js</a></td>
	<td data-value="10.24" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.24" class="pct low">10.24%</td>
	<td data-value="166" class="abs low">17/166</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="52" class="abs low">0/52</td>
	<td data-value="3.5" class="pct low">3.5%</td>
	<td data-value="57" class="abs low">2/57</td>
	<td data-value="11.64" class="pct low">11.64%</td>
	<td data-value="146" class="abs low">17/146</td>
	</tr>

<tr>
	<td class="file low" data-value="fastifyCompatibleResolvers.js"><a href="fastifyCompatibleResolvers.js.html">fastifyCompatibleResolvers.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="347" class="abs low">0/347</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="230" class="abs low">0/230</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="67" class="abs low">0/67</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="328" class="abs low">0/328</td>
	</tr>

<tr>
	<td class="file low" data-value="fastifyResolvers.js"><a href="fastifyResolvers.js.html">fastifyResolvers.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="206" class="abs low">0/206</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="136" class="abs low">0/136</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="53" class="abs low">0/53</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="205" class="abs low">0/205</td>
	</tr>

<tr>
	<td class="file low" data-value="fastifySchema.js"><a href="fastifySchema.js.html">fastifySchema.js</a></td>
	<td data-value="34.28" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 34%"></div><div class="cover-empty" style="width: 66%"></div></div>
	</td>
	<td data-value="34.28" class="pct low">34.28%</td>
	<td data-value="35" class="abs low">12/35</td>
	<td data-value="5.55" class="pct low">5.55%</td>
	<td data-value="18" class="abs low">1/18</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="34.28" class="pct low">34.28%</td>
	<td data-value="35" class="abs low">12/35</td>
	</tr>

<tr>
	<td class="file low" data-value="minimalResolvers.js"><a href="minimalResolvers.js.html">minimalResolvers.js</a></td>
	<td data-value="2.88" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 2%"></div><div class="cover-empty" style="width: 98%"></div></div>
	</td>
	<td data-value="2.88" class="pct low">2.88%</td>
	<td data-value="104" class="abs low">3/104</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="40" class="abs low">0/40</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="45" class="abs low">0/45</td>
	<td data-value="3.15" class="pct low">3.15%</td>
	<td data-value="95" class="abs low">3/95</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T16:31:40.526Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    