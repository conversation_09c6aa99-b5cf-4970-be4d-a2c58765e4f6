'use client'
import { useRecentlyWatched } from '@/hooks/useRecentlyWatched'
import Carousel from '@/components/ui/Carousel'

export default function RecentlyWatchedCarousels() {
  const { recentlyWatched, groupedRecentlyWatched } = useRecentlyWatched()

  if (!recentlyWatched || recentlyWatched.length === 0) {
    return null
  }

  return (
    <div className="space-y-8 mb-12">
      <h2 className="text-2xl font-bold text-white">Continue Watching</h2>
      
      {groupedRecentlyWatched.movies && groupedRecentlyWatched.movies.length > 0 && (
        <Carousel
          title="Recently Watched Movies"
          items={groupedRecentlyWatched.movies}
          type="movies"
        />
      )}
      
      {groupedRecentlyWatched.series && groupedRecentlyWatched.series.length > 0 && (
        <Carousel
          title="Recently Watched Series"
          items={groupedRecentlyWatched.series}
          type="series"
        />
      )}
      
      {groupedRecentlyWatched.anime && groupedRecentlyWatched.anime.length > 0 && (
        <Carousel
          title="Recently Watched Anime"
          items={groupedRecentlyWatched.anime}
          type="anime"
        />
      )}
    </div>
  )
}
