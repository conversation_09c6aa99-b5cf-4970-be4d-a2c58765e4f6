// File: scripts/monitor_db_changes.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');
const Movie = require('../src/db/models/Movie');

async function monitorDbChanges() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Get initial counts
    const initialSeriesCount = await Series.countDocuments();
    const initialAnimeCount = await Anime.countDocuments();
    const initialMovieCount = await Movie.countDocuments();
    
    const initialSeriesWithSeasonsCount = await Series.countDocuments({
      tmdbSeasons: { $exists: true, $ne: [] }
    });
    
    const initialAnimeWithSeasonsCount = await Anime.countDocuments({
      $or: [
        { tmdbSeasons: { $exists: true, $ne: [] } },
        { jikanSeasons: { $exists: true, $ne: [] } }
      ]
    });
    
    logger.info(`Initial counts:`);
    logger.info(`Series: ${initialSeriesCount} (${initialSeriesWithSeasonsCount} with seasons)`);
    logger.info(`Anime: ${initialAnimeCount} (${initialAnimeWithSeasonsCount} with seasons)`);
    logger.info(`Movies: ${initialMovieCount}`);
    
    // Monitor changes every 5 seconds
    const interval = setInterval(async () => {
      try {
        const currentSeriesCount = await Series.countDocuments();
        const currentAnimeCount = await Anime.countDocuments();
        const currentMovieCount = await Movie.countDocuments();
        
        const currentSeriesWithSeasonsCount = await Series.countDocuments({
          tmdbSeasons: { $exists: true, $ne: [] }
        });
        
        const currentAnimeWithSeasonsCount = await Anime.countDocuments({
          $or: [
            { tmdbSeasons: { $exists: true, $ne: [] } },
            { jikanSeasons: { $exists: true, $ne: [] } }
          ]
        });
        
        // Check for changes
        if (currentSeriesCount !== initialSeriesCount ||
            currentAnimeCount !== initialAnimeCount ||
            currentMovieCount !== initialMovieCount ||
            currentSeriesWithSeasonsCount !== initialSeriesWithSeasonsCount ||
            currentAnimeWithSeasonsCount !== initialAnimeWithSeasonsCount) {
          
          logger.info(`Database changes detected:`);
          logger.info(`Series: ${initialSeriesCount} -> ${currentSeriesCount}`);
          logger.info(`Series with seasons: ${initialSeriesWithSeasonsCount} -> ${currentSeriesWithSeasonsCount}`);
          logger.info(`Anime: ${initialAnimeCount} -> ${currentAnimeCount}`);
          logger.info(`Anime with seasons: ${initialAnimeWithSeasonsCount} -> ${currentAnimeWithSeasonsCount}`);
          logger.info(`Movies: ${initialMovieCount} -> ${currentMovieCount}`);
          
          // Check for new series with seasons
          if (currentSeriesWithSeasonsCount > initialSeriesWithSeasonsCount) {
            const newSeriesWithSeasons = await Series.find({
              tmdbSeasons: { $exists: true, $ne: [] }
            }).sort({ _id: -1 }).limit(5);
            
            logger.info(`Recent series with seasons:`);
            for (const series of newSeriesWithSeasons) {
              logger.info(`- ${series.title}: ${series.tmdbSeasons.length} seasons`);
            }
          }
          
          // Check for new anime with seasons
          if (currentAnimeWithSeasonsCount > initialAnimeWithSeasonsCount) {
            const newAnimeWithSeasons = await Anime.find({
              $or: [
                { tmdbSeasons: { $exists: true, $ne: [] } },
                { jikanSeasons: { $exists: true, $ne: [] } }
              ]
            }).sort({ _id: -1 }).limit(5);
            
            logger.info(`Recent anime with seasons:`);
            for (const anime of newAnimeWithSeasons) {
              logger.info(`- ${anime.title}: ${anime.tmdbSeasons?.length || 0} TMDB seasons, ${anime.jikanSeasons?.length || 0} Jikan seasons`);
            }
          }
        }
      } catch (error) {
        logger.error(`Error monitoring database: ${error.message}`);
      }
    }, 5000);
    
    // Handle process termination
    process.on('SIGINT', async () => {
      clearInterval(interval);
      await mongoose.disconnect();
      logger.info('Disconnected from MongoDB');
      process.exit(0);
    });
  } catch (error) {
    logger.error(`Error monitoring database: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

monitorDbChanges();
