# 1. Use an official Node.js LTS slim image
FROM node:18-slim

# 2. Install necessary dependencies for Chromium and Node apps
# Using a more comprehensive list found to work well in container environments
RUN apt-get update && apt-get install -yq --no-install-recommends \
    # --- System Chromium. ---
    chromium \
    # --- Dependencies for Chromium ---
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm-dev \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxshmfence-dev \
    libxtst6 \
    ca-certificates \ 
    fonts-liberation \ 
    lsb-release \
    wget \
    xdg-utils \
    # --- End Dependencies ---
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 3. Set the working directory
WORKDIR /usr/src/app

# 3.5. Accept build arguments and set as env
ARG MONGO_URI
ARG REDIS_HOST
ARG REDIS_PORT
ARG REDIS_PASSWORD
ARG REDIS_URL
ARG REDIS_SERVICE_URL
ENV MONGO_URI=$MONGO_URI
ENV REDIS_HOST=$REDIS_HOST
ENV REDIS_PORT=$REDIS_PORT
ENV REDIS_PASSWORD=$REDIS_PASSWORD
ENV REDIS_URL=$REDIS_URL
ENV REDIS_SERVICE_URL=$REDIS_SERVICE_URL

# 4. Copy package files (leverages Docker cache)
COPY package.json package-lock.json* ./

# 5. Install dependencies for backend
RUN npm ci

# 6. Copy the rest of the backend code
COPY . .

# 7. Set environment variables
ENV NODE_ENV=production
ENV FASTIFY_PORT=3001

# 8. Expose Fastify port
EXPOSE 3001

# 9. Start Fastify server
CMD ["node", "server-fastify.js"]

# --- Environment Variables Documentation ---
# Required:
# - MONGO_URI: MongoDB connection string
# - FASTIFY_PORT: Port for Fastify server (default: 3001)
#
# Optional (Redis):
# - REDIS_HOST: Redis host (default: localhost in dev, REDIS_URL in prod)
# - REDIS_PORT: Redis port (default: 6379)
# - REDIS_PASSWORD: Redis password
# - REDIS_URL: Full Redis URL (alternative to host/port/password)
# - REDIS_SERVICE_URL: Service-specific Redis URL
#
# --- Important Reminder ---
# Your Node.js code STILL needs the correct launch arguments for container environments:
# e.g., puppeteer.launch({
#   executablePath: process.env.PUPPETEER_EXECUTABLE_PATH, // Redundant if env var is set, but explicit
#   headless: true, // Or 'new'
#   args: [
#     '--no-sandbox',
#     '--disable-setuid-sandbox',
#     '--disable-dev-shm-usage',
#     '--disable-gpu',
#     '--no-zygote'
#   ]
# });