import { NextResponse } from 'next/server';

// Extract the base URL from API_URL (server-side) or NEXT_PUBLIC_API_URL (client-side) or use default
const BACKEND_URL = (process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://netstream-backend-container:3001/graphql').replace('/graphql', '');

export async function POST() {
  try {
    console.log('Stop scraping endpoint called');
    console.log('Backend URL:', BACKEND_URL);

    // Call the backend stop endpoint directly
    try {
      const backendResponse = await fetch(`${BACKEND_URL}/api/scrape/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const backendResult = await backendResponse.json();
      console.log('Backend response:', backendResult);

      return NextResponse.json(backendResult);

    } catch (backendError) {
      console.error('Backend stop error:', backendError.message);
      return NextResponse.json({
        success: false,
        error: `Failed to communicate with backend: ${backendError.message}`
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Stop scraping error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to stop scraping: ${error.message}`
    }, { status: 500 });
  }
}