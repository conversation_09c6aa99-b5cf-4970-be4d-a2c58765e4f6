// src/utils/browserUtils.js - Puppeteer Helper for Prototype
const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const logger = require('./logger');
const { normalizeWiflixUrl } = require('./urlNormalizer');
// Use a plausible User-Agent
const USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36';

// Apply stealth plugin
puppeteer.use(StealthPlugin());

// Increase protocol timeout to prevent Network.enable timeouts
const PUPPETEER_LAUNCH_OPTIONS = {
    headless: true,
    args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-features=IsolateOrigins,site-per-process',
        '--disable-web-security',
        '--disable-gpu', // Enable for stability
        '--disable-extensions',
        '--disable-setuid-sandbox',
        '--no-first-run',
        '--no-zygote',
        '--single-process', // This might help with stability
        '--disable-accelerated-2d-canvas',
        '--disable-accelerated-video-decode',
        '--disable-background-networking',
        '--disable-background-timer-throttling',
        '--disable-breakpad',
        '--disable-client-side-phishing-detection',
        '--disable-default-apps',
        '--disable-dev-shm-usage',
        '--disable-domain-reliability',
        '--disable-extensions',
        '--disable-features=AudioServiceOutOfProcess,IsolateOrigins,site-per-process',
        '--disable-hang-monitor',
        '--disable-ipc-flooding-protection',
        '--disable-notifications',
        '--disable-offer-store-unmasked-wallet-cards',
        '--disable-popup-blocking',
        '--disable-print-preview',
        '--disable-prompt-on-repost',
        '--disable-renderer-backgrounding',
        '--disable-speech-api',
        '--disable-sync',
        '--disable-translate',
        '--disable-webgl',
        '--hide-scrollbars',
        '--ignore-certificate-errors',
        '--metrics-recording-only',
        '--mute-audio',
        '--no-default-browser-check',
        '--no-experiments',
        '--no-pings',
        '--password-store=basic',
        '--use-gl=swiftshader',
        '--use-mock-keychain',
    ],
    // Add protocol timeout (default is 30000ms)
    protocolTimeout: 180000, // Increase to 180 seconds
    // Add browser timeout
    timeout: 300000, // 5 minutes for browser launch
};

let browserInstance = null;
let activePages = 0;
// Concurrency management with environment variable support
const MAX_PAGES = process.env.MAX_CONCURRENT_PAGES ? parseInt(process.env.MAX_CONCURRENT_PAGES) : 1; // Reduced to 1
const pageQueue = [];
const MAX_RETRY_ATTEMPTS = process.env.MAX_RETRY_ATTEMPTS ? parseInt(process.env.MAX_RETRY_ATTEMPTS) : 5; // Increased to 5
const RETRY_DELAY_BASE = process.env.RETRY_DELAY_BASE ? parseInt(process.env.RETRY_DELAY_BASE) : 5000; // 5 seconds base delay
const QUEUE_PROCESSING_INTERVAL = 1000; // Increased to 1 second

// Log the concurrency settings
logger.info(`[BrowserUtils] Initialized with MAX_PAGES=${MAX_PAGES}, MAX_RETRY_ATTEMPTS=${MAX_RETRY_ATTEMPTS}, RETRY_DELAY_BASE=${RETRY_DELAY_BASE}ms`);

async function getBrowser() {
    if (!browserInstance || !browserInstance.isConnected()) {
        logger.info('[BrowserUtils] Launching new Puppeteer browser instance...');
         try {
            browserInstance = await puppeteer.launch(PUPPETEER_LAUNCH_OPTIONS);
            browserInstance.on('disconnected', () => {
                logger.warn('[BrowserUtils] Puppeteer browser instance disconnected.');
                browserInstance = null;
                activePages = 0;
                // Clear the queue when browser disconnects
                while (pageQueue.length > 0) {
                    const { reject } = pageQueue.shift();
                    reject(new Error('Browser disconnected while in queue'));
                }
            });
            activePages = 0;
            logger.info('[BrowserUtils] New browser instance launched.');
         } catch (launchError) {
              logger.error(`[BrowserUtils] Failed to launch browser: ${launchError.message}`, { stack: launchError.stack });
              throw launchError;
         }
    }
    return browserInstance;
}

// Process the page queue with improved handling
async function processQueue() {
    if (pageQueue.length === 0) return;

    // If MAX_PAGES is 0, process all items without concurrency limits
    if (MAX_PAGES === 0) {
        const queueLength = pageQueue.length;
        while (pageQueue.length > 0) {
            const { resolve } = pageQueue.shift();
            resolve(); // Signal that a slot is available
        }
        logger.debug(`[BrowserUtils] Processed all ${queueLength} items in queue (no concurrency limit).`);
        return;
    }

    // Process as many items as possible up to MAX_PAGES
    while (pageQueue.length > 0 && activePages < MAX_PAGES) {
        const { resolve } = pageQueue.shift();
        resolve(); // Signal that a slot is available

        // Log queue status
        if (pageQueue.length > 0) {
            logger.debug(`[BrowserUtils] Queue processed 1 item. ${pageQueue.length} items remaining in queue.`);
        } else {
            logger.debug(`[BrowserUtils] Queue empty. All pending requests are being processed.`);
        }
    }

    // If there are still items in the queue, schedule another processing
    if (pageQueue.length > 0) {
        setTimeout(processQueue, QUEUE_PROCESSING_INTERVAL);
    }
}

/**
 * Fetches HTML content of a URL using Puppeteer with stealth.
 * Manages a shared browser instance and limits concurrent pages.
 * @param {string} url The URL to fetch.
 * @param {number} [timeoutMs=180000] Timeout in milliseconds.
 * @param {number} [retryAttempt=0] Current retry attempt number.
 * @returns {Promise<string|null>} HTML content as string, or null on failure.
 */
async function fetchPageWithPuppeteer(url, timeoutMs = 180000, retryAttempt = 0) {
    // Check if URL is from flemmix.net
    const isFlemmix = url.includes('flemmix.net');

    // Use a different approach for flemmix.net
    if (isFlemmix) {
        return await fetchFlemmixPage(url, timeoutMs, retryAttempt);
    }
    let page = null;
    let browser = null;

    // Clean up URL - remove trailing dots/ellipses
    const cleanUrl = url.replace(/\.{3,}$/g, '').replace(/\s+/g, '');

    // Normalize the URL to use the current WIFLIX_BASE domain
    const normalizedUrl = await normalizeWiflixUrl(cleanUrl);

    logger.info(`[BrowserUtils] Fetching with Puppeteer+Stealth: ${normalizedUrl.substring(0,100)}${retryAttempt > 0 ? ` (retry ${retryAttempt}/${MAX_RETRY_ATTEMPTS})` : ''}`);

    // Enhanced concurrency management with queue (only if MAX_PAGES > 0)
    if (MAX_PAGES > 0 && activePages >= MAX_PAGES) {
        if (retryAttempt >= MAX_RETRY_ATTEMPTS) {
            logger.error(`[BrowserUtils] Max retry attempts (${MAX_RETRY_ATTEMPTS}) reached for ${cleanUrl}`);
            throw new Error(`Puppeteer concurrency limit (${MAX_PAGES}) reached after ${MAX_RETRY_ATTEMPTS} attempts.`);
        }

        const retryDelay = RETRY_DELAY_BASE * (retryAttempt + 1);
        logger.warn(`[BrowserUtils] Max concurrent pages (${MAX_PAGES}) reached. Attempt ${retryAttempt + 1}/${MAX_RETRY_ATTEMPTS}. Waiting ${retryDelay/1000}s...`);

        // Add to queue with exponential backoff
        await new Promise((resolve, reject) => {
            pageQueue.push({ resolve, reject, url: normalizedUrl });
            setTimeout(() => {
                // If we're still in the queue after timeout, remove and reject
                const index = pageQueue.findIndex(item => item.resolve === resolve);
                if (index !== -1) {
                    pageQueue.splice(index, 1);
                    // Try again with increased retry count
                    resolve();
                }
            }, retryDelay);
        });

        // Recursive retry with incremented attempt count
        return fetchPageWithPuppeteer(cleanUrl, timeoutMs, retryAttempt + 1);
    }

    try {
        browser = await getBrowser().catch(err => {
            if (err.message.includes('Protocol timeout')) {
                logger.error(`[BrowserUtils] Protocol timeout when launching browser. Retrying with increased timeout...`);
                // Force browser instance to null to trigger a new launch
                browserInstance = null;
                return getBrowser();
            }
            throw err;
        });
        activePages++;
        logger.debug(`[BrowserUtils] Opening new page (${activePages}${MAX_PAGES > 0 ? `/${MAX_PAGES}` : ' - no limit'}). Target: ${cleanUrl.substring(0,100)}`);
        page = await browser.newPage();

        await page.setUserAgent(USER_AGENT);
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9,fr-FR;q=0.8,fr;q=0.7'
        });

        // Set a shorter client-side timeout to detect non-responsive pages faster
        page.setDefaultNavigationTimeout(timeoutMs);
        page.setDefaultTimeout(timeoutMs);

        // Block unnecessary resources to save bandwidth and speed up loading
        await page.setRequestInterception(true);
        page.on('request', (req) => {
           const resourceType = req.resourceType();
           if(['image', 'stylesheet', 'font', 'media', 'other'].includes(resourceType)){
              req.abort().catch(err => logger.warn(`[BrowserUtils] Failed to abort ${resourceType} request: ${err.message.substring(0,100)}`));
           } else {
              req.continue().catch(err => logger.warn(`[BrowserUtils] Failed to continue ${resourceType} request: ${err.message.substring(0,100)}`));
           }
        });

        // Create a timeout promise that will reject after the specified timeout
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Navigation timeout of ${timeoutMs}ms exceeded for ${cleanUrl}`));
            }, timeoutMs);
        });

        // Race the navigation against the timeout
        const navigationPromise = page.goto(normalizedUrl, {
            waitUntil: 'networkidle2',
            timeout: timeoutMs
        });

        // Use Promise.race to implement a more reliable timeout
        const response = await Promise.race([
            navigationPromise,
            timeoutPromise
        ]);

        // Check for 404 or other error status codes
        const status = response.status();
        logger.debug(`[BrowserUtils] Navigation to ${normalizedUrl.substring(0,100)} completed with status: ${status}`);

        // Handle 404 errors specifically
        if (status === 404) {
            logger.warn(`[BrowserUtils] Page not found (404) for URL: ${normalizedUrl}`);
            throw new Error(`Page not found (404) for URL: ${normalizedUrl}`);
        }

        // Handle other non-OK statuses
        if (!response.ok() && status !== 304) {
            let pageText = '';
            try {
                pageText = await page.evaluate(() => document.body.textContent.substring(0, 200));
            } catch {}

            throw new Error(`[BrowserUtils] Received non-OK status ${status} for ${normalizedUrl}. Hint: ${pageText}`);
        }

        // Check if page contains "404 Not Found" text (some sites return 200 but show 404 content)
        const pageContent = await page.content();
        if (pageContent.includes('404 Not Found') ||
            pageContent.includes('Page not found') ||
            pageContent.includes('LiteSpeed Web Server')) {
            logger.warn(`[BrowserUtils] Page content indicates 404 for URL: ${normalizedUrl}`);
            throw new Error(`Page content indicates 404 for URL: ${normalizedUrl}`);
        }

        // Replace waitForTimeout with a compatible alternative
        // Use setTimeout with a promise instead
        await new Promise(resolve => setTimeout(resolve, 500));

        // Get the HTML content
        const html = await page.content();
        return html;
    } catch (error) {
        // Handle timeout errors specifically
        if (error.message.includes('timeout') || error.message.includes('Timeout')) {
            logger.error(`[BrowserUtils] Navigation timeout for ${normalizedUrl}: ${error.message}`);

            // Try to get any content that might have loaded before timeout
            if (page) {
                try {
                    const partialHtml = await page.content();
                    if (partialHtml && partialHtml.length > 100) {
                        logger.info(`[BrowserUtils] Returning partial content for ${normalizedUrl} despite timeout`);
                        return partialHtml;
                    }
                } catch (contentError) {
                    logger.error(`[BrowserUtils] Failed to get partial content: ${contentError.message}`);
                }
            }
        }

        // Handle protocol errors specifically
        if (error.message.includes('Protocol timeout') || error.name === 'ProtocolError') {
            logger.error(`[BrowserUtils] Protocol error for ${normalizedUrl}: ${error.message}`);
            // Force browser instance to null to trigger a new launch on next attempt
            browserInstance = null;
        }

        logger.error(`[BrowserUtils] Puppeteer fetch failed for ${normalizedUrl}: ${error.message}`);
        throw error;
    } finally {
        if (page) {
            try {
                await page.close();
                logger.debug(`[BrowserUtils] Page closed for ${normalizedUrl.substring(0,100)}`);
            } catch (closeError) {
                logger.warn(`[BrowserUtils] Error closing page: ${closeError.message}`);
            }
            activePages--;
            // Process the next item in queue if any
            processQueue();
        }
    }
}

// Function to gracefully close the browser (call this on script shutdown)
async function closeBrowser() {
     if (browserInstance && browserInstance.isConnected()) {
        logger.info('[BrowserUtils] Closing shared Puppeteer browser instance...');
        try {
            await browserInstance.close();
            logger.info('[BrowserUtils] Shared browser instance closed successfully.');
        } catch (closeError) {
            logger.error(`[BrowserUtils] Error closing browser: ${closeError.message}`);
        } finally {
             browserInstance = null;
             activePages = 0;
        }
     } else {
          logger.info('[BrowserUtils] No active browser instance to close.');
     }
}

/**
 * Special function to fetch content from flemmix.net
 * Uses a more robust approach to handle anti-scraping measures
 * @param {string} url The URL to fetch
 * @param {number} timeoutMs Timeout in milliseconds
 * @param {number} retryAttempt Current retry attempt
 * @returns {Promise<string|null>} HTML content as string, or null on failure
 */
async function fetchFlemmixPage(url, timeoutMs = 180000, retryAttempt = 0) {
    const axios = require('axios');
    const https = require('https');

    logger.info(`[BrowserUtils] Fetching flemmix page with special handler: ${url}`);

    try {
        // First try with axios
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
                'Accept-Language': 'en-US,en;q=0.9,fr-FR;q=0.8,fr;q=0.7',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Sec-Ch-Ua': '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            },
            timeout: 30000,
            httpsAgent: new https.Agent({
                rejectUnauthorized: false
            })
        });

        if (response.status === 200 && response.data) {
            logger.info(`[BrowserUtils] Successfully fetched ${url} with axios`);

            // Check if the HTML contains the expected elements
            const html = response.data;
            if (typeof html === 'string' &&
                (html.includes('<div id="dle-content">') || html.includes('<div class="mov clearfix">'))) {
                return html;
            } else {
                logger.warn(`[BrowserUtils] Axios response doesn't contain expected elements for ${url}`);
                // Continue to Puppeteer approach
            }
        }
    } catch (axiosError) {
        logger.warn(`[BrowserUtils] Axios fetch failed for ${url}: ${axiosError.message}`);
        // Continue to Puppeteer approach
    }

    // If axios fails, try with Puppeteer
    let browser = null;
    let page = null;

    try {
        // Launch a new browser instance for each request to avoid detection
        browser = await puppeteer.launch({
            ...PUPPETEER_LAUNCH_OPTIONS,
            headless: true
        });

        page = await browser.newPage();

        // Set a realistic viewport
        await page.setViewport({
            width: 1366,
            height: 768
        });

        // Set user agent
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36');

        // Set extra HTTP headers
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9,fr-FR;q=0.8,fr;q=0.7'
        });

        // Block unnecessary resources to speed up loading
        await page.setRequestInterception(true);
        page.on('request', (req) => {
            const resourceType = req.resourceType();
            if(['image', 'stylesheet', 'font', 'media'].includes(resourceType)){
                req.abort();
            } else {
                req.continue();
            }
        });

        // Navigate to the URL with a timeout
        logger.info(`[BrowserUtils] Navigating to ${url} with Puppeteer`);
        await page.goto(url, {
            waitUntil: 'domcontentloaded',
            timeout: timeoutMs
        });

        // Wait for content to load
        await page.waitForSelector('body', { timeout: 30000 });

        // Get the HTML content
        const html = await page.content();

        if (html) {
            logger.info(`[BrowserUtils] Successfully fetched ${url} with Puppeteer`);
            return html;
        }

        return null;
    } catch (error) {
        logger.error(`[BrowserUtils] Puppeteer fetch failed for ${url}: ${error.message}`);

        // Try to get any content that might have loaded before timeout
        if (page) {
            try {
                const partialHtml = await page.content();
                if (partialHtml && partialHtml.length > 100) {
                    logger.info(`[BrowserUtils] Retrieved partial content for ${url}`);
                    return partialHtml;
                }
            } catch (contentError) {
                logger.error(`[BrowserUtils] Failed to get partial content: ${contentError.message}`);
            }
        }

        // Retry logic
        if (retryAttempt < MAX_RETRY_ATTEMPTS) {
            const nextRetryDelay = RETRY_DELAY_BASE * Math.pow(2, retryAttempt);
            logger.info(`[BrowserUtils] Retrying fetch for ${url} in ${nextRetryDelay}ms (attempt ${retryAttempt + 1}/${MAX_RETRY_ATTEMPTS})`);

            await new Promise(resolve => setTimeout(resolve, nextRetryDelay));
            return fetchFlemmixPage(url, timeoutMs, retryAttempt + 1);
        }

        return null;
    } finally {
        if (browser) {
            await browser.close().catch(err => logger.error(`[BrowserUtils] Error closing browser: ${err.message}`));
        }
    }
}

module.exports = { fetchPageWithPuppeteer, closeBrowser, getBrowser, fetchFlemmixPage };