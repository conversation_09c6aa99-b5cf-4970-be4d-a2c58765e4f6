{"timestamp":"2025-04-25T21:28:47.050Z","error":{"message":"Invalid or non-HTTP(S) URL provided: ttps://tipfly.xyz/em-466373-75l1vqdptwbs","name":"Error"},"context":{"phase":"url_extraction","documentId":"67de166a6825d721f00429a3","documentTitle":"The Voice : La Plus Belle Voix","originalUrl":"ttps://tipfly.xyz/em-466373-75l1vqdptwbs","streamUrlId":"67f4567b5e602f960ec16d16","episodeContext":{"episodeId":"67f4567b5e602f960ec16d13","episodeNumber":"25","season":"14"}}}
{"timestamp":"2025-04-25T21:34:31.256Z","error":{"message":"cursor id 2251173084993081828 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Series"}}
{"timestamp":"2025-04-25T22:08:27.229Z","error":{"message":"Invalid or non-HTTP(S) URL provided: ttps://tipfly.xyz/em-466373-75l1vqdptwbs","name":"Error"},"context":{"phase":"url_extraction","documentId":"67de166a6825d721f00429a3","documentTitle":"The Voice : La Plus Belle Voix","originalUrl":"ttps://tipfly.xyz/em-466373-75l1vqdptwbs","streamUrlId":"67f4567b5e602f960ec16d16","episodeContext":{"episodeId":"67f4567b5e602f960ec16d13","episodeNumber":"25","season":"14"}}}
{"timestamp":"2025-04-25T22:13:03.395Z","error":{"message":"cursor id 9180080877415365490 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Movie"}}
{"timestamp":"2025-04-25T22:14:12.249Z","error":{"message":"cursor id 7459213956240913548 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Series"}}
{"timestamp":"2025-04-26T16:14:45.757Z","error":{"message":"cursor id 7794636480499278279 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Series"}}
{"timestamp":"2025-04-26T16:20:27.273Z","error":{"message":"cursor id 1392501945178626796 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Movie"}}
{"timestamp":"2025-04-26T16:41:14.067Z","error":{"message":"cursor id 713070722195182966 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Movie"}}
{"timestamp":"2025-04-26T16:42:05.290Z","error":{"message":"cursor id 3883174274254126375 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Series"}}
{"timestamp":"2025-04-29T13:25:07.220Z","error":{"message":"request to https://tipfly.xyz/em-352961-6it6zdbzzkx7 failed, reason: socket hang up","name":"FetchError"},"context":{"phase":"url_extraction","documentId":"67df76c8b09d0d40a153fc3b","documentTitle":"Le Dog Show","originalUrl":"https://tipfly.xyz/em-352961-6it6zdbzzkx7","streamUrlId":"67df76c8b09d0d40a153fc3e","episodeContext":{}}}
{"timestamp":"2025-04-29T13:37:37.030Z","error":{"message":"cursor id 247280764352360574 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Movie"}}
{"timestamp":"2025-04-29T13:37:53.701Z","error":{"message":"cursor id 3805186029818069261 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Movie"}}
{"timestamp":"2025-04-29T14:00:22.997Z","error":{"message":"cursor id 5025356991596292619 not found","name":"MongoServerError"},"context":{"phase":"cursor_iteration","collection":"Series"}}
