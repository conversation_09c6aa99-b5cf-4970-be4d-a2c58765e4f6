# File: schema.graphql

type Query {
    item(id: ID!, type: ItemType!): Item
    search(query: String!, page: Int = 1, limit: Int = 20): SearchResult
    movies(sort: SortOption, page: Int = 1, limit: Int = 20): [Movie!]!
    series(sort: SortOption, page: Int = 1, limit: Int = 20): [Series!]!
    anime(sort: SortOption, page: Int = 1, limit: Int = 20): [Anime!]!
    liveTV(page: Int = 1, limit: Int = 20): [LiveTV!]!
    animes(filter: AnimeFilter, sort: SortOption, page: Int = 1, limit: Int = 20): [Anime!]!
    stream(itemId: ID!, type: ItemType!, streamId: ID!): StreamURL
    play(type: ItemType!, id: ID!, ep: String, lang: Language): PlayURL
    config: Config
    duplicateDetailUrlPaths: [DuplicateDetailUrlPath!]!
    # Admin query to validate token
    validateAdminToken(token: String!): AdminValidationResult!
    # New queries for movie carousels
    latestMovies(excludeAncien: Boolean = true, page: Int = 1, limit: Int = 20): [Movie!]!
    ancienMovies(page: Int = 1, limit: Int = 20): [Movie!]!
    # New queries for series and anime carousels
    latestSeries(page: Int = 1, limit: Int = 20): [Series!]!
    latestAnime(page: Int = 1, limit: Int = 20): [Anime!]!
    # New query for anime movies from films-vf-vostfr
    animeMovies(page: Int = 1, limit: Int = 20): [Anime!]!
    # Genre-based queries
    moviesByGenre(genre: String!, page: Int = 1, limit: Int = 20): [Movie!]!
    seriesByGenre(genre: String!, page: Int = 1, limit: Int = 20): [Series!]!
    animeByGenre(genre: String!, page: Int = 1, limit: Int = 20): [Anime!]!
    # Get available genres
    availableGenres: GenreList!
    # Get related seasons by TMDB ID
    relatedSeasons(tmdbId: Int!, currentItemId: ID!): [Item!]!
    # Admin queries
    databaseStats: DatabaseStats!
    contentOverview: ContentOverview!
    displaySettings(adminToken: String!): DisplaySettings!
}

type Mutation {
    # Admin login mutation
    adminLogin(adminKey: String!): AdminLoginResult!
    # Delete item mutation (requires admin token)
    deleteItem(id: ID!, type: ItemType!, adminToken: String!): DeleteItemResult!
    # Update item mutation (requires admin token)
    updateItem(id: ID!, type: ItemType!, input: ItemInput!, adminToken: String!): UpdateItemResult!
    # Scrape specific item mutation (requires admin token)
    scrapeItem(id: ID!, type: ItemType!, adminToken: String!): ScrapeItemResult!
    # Update base URL configuration (requires admin token)
    updateBaseUrl(key: String!, value: String!, adminToken: String!): UpdateConfigResult!
    # Update API key configuration (requires admin token)
    updateApiKey(key: String!, value: String!, adminToken: String!): UpdateConfigResult!
    # Manually scrape a URL (requires admin token)
    scrapeUrlManually(url: String!, type: ItemType!, adminToken: String!): ScrapeUrlResult!
    # Update admin display settings (requires admin token)
    updateDisplaySettings(gridItemsEnabled: Boolean!, adminToken: String!): UpdateConfigResult!
}

enum ItemType {
    MOVIE
    SERIES
    ANIME
    LIVETV
}

enum SortOption {
    LATEST
    TRENDING
    ALPHA
    RELEASE
}

input AnimeFilter {
    search: String
    genre: [String!] # Added genre filter as an array of strings
    type: String # TV, Movie etc.
    year: Int
    score: Float
    status: String # Airing, Finished Airing etc.
}

enum Language {
    VF
    VOSTFR
    unknown # Ensure 'unknown' is explicitly listed if used
}

type Config {
    tmdbApiKey: String
    geminiApiKey: String
    wiflixBase: String!
    frenchAnimeBase: String!
    witvBase: String!
}

type SearchResult {
    items: [Item!]!
}

type GenreList {
    movies: [String!]!
    series: [String!]!
    anime: [String!]!
}

type Metadata {
    synopsis: String
    actors: [String!]
    year: String
    genre: String
    origin: String
    creator: String
    duration: String
}

type TMDB {
    id: Int
    title: String        # Used for both movies and TV series titles
    original_title: String
    overview: String
    release_date: String # Used for both movie release dates and TV first air dates
    poster_path: String
    backdrop_path: String
    vote_average: Float
    vote_count: Int
    genres: [String!]
    cast: [TMDBCastMember]
    crew: [TMDBCrewMember]
    # TV Series specific fields
    number_of_seasons: Int
    number_of_episodes: Int
    in_production: Boolean
    status: String
}

type TMDBCastMember {
    id: Int
    name: String
    character: String
}

type TMDBCrewMember {
    id: Int
    name: String
    job: String
}

type TmdbSeason {
    air_date: String
    tmdb_season_id: Int
    name: String
    overview: String
    poster_path: String
    season_number: Int
    vote_average: Float
    episodes: [TmdbEpisode]
}

type TmdbEpisode {
    air_date: String
    episode_number: Int
    tmdb_episode_id: Int
    name: String
    overview: String
    still_path: String
    vote_average: Float
}


type Jikan {
    mal_id: Int
    title: JikanTitle
    type: String
    source: String
    episodes: Int
    status: String
    airing: Boolean
    aired: JikanAired
    duration: String
    rating: String
    score: Float
    scored_by: Int
    rank: Int
    popularity: Int
    members: Int
    favorites: Int
    synopsis: String
    background: String
    season: String
    year: Int
    studios: [JikanStudio!]
    genres: [JikanGenre!]
    themes: [JikanTheme!]
    demographics: [JikanDemographic!]
    images: JikanImages
    trailer: JikanTrailer
    approved: Boolean
    relations: [JikanRelation!]
    streaming_platforms: [JikanStreamingPlatform!]
    lastUpdated: String # Changed to String as resolver uses toISOString()
}

type JikanTitle {
    default: String
    english: String
    japanese: String
    synonyms: [String!]
}

type JikanAired {
    from: String # Changed to String
    to: String   # Changed to String
    string: String
}

type JikanStudio {
    mal_id: Int
    name: String
}

type JikanGenre {
    mal_id: Int
    name: String
    type: String
}

type JikanTheme {
    mal_id: Int
    name: String
    type: String
}

type JikanDemographic {
    mal_id: Int
    name: String
    type: String
}

type JikanImages {
    jpg: JikanImageSet
    webp: JikanImageSet
}

type JikanImageSet {
    image_url: String
    small_image_url: String
    large_image_url: String
}

type JikanTrailer {
    youtube_id: String
    url: String
    embed_url: String
}

type JikanRelation {
    relation: String
    entry: [JikanRelationEntry!]
}

type JikanRelationEntry {
    mal_id: Int
    type: String
    name: String
    url: String
}

type JikanStreamingPlatform {
    name: String
    url: String
}

type JikanSeason {
    mal_id: Int
    title: String
    season_number: Int
    episodes: Int
    score: Float
    year: Int
    synopsis: String
    aired: JikanAired
}

interface Item {
    id: ID!
    title: String!
    displayTitle: String # <<< ADDED displayTitle
    detailUrl: String!
    detailUrlPath: String
    image: String
    thumbnail: String    # <<< ADDED thumbnail
    metadata: Metadata
    tmdb: TMDB
    # Jikan only exists on Anime, so not in the interface
}

type Movie implements Item {
    id: ID!
    title: String!
    displayTitle: String # Implement from Item
    detailUrl: String!
    detailUrlPath: String
    cleanedTitle: String
    image: String
    thumbnail: String # Implement from Item
    streamingUrls: [StreamingUrl!]!
    metadata: Metadata
    tmdb: TMDB
    trendingRank: Int # Rank from trendingitems collection (0-based)
    trendingRankDisplay: String # Display text: "★" for rank 0, "1", "2", "3" for others
    trendingRankIcon: TrendingRankIcon # Icon information for trending rank display
}

type Series implements Item {
    id: ID!
    title: String!
    displayTitle: String # Implement from Item
    detailUrl: String!
    detailUrlPath: String
    cleanedTitle: String
    season: String
    image: String
    thumbnail: String # Implement from Item
    episodes: [Episode!]!
    metadata: Metadata
    tmdb: TMDB
    tmdbSeason: TmdbSeason # Legacy single season
    tmdbSeasons: [TmdbSeason!] # Array of all seasons
    trendingRank: Int # Rank from trendingitems collection (0-based)
    trendingRankDisplay: String # Display text: "★" for rank 0, "1", "2", "3" for others
    trendingRankIcon: TrendingRankIcon # Icon information for trending rank display
}

type Anime implements Item {
    id: ID!
    title: String!
    displayTitle: String # Implement from Item
    detailUrl: String!
    detailUrlPath: String
    image: String
    thumbnail: String # Implement from Item
    season: String
    animeLanguage: Language
    episodes: [Episode!]!
    streamingUrls: [StreamingUrl!]! # For films/OVAs
    metadata: Metadata
    tmdb: TMDB
    tmdbSeason: TmdbSeason # Legacy single season
    tmdbSeasons: [TmdbSeason!] # Array of all seasons
    jikan: Jikan # Specific to Anime
    jikanSeasons: [JikanSeason!] # Array of all Jikan seasons
    trendingRank: Int # Rank from trendingitems collection (0-based)
    trendingRankDisplay: String # Display text: "★" for rank 0, "1", "2", "3" for others
    trendingRankIcon: TrendingRankIcon # Icon information for trending rank display
}

type LiveTV implements Item {
    id: ID!
    title: String!
    displayTitle: String # Implement from Item
    detailUrl: String!
    detailUrlPath: String
    cleanedTitle: String
    image: String
    thumbnail: String # Implement from Item
    streamingUrls: [StreamingUrl!]!
    metadata: Metadata # LiveTV might not have much metadata
    tmdb: TMDB # LiveTV might not have TMDB data
}

type Episode {
    episodeNumber: String!
    season: String
    language: Language
    streamingUrls: [StreamingUrl!]!
}

type StreamingUrl {
    id: ID! # Ensure ID is defined
    url: String!
    provider: String
    language: Language
    lastChecked: String # Changed to String
    isActive: Boolean
    sourceStreamUrl: String
    size: String
    type: String # e.g., HLS, MP4
    method: String # e.g., GET, iframe
}

# For the stream query response
type StreamURL {
    sourceStreamUrl: String
    size: String
    type: String
    method: String
}

# For the play query response
type PlayURL {
    url: String!
}

# For the duplicateDetailUrlPaths query response
type DuplicateDetailUrlPath {
    detailUrlPath: String!
    items: [Item!]! # Returns items matching the interface
}

# Admin types
type AdminLoginResult {
    success: Boolean!
    token: String
    message: String
}

type AdminValidationResult {
    isValid: Boolean!
}

type DeleteItemResult {
    success: Boolean!
    message: String
    deletedId: ID
    type: ItemType
}

type ScrapeItemResult {
    success: Boolean!
    message: String
    itemId: ID
    type: ItemType
    logId: String
}

# For the scrapeUrlManually mutation response
type ScrapeUrlResult {
    success: Boolean!
    message: String
    url: String
    type: ItemType
    logId: String
}

# For the updateBaseUrl mutation response
type UpdateConfigResult {
    success: Boolean!
    message: String
    key: String
    value: String
}

# Admin types for dashboard
type DatabaseStats {
    movies: Int!
    series: Int!
    anime: Int!
    livetv: Int!
    totalItems: Int!
}

type ContentOverview {
    recentlyAdded: Int!
    trending: Int!
    mostWatched: Int!
    totalViews: Int!
}

type DisplaySettings {
    gridItemsEnabled: Boolean!
}

# Trending rank icon information for frontend display
type TrendingRankIcon {
    rank: Int! # The actual rank (0-based)
    displayRank: Int! # Display rank (1-based for UI)
    icon: String! # Icon type: "star" for rank 0, "number" for others
    size: String! # Size: "large" for top 3, "small" for others
    text: String! # Display text: "★" for rank 0, "1", "2", "3", etc.
    className: String! # CSS class for styling
}

input ItemInput {
    title: String
    displayTitle: String
    detailUrl: String
    detailUrlPath: String
    cleanedTitle: String
    thumbnail: String
    image: String
    season: String
    metadata: MetadataInput
    streamingUrls: [StreamingUrlInput!]
    episodes: [EpisodeInput!]
    tmdb: TMDBInput
    jikan: JikanInput
}

input MetadataInput {
    synopsis: String
    actors: [String!]
    year: String
    genre: String
    origin: String
    creator: String
    duration: String
}

input StreamingUrlInput {
    id: ID
    url: String!
    provider: String
    language: Language
    lastChecked: String
    isActive: Boolean
    sourceStreamUrl: String
    size: String
    type: String
    method: String
}

input EpisodeInput {
    episodeNumber: String!
    season: String
    language: Language
    streamingUrls: [StreamingUrlInput!]!
}

input TMDBInput {
    id: Int
    title: String
    original_title: String
    overview: String
    release_date: String
    poster_path: String
    backdrop_path: String
    vote_average: Float
    vote_count: Int
    genres: [String!]
    cast: [TMDBCastMemberInput!]
    crew: [TMDBCrewMemberInput!]
    number_of_seasons: Int
    number_of_episodes: Int
    in_production: Boolean
    status: String
}

input TMDBCastMemberInput {
    id: Int
    name: String
    character: String
}

input TMDBCrewMemberInput {
    id: Int
    name: String
    job: String
}

input JikanInput {
    mal_id: Int
    title: JikanTitleInput
    type: String
    source: String
    episodes: Int
    status: String
    airing: Boolean
    aired: JikanAiredInput
    duration: String
    rating: String
    score: Float
    scored_by: Int
    rank: Int
    popularity: Int
    members: Int
    favorites: Int
    synopsis: String
    background: String
    season: String
    year: Int
    studios: [JikanStudioInput!]
    genres: [JikanGenreInput!]
    themes: [JikanThemeInput!]
    demographics: [JikanDemographicInput!]
    streaming_platforms: [JikanStreamingPlatformInput!]
}

input JikanTitleInput {
    default: String
    english: String
    japanese: String
}

input JikanAiredInput {
    from: String
    to: String
    string: String
}

input JikanStudioInput {
    mal_id: Int
    name: String
}

input JikanGenreInput {
    mal_id: Int
    name: String
    type: String
}

input JikanThemeInput {
    mal_id: Int
    name: String
    type: String
}

input JikanDemographicInput {
    mal_id: Int
    name: String
    type: String
}

input JikanStreamingPlatformInput {
    name: String
    url: String
}

type UpdateItemResult {
    success: Boolean!
    message: String
    item: Item
}