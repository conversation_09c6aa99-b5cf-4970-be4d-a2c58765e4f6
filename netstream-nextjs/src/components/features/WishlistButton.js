'use client'
import { useState } from 'react'
import { useWishlist } from '@/hooks/useWishlist'

export default function WishlistButton({ item, type, className = '' }) {
  const { isInWishlist, addToWishlist, removeFromWishlist } = useWishlist()
  const [isLoading, setIsLoading] = useState(false)

  const inWishlist = isInWishlist(item.id, type)

  const handleClick = async (e) => {
    e.preventDefault()
    e.stopPropagation()
    
    setIsLoading(true)
    
    try {
      if (inWishlist) {
        await removeFromWishlist(item.id, type)
      } else {
        await addToWishlist({
          id: item.id,
          title: item.title || item.displayTitle,
          thumbnail: item.thumbnail || item.image,
          type: type,
          year: item.year || item.metadata?.year
        })
      }
    } catch (error) {
      console.error('Error updating wishlist:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={`w-8 h-8 rounded-full flex items-center justify-center transition-all ${
        inWishlist 
          ? 'bg-red-600 hover:bg-red-700 text-white' 
          : 'bg-gray-800 hover:bg-gray-700 text-gray-300'
      } ${className}`}
      title={inWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
    >
      {isLoading ? (
        <i className="fas fa-spinner fa-spin text-xs"></i>
      ) : (
        <i className={`fas ${inWishlist ? 'fa-heart' : 'fa-heart'} text-xs`}></i>
      )}
    </button>
  )
}
