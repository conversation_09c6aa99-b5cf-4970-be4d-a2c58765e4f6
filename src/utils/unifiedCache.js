/**
 * Unified High-Performance Caching System for NetStream
 * Replaces multiple separate LRU caches with a single, optimized solution
 */

const logger = require('./logger');

class UnifiedCache {
  constructor(options = {}) {
    this.maxSize = options.maxSize || 10000; // Increased from 500
    this.maxMemoryMB = options.maxMemoryMB || 100; // 100MB memory limit
    this.ttl = options.ttl || 3600000; // 1 hour default TTL
    
    // Separate storage for different data types
    this.caches = {
      graphql: new Map(),      // GraphQL query results
      tmdb: new Map(),         // TMDB API responses
      jikan: new Map(),        // Jikan API responses
      gemini: new Map(),       // Gemini AI responses
      enrichment: new Map(),   // Enrichment results
      media: new Map(),        // Media metadata
      streaming: new Map()     // Streaming URLs
    };
    
    // Access tracking for LRU
    this.accessOrder = {
      graphql: [],
      tmdb: [],
      jikan: [],
      gemini: [],
      enrichment: [],
      media: [],
      streaming: []
    };
    
    // Memory usage tracking
    this.memoryUsage = 0;
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      memoryEvictions: 0
    };
    
    // Cleanup interval
    this.cleanupInterval = setInterval(() => this.cleanup(), 300000); // 5 minutes
  }

  /**
   * Generate optimized cache key
   */
  generateKey(namespace, identifier, params = {}) {
    const paramString = Object.keys(params).length > 0 
      ? JSON.stringify(params, Object.keys(params).sort())
      : '';
    return `${namespace}:${identifier}${paramString ? ':' + this.hashString(paramString) : ''}`;
  }

  /**
   * Simple string hash for parameter compression
   */
  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Estimate memory usage of an object
   */
  estimateSize(obj) {
    const jsonString = JSON.stringify(obj);
    return jsonString.length * 2; // Rough estimate: 2 bytes per character
  }

  /**
   * Set cache entry with intelligent eviction
   */
  set(cacheType, key, value, customTTL = null) {
    if (!this.caches[cacheType]) {
      logger.warn(`Unknown cache type: ${cacheType}`);
      return false;
    }

    const cache = this.caches[cacheType];
    const accessOrder = this.accessOrder[cacheType];
    const ttl = customTTL || this.ttl;
    const expiresAt = Date.now() + ttl;
    const size = this.estimateSize(value);

    // Check memory limits
    if (this.memoryUsage + size > this.maxMemoryMB * 1024 * 1024) {
      this.evictByMemory();
    }

    // Remove existing entry if present
    if (cache.has(key)) {
      const oldEntry = cache.get(key);
      this.memoryUsage -= oldEntry.size;
      const index = accessOrder.indexOf(key);
      if (index > -1) accessOrder.splice(index, 1);
    }

    // Check size limits and evict LRU if necessary
    if (cache.size >= this.maxSize / Object.keys(this.caches).length) {
      this.evictLRU(cacheType);
    }

    // Add new entry
    cache.set(key, {
      value,
      expiresAt,
      size,
      createdAt: Date.now()
    });
    
    accessOrder.push(key);
    this.memoryUsage += size;

    return true;
  }

  /**
   * Get cache entry with access tracking
   */
  get(cacheType, key) {
    if (!this.caches[cacheType]) {
      this.stats.misses++;
      return null;
    }

    const cache = this.caches[cacheType];
    const accessOrder = this.accessOrder[cacheType];
    
    if (!cache.has(key)) {
      this.stats.misses++;
      return null;
    }

    const entry = cache.get(key);
    
    // Check expiration
    if (Date.now() > entry.expiresAt) {
      this.delete(cacheType, key);
      this.stats.misses++;
      return null;
    }

    // Update access order (move to end)
    const index = accessOrder.indexOf(key);
    if (index > -1) {
      accessOrder.splice(index, 1);
      accessOrder.push(key);
    }

    this.stats.hits++;
    return entry.value;
  }

  /**
   * Delete cache entry
   */
  delete(cacheType, key) {
    if (!this.caches[cacheType]) return false;

    const cache = this.caches[cacheType];
    const accessOrder = this.accessOrder[cacheType];
    
    if (cache.has(key)) {
      const entry = cache.get(key);
      this.memoryUsage -= entry.size;
      cache.delete(key);
      
      const index = accessOrder.indexOf(key);
      if (index > -1) accessOrder.splice(index, 1);
      
      return true;
    }
    return false;
  }

  /**
   * Evict least recently used entries
   */
  evictLRU(cacheType) {
    const cache = this.caches[cacheType];
    const accessOrder = this.accessOrder[cacheType];
    
    if (accessOrder.length === 0) return;
    
    const keyToEvict = accessOrder.shift();
    if (cache.has(keyToEvict)) {
      const entry = cache.get(keyToEvict);
      this.memoryUsage -= entry.size;
      cache.delete(keyToEvict);
      this.stats.evictions++;
    }
  }

  /**
   * Evict entries to free memory
   */
  evictByMemory() {
    const targetMemory = this.maxMemoryMB * 1024 * 1024 * 0.8; // Target 80% of max
    
    // Sort all entries by access time across all caches
    const allEntries = [];
    for (const [cacheType, cache] of Object.entries(this.caches)) {
      for (const [key, entry] of cache.entries()) {
        allEntries.push({ cacheType, key, entry });
      }
    }
    
    // Sort by creation time (oldest first)
    allEntries.sort((a, b) => a.entry.createdAt - b.entry.createdAt);
    
    // Evict oldest entries until memory target is reached
    for (const { cacheType, key } of allEntries) {
      if (this.memoryUsage <= targetMemory) break;
      this.delete(cacheType, key);
      this.stats.memoryEvictions++;
    }
  }

  /**
   * Cleanup expired entries
   */
  cleanup() {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [cacheType, cache] of Object.entries(this.caches)) {
      const keysToDelete = [];
      
      for (const [key, entry] of cache.entries()) {
        if (now > entry.expiresAt) {
          keysToDelete.push(key);
        }
      }
      
      for (const key of keysToDelete) {
        this.delete(cacheType, key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      logger.info(`Cache cleanup: removed ${cleanedCount} expired entries`);
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalEntries = Object.values(this.caches).reduce((sum, cache) => sum + cache.size, 0);
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0;
    
    return {
      ...this.stats,
      totalEntries,
      memoryUsageMB: (this.memoryUsage / 1024 / 1024).toFixed(2),
      hitRate: `${hitRate}%`,
      cacheBreakdown: Object.fromEntries(
        Object.entries(this.caches).map(([type, cache]) => [type, cache.size])
      )
    };
  }

  /**
   * Clear specific cache type or all caches
   */
  clear(cacheType = null) {
    if (cacheType && this.caches[cacheType]) {
      this.caches[cacheType].clear();
      this.accessOrder[cacheType] = [];
    } else {
      // Clear all caches
      for (const [type, cache] of Object.entries(this.caches)) {
        cache.clear();
        this.accessOrder[type] = [];
      }
      this.memoryUsage = 0;
    }
  }

  /**
   * Destroy cache and cleanup
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Create singleton instance
const unifiedCache = new UnifiedCache({
  maxSize: 10000,
  maxMemoryMB: 100,
  ttl: 3600000 // 1 hour
});

// Graceful shutdown
process.on('SIGTERM', () => unifiedCache.destroy());
process.on('SIGINT', () => unifiedCache.destroy());

module.exports = {
  UnifiedCache,
  cache: unifiedCache
};
