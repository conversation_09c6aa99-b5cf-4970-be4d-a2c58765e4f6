/**
 * Size-limited LRU (Least Recently Used) cache implementation
 * This cache automatically removes the least recently used items when it reaches its size limit
 */
class LRUCache {
  /**
   * Create a new LRU cache
   * @param {number} maxSize - Maximum number of items to store in the cache
   */
  constructor(maxSize = 1000) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }

  /**
   * Set a value in the cache
   * @param {string} key - The cache key
   * @param {any} value - The value to store
   * @returns {LRUCache} - The cache instance for chaining
   */
  set(key, value) {
    // If the key already exists, delete it first to update its position
    if (this.cache.has(key)) {
      this.cache.delete(key);
    }
    // If cache is full, remove the oldest entry (first item in the Map)
    else if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
    
    // Add the new entry (will be at the end, making it the most recently used)
    this.cache.set(key, value);
    return this;
  }

  /**
   * Get a value from the cache
   * @param {string} key - The cache key
   * @returns {any} - The cached value or undefined if not found
   */
  get(key) {
    // If the key doesn't exist, return undefined
    if (!this.cache.has(key)) {
      return undefined;
    }
    
    // Get the value
    const value = this.cache.get(key);
    
    // Remove and re-add the entry to make it the most recently used
    this.cache.delete(key);
    this.cache.set(key, value);
    
    return value;
  }

  /**
   * Check if a key exists in the cache
   * @param {string} key - The cache key
   * @returns {boolean} - True if the key exists, false otherwise
   */
  has(key) {
    return this.cache.has(key);
  }

  /**
   * Delete a key from the cache
   * @param {string} key - The cache key
   * @returns {boolean} - True if the key was deleted, false otherwise
   */
  delete(key) {
    return this.cache.delete(key);
  }

  /**
   * Clear the cache
   */
  clear() {
    this.cache.clear();
  }

  /**
   * Get the number of items in the cache
   * @returns {number} - The number of items in the cache
   */
  get size() {
    return this.cache.size;
  }

  /**
   * Get all keys in the cache
   * @returns {Iterator} - An iterator over the cache keys
   */
  keys() {
    return this.cache.keys();
  }

  /**
   * Get all values in the cache
   * @returns {Iterator} - An iterator over the cache values
   */
  values() {
    return this.cache.values();
  }

  /**
   * Get all entries in the cache
   * @returns {Iterator} - An iterator over the cache entries
   */
  entries() {
    return this.cache.entries();
  }
}

/**
 * Factory function to create a new LRU cache with a specific size limit
 * @param {number} maxSize - Maximum number of items to store in the cache
 * @returns {LRUCache} - A new LRU cache instance
 */
function createLRUCache(maxSize = 1000) {
  return new LRUCache(maxSize);
}

module.exports = {
  LRUCache,
  createLRUCache
};
