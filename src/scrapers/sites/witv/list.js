const axios = require('axios');
const cheerio = require('cheerio');
const { WITV_BASE } = require('../../../config/constants');
const logger = require('../../../utils/logger');
const Config = require('../../../db/models/Config');

async function scrapeWitvList(maxPages) {
  const channels = [];

  // Get the latest WITV_BASE from the database
  const witvBase = await Config.getValue('WITV_BASE', WITV_BASE);
  logger.info(`Using WiTV base URL: ${witvBase}`);

  for (let page = 1; page <= maxPages; page++) {
    try {
      const url = page === 1 ? `https://${witvBase}/chaines-live/` : `https://${witvBase}/chaines-live/page/${page}/`;
      logger.info(`Scraping WiTV list from: ${url}`);
      const { data } = await axios.get(url, { timeout: 10000 });
      const $ = cheerio.load(data);

      $('.holographic-card .ann-short_item').each((i, el) => {
        const title = $(el).find('.ann-short_price').text().trim();
        const detailUrl = $(el).find('a[href*="chaines-live"]').attr('href');
        const image = $(el).find('img.xfieldimage.poster').attr('src');

        if (title && detailUrl) {
          channels.push({
            title,
            detailUrl: detailUrl.startsWith('http') ? detailUrl : `https://${witvBase}${detailUrl}`,
            image: image.startsWith('http') ? image : `https://${witvBase}${image}`
          });
        }
      });

      if (channels.length === 0 && page > 1) break;
      logger.info(`Scraped WiTV page ${page} - Channels: ${channels.length}`);
    } catch (error) {
      if (error.response?.status === 404) break;
      logger.error(`WiTV list scrape error on page ${page}: ${error.message}`);
      break;
    }
  }
  logger.info(`Total channels scraped: ${channels.length}`);
  return channels;
}

module.exports = { scrapeWitvList };