const mongoose = require('mongoose');
const { Schema } = mongoose;
// Remove this line since we're defining EpisodeSchema directly in this file
// const EpisodeSchema = require('./EpisodeSchema');
const logger = require('../../utils/logger');

const StreamingUrlSchema = new Schema({
    url: { type: String, required: true },
    provider: { type: String, default: 'unknown' },
    language: { type: String, default: 'unknown' },
    lastChecked: { type: Date, default: Date.now },
    isActive: { type: Boolean, default: true },
    sourceStreamUrl: { type: String, default: null },
    size: { type: String, default: null },   // Added
    type: { type: String, default: null },   // Added (HLS or MP4)
    method: { type: String, default: null } // Added
});

// Keep only one EpisodeSchema definition
const EpisodeSchema = new Schema({
    episodeNumber: { type: String, required: true }, // Keep as string
    season: { type: String, default: '1' },  // Keep as string
    streamingUrls: [StreamingUrlSchema]
});

const MetadataSchema = new Schema({
    synopsis: { type: String, default: '' },
    actors: [String],
    year: { type: String, default: '' },
    genre: { type: String, default: '' },
    origin: { type: String, default: '' },
    creator: { type: String, default: '' },
    duration: { type: String, default: '' }
});

// Existing TmdbSchema for the overall show
const TmdbSchema = new Schema({
    id: Number,
    title: String,
    overview: String,
    release_date: String, // Or Date
    poster_path: String,
    vote_average: Number,
    genres: [{ type: String }]
});

// NEW: Schema for detailed TMDB Season information
const TmdbSeasonSchema = new Schema({
    air_date: String, // Or Date
    // episode_count: Number, // TMDB's count of episodes for this season.
    tmdb_season_id: { type: Number }, // TMDB's own ID for the season
    name: String, // e.g., "Season 1"
    overview: String,
    poster_path: String,
    season_number: Number,
    vote_average: Number, // TMDB's vote average for this specific season
    episodes: [{ // Optional: if you want to store TMDB's episode list for the season
        air_date: String,
        episode_number: Number,
        tmdb_episode_id: { type: Number },
        name: String,
        overview: String,
        still_path: String,
        vote_average: Number,
    }]
}, { _id: false }); // _id: false if you don't need a separate MongoDB ID for each season entry

const SeriesSchema = new Schema({
    title: { type: String, required: true },
    originalTitle: { type: String },
    description: { type: String },
    year: { type: String }, // Or Number
    detailUrl: { type: String, required: true },
    // Remove duplicate detailUrlPath
    cleanedTitle: String,
    // Remove duplicate season field
    thumbnail: { type: String, default: '' },
    image: String,
    sourceSite: { type: String }, // e.g., 'wiflix', 'witv'
    detailUrlPath: { type: String, unique: true, required: true }, // Unique path for the series on the source site
    episodes: [EpisodeSchema],
    season: { type: String, default: '1' }, // This might represent the latest scraped season number from the source
    totalSeasons: { type: Number }, // Total seasons known from the source or TMDB
    status: { type: String }, // e.g., "Returning Series", "Ended"
    tmdb: TmdbSchema, // Existing TMDB info for the whole series
    tmdbSeasons: [TmdbSeasonSchema], // NEW: Array to store detailed TMDB data for each season
    lastScrapedAt: { type: Date, default: Date.now },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
}, {
    collection: 'series',
    timestamps: true
});

SeriesSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    next();
});

module.exports = mongoose.model('Series', SeriesSchema);