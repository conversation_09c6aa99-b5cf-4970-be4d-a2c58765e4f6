const fs = require('fs');

/**
 * Fetches direct video URL from Uqload by parsing <script> tags in the embed page for video URLs.
 */
async function fetchUqloadStream(inputUrl, config, cookies, debug = false) { // Added debug parameter
    const html = cookies.eResponse.data;
    const scriptMatch = html.match(/<script[^>]*>([\s\S]*?)<\/script>/gi);
    if (scriptMatch) {
        for (const script of scriptMatch) {
            const urlMatch = script.match(/(https?:\/\/[^\s'"]+\.(mp4|m3u8))/i);
            if (urlMatch) {
                console.log(`[Uqload] Extracted from script: ${urlMatch[1]}`);
                return urlMatch[1];
            }
        }
    }
    if (debug) { // Conditionally write debug file
        const debugFile = `uqload_debug_${Date.now()}.html`;
        fs.writeFileSync(debugFile, html);
        console.log(`[Uqload] Debug HTML saved to: ${debugFile}`); // Informative log
    }
    throw new Error(`No direct source found in embed page or scripts${debug ? ` - check ${debugFile}` : ''}`); // Conditional debug file message
}

module.exports = fetchUqloadStream;