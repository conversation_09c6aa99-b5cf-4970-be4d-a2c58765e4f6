'use client'
import { useState, useEffect, useRef } from 'react'
import { usePlayer } from '@/context/PlayerContext'
import { useLazyQuery } from '@apollo/client'
import { GET_STREAM_URL, GET_PLAY_URL } from '@/lib/queries'
import Hls from 'hls.js';

export default function VideoPlayer() {
  const { isPlayerOpen, currentMedia, closePlayer, playerState, updatePlayerState } = usePlayer()
  const [videoUrl, setVideoUrl] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [streamType, setStreamType] = useState('') // 'hls', 'mp4', 'iframe'
  const [streamMethod, setStreamMethod] = useState('') // 'GET', 'iframe'
  const videoRef = useRef(null)
  
  // GraphQL queries
  const [getStreamUrl] = useLazyQuery(GET_STREAM_URL)
  const [getPlayUrl] = useLazyQuery(GET_PLAY_URL)

  // Controls visibility
  const [showControls, setShowControls] = useState(true)
  const controlsTimeoutRef = useRef(null)

  useEffect(() => {
    if (isPlayerOpen && currentMedia) {
      // If the player is opened with an iframe method, set videoUrl and streamMethod directly
      if (currentMedia.streamMethod === 'iframe' && currentMedia.videoUrl) {
        setVideoUrl(currentMedia.videoUrl);
        setStreamMethod('iframe');
        setStreamType('iframe');
        setIsLoading(false);
        setError(null);
        return;
      }
      // For direct source, always use loadVideoSource
      loadVideoSource()
    }
    
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [isPlayerOpen, currentMedia])

  // Handle controls visibility
  useEffect(() => {
    if (showControls && playerState.isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 3000)
    }
    
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [showControls, playerState.isPlaying])

  // HLS.js integration for HLS streams
  useEffect(() => {
    if (!videoUrl || !isPlayerOpen) return;
    if (streamType === 'hls') {
      if (Hls.isSupported()) {
        const hls = new Hls();
        hls.loadSource(videoUrl);
        hls.attachMedia(videoRef.current);
        hls.on(Hls.Events.ERROR, (event, data) => {
          console.error('HLS.js error:', data);
        });
        return () => {
          hls.destroy();
        };
      } else if (videoRef.current && videoRef.current.canPlayType('application/vnd.apple.mpegurl')) {
        videoRef.current.src = videoUrl;
      }
    } else if (videoUrl && videoRef.current) {
      videoRef.current.src = videoUrl;
    }
  }, [videoUrl, streamType, isPlayerOpen]);

  // Load video source based on media type
  const loadVideoSource = async () => {
    if (!currentMedia) return
    setIsLoading(true)
    setError(null)
    try {
      console.log('Loading video source for:', {
        id: currentMedia.id,
        type: currentMedia.type,
        streamId: currentMedia.streamId,
        episodeNumber: currentMedia.episodeNumber,
        language: currentMedia.language,
        directSourceUrl: currentMedia.directSourceUrl
      })
      // If directSourceUrl is provided, use it directly
      if (currentMedia.directSourceUrl) {
        setVideoUrl(currentMedia.directSourceUrl);
        setStreamType(currentMedia.type?.toLowerCase() || '');
        setStreamMethod(currentMedia.method?.toLowerCase() || '');
        console.log('Using direct source URL:', {
          url: currentMedia.directSourceUrl,
          type: currentMedia.type,
          method: currentMedia.method,
          size: currentMedia.size
        });
        setIsLoading(false);
        return;
      }
      // --- STRICT TYPE GUARDING AND LOGGING ---
      const allowedTypes = ['MOVIE', 'SERIES', 'ANIME', 'LIVETV']
      let type = (currentMedia.type || 'SERIES').toString().toUpperCase()
      if (!allowedTypes.includes(type)) {
        // Try to fallback to mediaType if available
        type = currentMedia.mediaType?.toUpperCase() || 'SERIES';
      }
      if (!allowedTypes.includes(type)) {
        console.error('Invalid type for stream query:', type, 'Allowed:', allowedTypes)
        setError(`Invalid type for stream query: ${type}`)
        setIsLoading(false)
        return
      }
      const itemId = currentMedia.id?.toString()
      const streamId = currentMedia.streamId?.toString()
      if (!itemId || !streamId) {
        console.error('Missing itemId or streamId for stream query:', { itemId, streamId })
        setError('Missing itemId or streamId for stream query')
        setIsLoading(false)
        return
      }
      console.log('Calling stream query with:', { itemId, type, streamId })
      // --- END TYPE GUARDING ---
      const { data, error: queryError } = await getStreamUrl({
        variables: {
          itemId,
          type,
          streamId
        },
        fetchPolicy: 'network-only'
      })
      if (queryError) {
        console.error('GraphQL error from stream query:', queryError)
        throw new Error(`GraphQL error: ${queryError.message}`)
      }
      console.log('Stream query response:', data)
      if (data?.stream?.sourceStreamUrl) {
        setVideoUrl(data.stream.sourceStreamUrl)
        setStreamType(data.stream.type?.toLowerCase() || '')
        setStreamMethod(data.stream.method?.toLowerCase() || '')
        console.log('Stream URL set successfully:', {
          url: data.stream.sourceStreamUrl,
          type: data.stream.type,
          method: data.stream.method,
          size: data.stream.size
        })
      } else {
        console.error('No sourceStreamUrl found in response:', data)
        throw new Error('No sourceStreamUrl found')
      }
    } catch (err) {
      console.error('Error loading video:', err)
      setError(err.message || 'Failed to load video')
    } finally {
      setIsLoading(false)
    }
  }

  // Video event handlers
  const handlePlay = () => {
    updatePlayerState({ isPlaying: true })
  }

  const handlePause = () => {
    updatePlayerState({ isPlaying: false })
  }

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      updatePlayerState({
        currentTime: videoRef.current.currentTime,
        duration: videoRef.current.duration || 0
      })
    }
  }

  const handleVolumeChange = () => {
    if (videoRef.current) {
      updatePlayerState({
        volume: videoRef.current.volume,
        isMuted: videoRef.current.muted
      })
    }
  }

  const togglePlay = () => {
    if (videoRef.current) {
      if (playerState.isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
    }
  }

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted
    }
  }

  const handleFullscreen = () => {
    const container = document.querySelector('.player-container')
    if (!container) return
    
    if (!document.fullscreenElement) {
      container.requestFullscreen().catch(err => {
        console.error('Error attempting to enable fullscreen:', err)
      })
      updatePlayerState({ isFullscreen: true })
    } else {
      document.exitFullscreen()
      updatePlayerState({ isFullscreen: false })
    }
  }

  const handleSeek = (e) => {
    const progressBar = e.currentTarget
    const rect = progressBar.getBoundingClientRect()
    const pos = (e.clientX - rect.left) / rect.width
    
    if (videoRef.current && !isNaN(videoRef.current.duration)) {
      videoRef.current.currentTime = pos * videoRef.current.duration
    }
  }

  // Show controls when mouse moves
  const handleMouseMove = () => {
    setShowControls(true)
    
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
    
    if (playerState.isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 3000)
    }
  }

  if (!isPlayerOpen || !currentMedia) {
    return null
  }

  // Format time for display (mm:ss)
  const formatTime = (seconds) => {
    if (isNaN(seconds)) return '00:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      <div 
        className="player-container relative w-full h-full"
        onMouseMove={handleMouseMove}
      >
        {/* Close Button */}
        <button
          onClick={closePlayer}
          className="absolute top-4 right-4 z-10 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-colors"
        >
          <i className="fas fa-times text-xl"></i>
        </button>

        {/* Video Player */}
        {isLoading ? (
          <div className="w-full h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white"></div>
          </div>
        ) : error ? (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-white text-center">
              <i className="fas fa-exclamation-triangle text-6xl mb-4 text-red-500"></i>
              <h2 className="text-2xl font-bold mb-2">Error</h2>
              <p>{error}</p>
            </div>
          </div>
        ) : streamMethod === 'iframe' ? (
          <iframe 
            src={videoUrl || null} 
            className="w-full h-full" 
            allowFullScreen 
            allow="autoplay; encrypted-media"
          ></iframe>
        ) : (
          <video
            ref={videoRef}
            className="w-full h-full"
            src={videoUrl || null}
            autoPlay
            onPlay={handlePlay}
            onPause={handlePause}
            onTimeUpdate={handleTimeUpdate}
            onVolumeChange={handleVolumeChange}
          ></video>
        )}

        {/* Player Controls - Only show for direct video, not iframes */}
        {streamMethod !== 'iframe' && showControls && (
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
            {/* Title */}
            <div className="text-white mb-2">
              <h3 className="font-bold">{currentMedia.title}</h3>
              {currentMedia.episodeNumber && (
                <p className="text-sm">Episode {currentMedia.episodeNumber}</p>
              )}
            </div>
            
            {/* Progress Bar */}
            <div 
              className="w-full h-2 bg-gray-700 rounded-full mb-2 cursor-pointer"
              onClick={handleSeek}
            >
              <div 
                className="h-full bg-red-600 rounded-full"
                style={{ width: `${(playerState.currentTime / playerState.duration) * 100 || 0}%` }}
              ></div>
            </div>
            
            {/* Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Play/Pause */}
                <button onClick={togglePlay} className="text-white">
                  {playerState.isPlaying ? (
                    <i className="fas fa-pause"></i>
                  ) : (
                    <i className="fas fa-play"></i>
                  )}
                </button>
                
                {/* Volume */}
                <button onClick={toggleMute} className="text-white">
                  {playerState.isMuted ? (
                    <i className="fas fa-volume-mute"></i>
                  ) : (
                    <i className="fas fa-volume-up"></i>
                  )}
                </button>
                
                {/* Time */}
                <div className="text-white text-sm">
                  {formatTime(playerState.currentTime)} / {formatTime(playerState.duration)}
                </div>
              </div>
              
              <div>
                {/* Fullscreen */}
                <button onClick={handleFullscreen} className="text-white">
                  <i className="fas fa-expand"></i>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
