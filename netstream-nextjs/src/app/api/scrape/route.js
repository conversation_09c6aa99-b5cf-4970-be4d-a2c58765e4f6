import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../lib/mongodb';

// Extract the base URL from API_URL (server-side) or NEXT_PUBLIC_API_URL (client-side) or use default
const BACKEND_URL = (process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://netstream-backend-container:3001/graphql').replace('/graphql', '');

export async function POST(request) {
  try {
    const body = await request.json();
    const { 
      mode = 'latest', 
      type = 'all', 
      pages = { movies: 2, series: 2, anime: 2, livetv: 4 }, 
      enrichment = false, 
      gemini = false 
    } = body;

    // Connect to database to log the scraping job
    const db = await connectToDatabase();
    
    // Create a scraping job record
    const jobId = `scrape_${Date.now()}`;
    const scrapingJob = {
      jobId,
      mode,
      type,
      pages,
      enrichment,
      gemini,
      status: 'started',
      startedAt: new Date(),
      estimatedDuration: `${Object.values(pages).reduce((sum, pageCount) => sum + pageCount, 0) * 2} minutes`
    };

    // Save job to database
    await db.collection('scraping_jobs').insertOne(scrapingJob);

    // Start the actual scraping process by calling the backend API
    process.nextTick(async () => {
      try {
        console.log(`Starting scraping job ${jobId} with mode: ${mode}, type: ${type}, pages:`, pages);

        // Update job status to running
        await db.collection('scraping_jobs').updateOne(
          { jobId },
          {
            $set: {
              status: 'running',
              startedAt: new Date()
            }
          }
        );

        // Call the backend scraping API
        const backendResponse = await fetch(`${BACKEND_URL}/api/scrape`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            mode,
            type,
            pages: Math.max(pages.movies || 2, pages.series || 2, pages.anime || 2, pages.livetv || 4),
            enrichment,
            gemini
          })
        });

        const result = await backendResponse.json();

        if (result.success) {
          console.log(`Scraping job ${jobId} started successfully on backend`);

          // Update job status to completed (the backend handles the actual scraping)
          await db.collection('scraping_jobs').updateOne(
            { jobId },
            {
              $set: {
                status: 'completed',
                completedAt: new Date(),
                output: `Backend scraping started: ${result.message}`,
                backendJobInfo: result
              }
            }
          );
        } else {
          throw new Error(`Backend scraping failed: ${result.error}`);
        }

      } catch (error) {
        console.error(`Scraping job ${jobId} failed:`, error);

        // Update job status to failed
        await db.collection('scraping_jobs').updateOne(
          { jobId },
          {
            $set: {
              status: 'failed',
              failedAt: new Date(),
              error: error.message
            }
          }
        );
      }
    });

    return NextResponse.json({
      success: true,
      message: `Scraping job started successfully`,
      data: {
        ...scrapingJob,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Scraping API error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to start scraping: ${error.message}`
    }, { status: 500 });
  }
} 