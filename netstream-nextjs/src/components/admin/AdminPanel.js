'use client'
import { useState } from 'react'
import { useQuery, useMutation } from '@apollo/client'
import { GET_DATABASE_STATS, GET_CONTENT_OVERVIEW, SEARCH_MEDIA, DELETE_ITEM, UPDATE_ITEM } from '@/lib/queries'
import { useAdminContext } from '@/context/AdminContext'
import { AdminContentCard } from './AdminContentCard'
// Import new tab components
import PerformanceTab from '@/app/admin/components/PerformanceTab'
import ScrapingTab from '@/app/admin/components/ScrapingTab'
import UsersTab from '@/app/admin/components/UsersTab'
import SystemTab from '@/app/admin/components/SystemTab'
import ConfigurationTab from '@/app/admin/components/ConfigurationTab'
import LogsTab from '@/app/admin/components/LogsTab'

const TABS = [
  { key: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
  { key: 'content', label: 'Content', icon: 'fas fa-film' },
  { key: 'performance', label: 'Performance', icon: 'fas fa-chart-line' },
  { key: 'scraping', label: 'Scraping', icon: 'fas fa-spider' },
  { key: 'users', label: 'Users', icon: 'fas fa-users' },
  { key: 'system', label: 'System', icon: 'fas fa-server' },
  { key: 'config', label: 'Config', icon: 'fas fa-cog' },
  { key: 'logs', label: 'Logs', icon: 'fas fa-file-alt' },
]

export default function AdminPanel() {
  const { user, logout } = useAdminContext()
  const { data: dbStatsData, loading: dbLoading, error: dbError } = useQuery(GET_DATABASE_STATS)
  const { data: overviewData, loading: overviewLoading, error: overviewError } = useQuery(GET_CONTENT_OVERVIEW)
  const [activeTab, setActiveTab] = useState('dashboard')
  const [deleteItem] = useMutation(DELETE_ITEM)
  const [updateItem] = useMutation(UPDATE_ITEM)
  
  const handleDelete = async (id, type) => {
    if (!window.confirm('Are you sure you want to delete this item?')) return;
    try {
      await deleteItem({ variables: { id, type, adminToken: user.token } });
      // Refresh search or list
      refetch();
    } catch (err) {
      alert('Delete failed: ' + err.message);
    }
  }

  const handleUpdate = async (editedItem) => {
    try {
      if (!user?.token) {
        throw new Error('No admin token available');
      }

      const itemInput = {
        title: editedItem.title,
        displayTitle: editedItem.displayTitle,
        detailUrl: editedItem.detailUrl,
        detailUrlPath: editedItem.detailUrlPath,
        cleanedTitle: editedItem.cleanedTitle,
        thumbnail: editedItem.thumbnail,
        image: editedItem.image,
        season: editedItem.season,
        metadata: editedItem.metadata ? {
          synopsis: editedItem.metadata.synopsis,
          actors: editedItem.metadata.actors,
          year: editedItem.metadata.year,
          genre: editedItem.metadata.genre,
          origin: editedItem.metadata.origin,
          creator: editedItem.metadata.creator,
          duration: editedItem.metadata.duration
        } : undefined,
        streamingUrls: editedItem.streamingUrls?.map(url => ({
          id: url.id,
          url: url.url,
          provider: url.provider,
          language: url.language,
          lastChecked: url.lastChecked,
          isActive: url.isActive,
          sourceStreamUrl: url.sourceStreamUrl,
          size: url.size,
          type: url.type,
          method: url.method
        })),
        episodes: editedItem.episodes?.map(episode => ({
          episodeNumber: episode.episodeNumber,
          season: episode.season,
          language: episode.language,
          streamingUrls: episode.streamingUrls?.map(url => ({
            id: url.id,
            url: url.url,
            provider: url.provider,
            language: url.language,
            lastChecked: url.lastChecked,
            isActive: url.isActive,
            sourceStreamUrl: url.sourceStreamUrl,
            size: url.size,
            type: url.type,
            method: url.method
          }))
        })),
        tmdb: editedItem.tmdb ? {
          id: editedItem.tmdb.id,
          title: editedItem.tmdb.title,
          original_title: editedItem.tmdb.original_title,
          overview: editedItem.tmdb.overview,
          release_date: editedItem.tmdb.release_date,
          poster_path: editedItem.tmdb.poster_path,
          backdrop_path: editedItem.tmdb.backdrop_path,
          vote_average: editedItem.tmdb.vote_average,
          vote_count: editedItem.tmdb.vote_count,
          genres: editedItem.tmdb.genres,
          number_of_seasons: editedItem.tmdb.number_of_seasons,
          number_of_episodes: editedItem.tmdb.number_of_episodes,
          in_production: editedItem.tmdb.in_production,
          status: editedItem.tmdb.status
        } : undefined,
        jikan: editedItem.jikan ? {
          mal_id: editedItem.jikan.mal_id,
          title: {
            default: editedItem.jikan.title?.default,
            english: editedItem.jikan.title?.english,
            japanese: editedItem.jikan.title?.japanese
          },
          type: editedItem.jikan.type,
          source: editedItem.jikan.source,
          episodes: editedItem.jikan.episodes,
          status: editedItem.jikan.status,
          airing: editedItem.jikan.airing,
          aired: editedItem.jikan.aired ? {
            from: editedItem.jikan.aired.from,
            to: editedItem.jikan.aired.to,
            string: editedItem.jikan.aired.string
          } : undefined,
          duration: editedItem.jikan.duration,
          rating: editedItem.jikan.rating,
          score: editedItem.jikan.score,
          scored_by: editedItem.jikan.scored_by,
          rank: editedItem.jikan.rank,
          popularity: editedItem.jikan.popularity,
          members: editedItem.jikan.members,
          favorites: editedItem.jikan.favorites,
          synopsis: editedItem.jikan.synopsis,
          background: editedItem.jikan.background,
          season: editedItem.jikan.season,
          year: editedItem.jikan.year,
          studios: editedItem.jikan.studios?.map(studio => ({
            mal_id: studio.mal_id,
            name: studio.name
          })),
          genres: editedItem.jikan.genres?.map(genre => ({
            mal_id: genre.mal_id,
            name: genre.name,
            type: genre.type
          })),
          themes: editedItem.jikan.themes?.map(theme => ({
            mal_id: theme.mal_id,
            name: theme.name,
            type: theme.type
          })),
          demographics: editedItem.jikan.demographics?.map(demographic => ({
            mal_id: demographic.mal_id,
            name: demographic.name,
            type: demographic.type
          })),
          streaming_platforms: editedItem.jikan.streaming_platforms?.map(platform => ({
            name: platform.name,
            url: platform.url
          }))
        } : undefined
      }

      console.log('Updating item with:', {
        id: editedItem.id,
        type: editedItem.__typename.toUpperCase(),
        item: itemInput,
        adminToken: user.token
      });

      const result = await updateItem({
        variables: {
          id: editedItem.id,
          type: editedItem.__typename.toUpperCase(),
          item: itemInput,
          adminToken: user.token
        }
      });

      if (!result.data?.updateItem?.success) {
        throw new Error(result.data?.updateItem?.message || 'Update failed');
      }

      refetch(); // Refresh the list after update
    } catch (error) {
      console.error('Error updating item:', error);
      alert('Update failed: ' + error.message);
      throw error;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <h2 className="text-2xl font-bold text-white">Admin Panel</h2>
          <div className="flex space-x-2">
            {TABS.map(tab => (
              <button
                key={tab.key}
                className={`px-3 py-2 rounded-lg flex items-center space-x-2 text-sm font-semibold transition-colors ${activeTab === tab.key ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
                onClick={() => setActiveTab(tab.key)}
              >
                <i className={`${tab.icon}`}></i>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
        <button
          onClick={logout}
          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          <i className="fas fa-sign-out-alt mr-2"></i>
          Logout
        </button>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'dashboard' && (
          <DashboardTab
            dbStats={dbStatsData?.databaseStats}
            dbLoading={dbLoading}
            dbError={dbError}
            overview={overviewData?.contentOverview}
            overviewLoading={overviewLoading}
            overviewError={overviewError}
          />
        )}
        {activeTab === 'content' && <ContentTab />}
        {activeTab === 'performance' && <PerformanceTab />}
        {activeTab === 'scraping' && <ScrapingTab />}
        {activeTab === 'users' && <UsersTab />}
        {activeTab === 'system' && <SystemTab />}
        {activeTab === 'config' && <ConfigurationTab />}
        {activeTab === 'logs' && <LogsTab />}
      </div>
    </div>
  )
}

function DashboardTab({ dbStats, dbLoading, dbError, overview, overviewLoading, overviewError }) {
  if (dbLoading || overviewLoading) {
    return (
      <div className="flex justify-center py-8">
        <i className="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
      </div>
    )
  }
  if (dbError || overviewError) {
    return (
      <div className="text-center py-8 text-red-500">
        Error loading admin stats: {dbError?.message || overviewError?.message}
      </div>
    )
  }
  return (
    <>
      {/* Database Stats Grid */}
      {dbStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="fas fa-film text-blue-500 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-semibold text-white">Movies</h3>
                <p className="text-3xl font-bold text-blue-500">{dbStats.movies || 0}</p>
              </div>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="fas fa-tv text-green-500 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-semibold text-white">Series</h3>
                <p className="text-3xl font-bold text-green-500">{dbStats.series || 0}</p>
              </div>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="fas fa-dragon text-purple-500 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-semibold text-white">Anime</h3>
                <p className="text-3xl font-bold text-purple-500">{dbStats.anime || 0}</p>
              </div>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="fas fa-broadcast-tower text-red-500 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-semibold text-white">Live TV</h3>
                <p className="text-3xl font-bold text-red-500">{dbStats.livetv || 0}</p>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Content Overview */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Recently Added</h3>
            <p className="text-2xl font-bold text-blue-400">{overview.recentlyAdded}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Trending</h3>
            <p className="text-2xl font-bold text-green-400">{overview.trending}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Most Watched</h3>
            <p className="text-2xl font-bold text-purple-400">{overview.mostWatched}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Total Views</h3>
            <p className="text-2xl font-bold text-red-400">{overview.totalViews}</p>
          </div>
        </div>
      )}
    </>
  )
}

function ContentTab() {
  const [query, setQuery] = useState('')
  const [type, setType] = useState('')
  const { user } = useAdminContext()
  const { data, loading, error, refetch } = useQuery(SEARCH_MEDIA, {
    variables: { query, page: 1, limit: 20 },
    skip: !query
  })
  const [deleteItem] = useMutation(DELETE_ITEM)
  const [updateItem] = useMutation(UPDATE_ITEM)

  const items = data?.search?.items || []
  const filtered = type ? items.filter(i => i.__typename?.toLowerCase() === type) : items

  const handleUpdate = async (editedItem) => {
    try {
      if (!user?.token) {
        throw new Error('No admin token available');
      }

      const itemInput = {
        title: editedItem.title,
        displayTitle: editedItem.displayTitle,
        detailUrl: editedItem.detailUrl,
        detailUrlPath: editedItem.detailUrlPath,
        cleanedTitle: editedItem.cleanedTitle,
        thumbnail: editedItem.thumbnail,
        image: editedItem.image,
        season: editedItem.season,
        metadata: editedItem.metadata ? {
          synopsis: editedItem.metadata.synopsis,
          actors: editedItem.metadata.actors,
          year: editedItem.metadata.year,
          genre: editedItem.metadata.genre,
          origin: editedItem.metadata.origin,
          creator: editedItem.metadata.creator,
          duration: editedItem.metadata.duration
        } : undefined,
        streamingUrls: editedItem.streamingUrls?.map(url => ({
          id: url.id,
          url: url.url,
          provider: url.provider,
          language: url.language,
          lastChecked: url.lastChecked,
          isActive: url.isActive,
          sourceStreamUrl: url.sourceStreamUrl,
          size: url.size,
          type: url.type,
          method: url.method
        })),
        episodes: editedItem.episodes?.map(episode => ({
          episodeNumber: episode.episodeNumber,
          season: episode.season,
          language: episode.language,
          streamingUrls: episode.streamingUrls?.map(url => ({
            id: url.id,
            url: url.url,
            provider: url.provider,
            language: url.language,
            lastChecked: url.lastChecked,
            isActive: url.isActive,
            sourceStreamUrl: url.sourceStreamUrl,
            size: url.size,
            type: url.type,
            method: url.method
          }))
        })),
        tmdb: editedItem.tmdb ? {
          id: editedItem.tmdb.id,
          title: editedItem.tmdb.title,
          original_title: editedItem.tmdb.original_title,
          overview: editedItem.tmdb.overview,
          release_date: editedItem.tmdb.release_date,
          poster_path: editedItem.tmdb.poster_path,
          backdrop_path: editedItem.tmdb.backdrop_path,
          vote_average: editedItem.tmdb.vote_average,
          vote_count: editedItem.tmdb.vote_count,
          genres: editedItem.tmdb.genres,
          number_of_seasons: editedItem.tmdb.number_of_seasons,
          number_of_episodes: editedItem.tmdb.number_of_episodes,
          in_production: editedItem.tmdb.in_production,
          status: editedItem.tmdb.status
        } : undefined,
        jikan: editedItem.jikan ? {
          mal_id: editedItem.jikan.mal_id,
          title: {
            default: editedItem.jikan.title?.default,
            english: editedItem.jikan.title?.english,
            japanese: editedItem.jikan.title?.japanese
          },
          type: editedItem.jikan.type,
          source: editedItem.jikan.source,
          episodes: editedItem.jikan.episodes,
          status: editedItem.jikan.status,
          airing: editedItem.jikan.airing,
          aired: editedItem.jikan.aired ? {
            from: editedItem.jikan.aired.from,
            to: editedItem.jikan.aired.to,
            string: editedItem.jikan.aired.string
          } : undefined,
          duration: editedItem.jikan.duration,
          rating: editedItem.jikan.rating,
          score: editedItem.jikan.score,
          scored_by: editedItem.jikan.scored_by,
          rank: editedItem.jikan.rank,
          popularity: editedItem.jikan.popularity,
          members: editedItem.jikan.members,
          favorites: editedItem.jikan.favorites,
          synopsis: editedItem.jikan.synopsis,
          background: editedItem.jikan.background,
          season: editedItem.jikan.season,
          year: editedItem.jikan.year,
          studios: editedItem.jikan.studios?.map(studio => ({
            mal_id: studio.mal_id,
            name: studio.name
          })),
          genres: editedItem.jikan.genres?.map(genre => ({
            mal_id: genre.mal_id,
            name: genre.name,
            type: genre.type
          })),
          themes: editedItem.jikan.themes?.map(theme => ({
            mal_id: theme.mal_id,
            name: theme.name,
            type: theme.type
          })),
          demographics: editedItem.jikan.demographics?.map(demographic => ({
            mal_id: demographic.mal_id,
            name: demographic.name,
            type: demographic.type
          })),
          streaming_platforms: editedItem.jikan.streaming_platforms?.map(platform => ({
            name: platform.name,
            url: platform.url
          }))
        } : undefined
      }

      await updateItem({
        variables: {
          id: editedItem.id,
          type: editedItem.__typename.toUpperCase(),
          item: itemInput,
          adminToken: user.token
        }
      })
      refetch() // Refresh the list after update
    } catch (error) {
      console.error('Error updating item:', error)
      throw error
    }
  }

  const handleDelete = async (id, type) => {
    if (!window.confirm('Are you sure you want to delete this item?')) return;
    try {
      await deleteItem({ variables: { id, type, adminToken: user.token } });
      // Refresh search or list
      refetch();
    } catch (err) {
      alert('Delete failed: ' + err.message);
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row md:items-end gap-4">
        <div>
          <label className="block text-sm text-gray-300 mb-1">Search</label>
          <input
            className="bg-gray-800 text-white px-3 py-2 rounded w-64"
            placeholder="Title or keyword..."
            value={query}
            onChange={e => setQuery(e.target.value)}
            onKeyDown={e => e.key === 'Enter' && refetch()}
          />
        </div>
        <div>
          <label className="block text-sm text-gray-300 mb-1">Type</label>
          <select
            className="bg-gray-800 text-white px-3 py-2 rounded"
            value={type}
            onChange={e => setType(e.target.value)}
          >
            <option value="">All</option>
            <option value="movie">Movies</option>
            <option value="series">Series</option>
            <option value="anime">Anime</option>
            <option value="livetv">Live TV</option>
          </select>
        </div>
        <button
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
          onClick={() => refetch()}
        >
          <i className="fas fa-search mr-2"></i>Search
        </button>
        <button
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded ml-auto"
          // onClick={handleAddContent}
        >
          <i className="fas fa-plus mr-2"></i>Add Content
        </button>
      </div>
      {loading && <div className="text-gray-400">Loading...</div>}
      {error && <div className="text-red-500">Error: {error.message}</div>}
      <div className="flex flex-col mt-4">
        {filtered.map(item => (
          <AdminContentCard key={item.id} item={item} onDelete={handleDelete} onUpdate={handleUpdate} />
        ))}
      </div>
      {filtered.length === 0 && !loading && query && (
        <div className="text-gray-400 mt-8">No results found.</div>
      )}
    </div>
  )
}
