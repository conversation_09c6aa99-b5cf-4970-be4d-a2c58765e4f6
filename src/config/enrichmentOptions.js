// File: src/config/enrichmentOptions.js
/**
 * Configuration options for the enrichment process
 *
 * This file contains configuration options for the enrichment process
 * that can be used by the server and scrapers.
 *
 * To enable advanced enrichment with Gemini AI, set USE_ADVANCED_ENRICHMENT=true in your .env file.
 * This will use Gemini AI for better title matching and verification.
 *
 * Example .env configuration:
 * ```
 * GEMINI_API_KEY=your_api_key_here
 * USE_ADVANCED_ENRICHMENT=true
 * ```
 */

const { updateConfig, getConfig } = require('../enrichment/config/enrichmentConfig');

// Default options for different modes
const ENRICHMENT_MODES = {
  // Basic mode: No Gemini AI, but fetch seasons data
  BASIC: {
    USE_GEMINI: false,
    FETCH_SEASONS: true,
    USE_ADVANCED_MATCHING: false
  },

  // Advanced mode: Use Gemini AI and fetch seasons data
  ADVANCED: {
    USE_GEMINI: true,
    FETCH_SEASONS: true,
    USE_ADVANCED_MATCHING: true
  },

  // Minimal mode: No Gemini AI, no seasons data
  MINIMAL: {
    USE_GEMINI: false,
    FETCH_SEASONS: false,
    USE_ADVANCED_MATCHING: false
  }
};

// Get the current enrichment mode from environment variables
// If USE_ADVANCED_ENRICHMENT is true in .env, use ADVANCED mode, otherwise use BASIC
const ENRICHMENT_MODE = process.env.USE_ADVANCED_ENRICHMENT === 'true' ? 'ADVANCED' : 'BASIC';

/**
 * Initialize the enrichment configuration
 * @returns {Object} The current configuration
 */
function initEnrichmentConfig() {
  // Get the mode configuration
  const modeConfig = ENRICHMENT_MODES[ENRICHMENT_MODE] || ENRICHMENT_MODES.BASIC;

  // Override with environment variables if provided
  const config = {
    ...modeConfig,
    USE_GEMINI: process.env.USE_GEMINI === 'true' || modeConfig.USE_GEMINI,
    FETCH_SEASONS: process.env.FETCH_SEASONS !== 'false' && modeConfig.FETCH_SEASONS,
    USE_ADVANCED_MATCHING: process.env.USE_ADVANCED_MATCHING === 'true' || modeConfig.USE_ADVANCED_MATCHING
  };

  // Update the configuration
  updateConfig(config);

  return getConfig();
}

module.exports = {
  initEnrichmentConfig,
  ENRICHMENT_MODES
};
