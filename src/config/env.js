const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const constants = require('./constants');

// Function to sanitize Redis host
const getRedisHost = () => {
  let redisHost = process.env.REDIS_HOST;
  if (!redisHost) {
    if (process.env.NODE_ENV === 'production') {
      redisHost = process.env.REDIS_URL || process.env.REDIS_SERVICE_URL || 'localhost';
    } else {
      redisHost = 'localhost';
    }
  }
  // Sanitize the host and remove any protocol
  if (redisHost.includes('://')) {
    return redisHost.split('://')[1].split('/')[0];
  }
  return redisHost;
}

module.exports = {
  mongoUri: process.env.MONGO_URI,
  port: process.env.FASTIFY_PORT || 3001,
  tmdbApiKey: process.env.TMDB_API_KEY,
  geminiApiKey: process.env.GEMINI_API_KEY,
  telegramToken: process.env.TELEGRAM_TOKEN,
  scrapeMode: process.env.SCRAPE_MODE || 'full',
  // Redis configuration
  redisHost: getRedisHost(),
  redisPort: process.env.REDIS_PORT || 6379,
  redisPassword: process.env.REDIS_PASSWORD,
  redisUrl: process.env.REDIS_URL,
  ...constants,
};