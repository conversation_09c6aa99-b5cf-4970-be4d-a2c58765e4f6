import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../lib/mongodb';

// In-memory log storage (in production, you'd use a proper logging service)
let logStorage = [];

// Function to add log entry
export function addLogEntry(level, source, message, meta = {}) {
  const logEntry = {
    id: Date.now() + Math.random(),
    level,
    source,
    message,
    meta,
    timestamp: new Date().toISOString()
  };
  
  logStorage.push(logEntry);
  
  // Keep only last 1000 logs in memory
  if (logStorage.length > 1000) {
    logStorage = logStorage.slice(-1000);
  }
  
  return logEntry;
}

// Initialize some sample logs if storage is empty
if (logStorage.length === 0) {
  addLogEntry('info', 'system', 'Server started successfully');
  addLogEntry('info', 'database', 'MongoDB connection established');
  addLogEntry('info', 'scraping', 'Scraping service initialized');
  addLogEntry('warning', 'system', 'High memory usage detected');
  addLogEntry('error', 'api', 'API rate limit exceeded');
  addLogEntry('debug', 'cache', 'Cache service started');
  addLogEntry('info', 'enrichment', 'Metadata enrichment completed');
  addLogEntry('warning', 'scraping', 'Some items failed to scrape');
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const level = searchParams.get('level') || '';
    const source = searchParams.get('source') || '';
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 50;

    // Filter logs based on parameters
    let filteredLogs = [...logStorage];
    
    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }
    
    if (source) {
      filteredLogs = filteredLogs.filter(log => log.source === source);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    const logsData = {
      logs: paginatedLogs,
      pagination: {
        page,
        limit,
        total: filteredLogs.length,
        totalPages: Math.ceil(filteredLogs.length / limit)
      },
      filters: {
        level,
        source
      },
      timestamp: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: logsData
    });

  } catch (error) {
    console.error('Logs fetch error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to fetch logs: ${error.message}`
    }, { status: 500 });
  }
}

export async function DELETE() {
  try {
    // Clear all logs
    logStorage = [];
    
    // Add a log entry about the clearing
    addLogEntry('info', 'system', 'Logs cleared by admin');

    return NextResponse.json({
      success: true,
      message: 'Logs cleared successfully'
    });

  } catch (error) {
    console.error('Logs clear error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to clear logs: ${error.message}`
    }, { status: 500 });
  }
}

// Export the addLogEntry function for use in other parts of the application
// export { addLogEntry }; 