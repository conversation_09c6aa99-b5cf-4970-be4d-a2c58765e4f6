#!/bin/bash

# NetStream Docker Test Script
# Tests the complete Docker setup including API key management

set -e

echo "🐳 NetStream Docker Test Suite"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found! Please create it with your MongoDB URI and other settings."
    exit 1
fi

print_status ".env file found"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running! Please start Docker first."
    exit 1
fi

print_status "Docker is running"

# Stop any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down -v > /dev/null 2>&1 || true

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check if services are running
echo "🔍 Checking service status..."

# Check Redis
if docker-compose ps redis | grep -q "Up"; then
    print_status "Redis is running"
else
    print_error "Redis is not running"
    docker-compose logs redis
    exit 1
fi

# Check Backend
if docker-compose ps backend | grep -q "Up"; then
    print_status "Backend is running"
else
    print_error "Backend is not running"
    docker-compose logs backend
    exit 1
fi

# Check Frontend
if docker-compose ps frontend | grep -q "Up"; then
    print_status "Frontend is running"
else
    print_error "Frontend is not running"
    docker-compose logs frontend
    exit 1
fi

# Test health endpoints
echo "🏥 Testing health endpoints..."

# Test backend health
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    print_status "Backend health check passed"
else
    print_warning "Backend health check failed"
fi

# Test frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    print_status "Frontend is accessible"
else
    print_warning "Frontend is not accessible"
fi

# Test API key management
echo "🔑 Testing API key management..."
if command -v node > /dev/null 2>&1; then
    if [ -f scripts/test-api-keys-docker.js ]; then
        BACKEND_URL=http://localhost:3001 node scripts/test-api-keys-docker.js
        if [ $? -eq 0 ]; then
            print_status "API key management test passed"
        else
            print_warning "API key management test failed"
        fi
    else
        print_warning "API key test script not found"
    fi
else
    print_warning "Node.js not available for API key testing"
fi

# Show service URLs
echo ""
echo "🌐 Service URLs:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:3001"
echo "   GraphQL:  http://localhost:3001/graphql"
echo "   Admin:    http://localhost:3000/admin"
echo ""

# Show logs command
echo "📋 To view logs:"
echo "   docker-compose logs -f"
echo ""

# Show stop command
echo "🛑 To stop services:"
echo "   docker-compose down"
echo ""

print_status "Docker test completed successfully!"
echo "🎉 NetStream is ready to use!"
