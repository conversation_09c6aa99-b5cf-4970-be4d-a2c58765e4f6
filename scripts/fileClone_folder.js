// OmniDev reporting for duty.

// Purpose: Reads JSON files, extracts OneUpload file codes, CLONES files into the user's
// account, ORGANIZING them into folders. If a CLONE fails with "404 - No file",
// it re-runs the URL extraction logic for the ORIGINAL intermediate URL
// and attempts the clone AGAIN with the potentially updated source file code.
// Supports continuing previous runs or forcing a full overwrite/re-clone. Includes rate limiting.
// *CORRECTED SYNTAX & INTERNAL FUNCTION*.

// Usage:
//   node scripts/cloneAndOrganize_withRetry.js [options]

// ==================================================================================
//                              DEPENDENCIES
// ==================================================================================
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
const https = require('https'); // Needed for fetchWithSSLBypass
const { JSDOM } = require('jsdom'); // Needed for extraction logic
const dotenv = require('dotenv');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const logger = require('../src/utils/logger.js'); // Adjust path if needed

// ==================================================================================
//                              CONFIGURATION
// ==================================================================================
dotenv.config({ path: path.resolve(__dirname, '../.env') });
const { ONEUPLOAD_API_KEY } = process.env;

const API_BASE_URL = 'https://oneupload.to/api';
const CLONE_API_ENDPOINT = `${API_BASE_URL}/file/clone`;
const FOLDER_LIST_API_ENDPOINT = `${API_BASE_URL}/folder/list`;
const FOLDER_CREATE_API_ENDPOINT = `${API_BASE_URL}/folder/create`;

const STATE_FILE_NAME = 'cloned_organized_files.log';
const MOVIE_FOLDER_NAME = 'Movies';
const SERIES_FOLDER_NAME = 'Series';
const ANIME_FOLDER_NAME = 'Animes';

const RETRY_FAILURE_LOG_FILE = 'clone_retry_failures.log';

// --- Command Line Arguments ---
const argv = yargs(hideBin(process.argv))
    .option('Movie', { type: 'boolean', description: 'Process output_movies.json' })
    .option('Series', { type: 'boolean', description: 'Process output_series.json' })
    .option('Anime', { type: 'boolean', description: 'Process output_anime.json' })
    .option('inputDir', { type: 'string', default: './provider_url_output', description: 'Directory containing JSON input files' })
    .option('fldId', { type: 'number', description: 'TARGET Folder ID for ALL cloned files (OVERRIDES dynamic organization)' })
    .option('rootFldId', { type: 'number', default: 0, description: 'Organize content UNDER this root folder ID (0 = account root)' })
    .option('overwrite', { type: 'boolean', default: false, description: 'Overwrite state file & process all (ignores previous clones).' })
    .option('forceClone', { type: 'boolean', default: false, description: 'Clone files even if logged in state file (use with caution).' })
    .option('delay', { type: 'number', default: 1500, description: 'Delay (ms) between API actions.' })
    .option('retryFailedClone', { type: 'boolean', default: true, description: 'Attempt to re-extract URL and retry clone on 404 "No file" error.'})
    .help().alias('h', 'help')
    .argv;

// Determine processing parameters
const processAll = !argv.Movie && !argv.Series && !argv.Anime;
const collectionsToProcess = [];
const baseInputDir = path.resolve(argv.inputDir);
const stateFilePath = path.join(baseInputDir, STATE_FILE_NAME);
const retryFailureLogPath = path.join(baseInputDir, RETRY_FAILURE_LOG_FILE);

if (processAll || argv.Movie) collectionsToProcess.push('Movie');
if (processAll || argv.Series) collectionsToProcess.push('Series');
if (processAll || argv.Anime) collectionsToProcess.push('Anime');

const TARGET_FOLDER_ID_OVERRIDE = argv.fldId;
const ROOT_FOLDER_ID = argv.rootFldId;
const IS_OVERWRITE_MODE = argv.overwrite;
const FORCE_CLONE = argv.forceClone;
const DELAY_BETWEEN_ACTIONS_MS = argv.delay;
const RETRY_FAILED_CLONE = argv.retryFailedClone;

const RATE_LIMIT_STATUS = 403;
const RATE_LIMIT_RETRY_DELAY_MS = 30000;
const MAX_RATE_LIMIT_RETRIES = 3;

// ==================================================================================
//                              DATABASE MODELS & CONFIG
// ==================================================================================
// No DB models needed for cloning script
const ALL_MODEL_CONFIG = {
    'Movie': { type: 'media', filename: 'output_movies.json', topLevelFolder: MOVIE_FOLDER_NAME },
    'Series': { type: 'series', filename: 'output_series.json', topLevelFolder: SERIES_FOLDER_NAME },
    'Anime': { type: 'anime', filename: 'output_anime.json', topLevelFolder: ANIME_FOLDER_NAME },
};

const ACTIVE_FILES_CONFIG = collectionsToProcess.map(name => {
    const config = ALL_MODEL_CONFIG[name];
    if (!config) return null;
    const jsonFile = path.join(baseInputDir, config.filename);
    return { name, ...config, jsonFile };
}).filter(Boolean);

// ==================================================================================
//                              GLOBAL CACHE
// ==================================================================================
const folderCache = {};

// ==================================================================================
//                              UTILITY FUNCTIONS (Including Extraction Logic)
// ==================================================================================
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const ensureDirectoryExists = (dirPath) => { if (!fs.existsSync(dirPath)) { logger.info(`Directory '${dirPath}' does not exist. Creating...`); try { fs.mkdirSync(dirPath, { recursive: true }); logger.info(`Directory created: ${dirPath}`); } catch (error) { logger.error(`Failed to create directory '${dirPath}': ${error.message}`); process.exit(1); } } else { logger.debug(`Directory already exists: ${dirPath}`); } };
const initializeStateFile = (filePath) => { logger.info(`Initializing/Clearing state file: ${filePath}`); try { fs.writeFileSync(filePath, '', 'utf-8'); logger.info(`Initialized/Cleared ${filePath}`); } catch (error) { logger.error(`Error initializing state file ${filePath}: ${error.message}`); } };
const loadClonedState = (filePath) => { const clonedCodes = new Set(); if (!fs.existsSync(filePath)) { logger.info(`State file ${filePath} not found.`); return clonedCodes; } try { const lines = fs.readFileSync(filePath, 'utf-8').split('\n'); for (const line of lines) { const code = line.trim(); if (code) { clonedCodes.add(code); } } logger.info(`Loaded ${clonedCodes.size} previously cloned file codes from ${filePath}`); } catch (error) { logger.error(`Error reading state file ${filePath}: ${error.message}`); } return clonedCodes; };
const appendClonedState = (filePath, originalFileCode) => { if (!originalFileCode) return; logger.debug(`Appending ${originalFileCode} to state file ${filePath}`); try { fs.appendFileSync(filePath, `${originalFileCode}\n`, 'utf-8'); } catch (error) { logger.error(`Error appending to state file ${filePath}: ${error.message}`); } };
const extractFileCode = (url) => { if (!url) return null; try { const urlObj = new URL(url); if (!urlObj.hostname.includes('oneupload.to')) { logger.warn(`URL ${url} does not seem to be a OneUpload URL.`); return null; } const pathParts = urlObj.pathname.split('/').filter(part => part !== ''); const lastPart = pathParts[pathParts.length - 1]; if (lastPart) { return lastPart.startsWith('embed-') ? lastPart.substring(6) : lastPart; } } catch (e) { logger.error(`Error parsing URL ${url} to extract file code: ${e.message}`); } return null; };
const sanitizeFolderName = (name) => { if (!name) return 'Untitled'; let sanitized = name.replace(/[<>:"/\\|?*]+/g, ''); sanitized = sanitized.replace(/\s+/g, ' ').replace(/\.+/g, '.').trim(); return sanitized || 'Untitled'; };

// --- Copied/Adapted Extraction Logic ---
const fetchWithSSLBypass = async (url, options = {}) => { const httpsAgent = new https.Agent({ rejectUnauthorized: false }); const headers = { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 'Referer': options.headers?.Referer || url, 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 'Accept-Language': 'en-US,en;q=0.5', ...options.headers, }; const timeout = options.timeout || 25000; try { return await fetch(url, { method: options.method || 'GET', headers: headers, agent: url.startsWith('https') ? httpsAgent : undefined, redirect: options.redirect || 'follow', timeout: timeout, }); } catch (error) { if (error.name === 'FetchError' || error.code === 'ETIMEOUT' || error.type === 'request-timeout') { logger.warn(`[RetryExtractor] Network error during fetch for ${url}: ${error.message}`); } else { logger.error(`[RetryExtractor] Unexpected error during fetch setup for ${url}: ${error.message}`); } throw error; } };
function potentialUrlLooksLikeEmbed(url) { if (!url) return false; return url.match(/(\/embed-?|\/e\/|\/v\/|video|watch)/i) || url.match(/\/[a-zA-Z0-9_-]{8,}$/); }
function cleanProviderUrl(url) { if (!url) return url; try { const urlObj = new URL(url); if (urlObj.hostname.endsWith('oneupload.to') && urlObj.pathname.startsWith('/embed-')) { const newPathname = urlObj.pathname.replace('/embed-', '/'); const cleanedUrl = `${urlObj.protocol}//${urlObj.host}${newPathname}${urlObj.search}${urlObj.hash}`; logger.debug(`[RetryExtractor] Cleaned URL: ${cleanedUrl}`); return cleanedUrl; } } catch (e) { logger.warn(`[RetryExtractor] Could not parse URL for cleaning: ${url}. Error: ${e.message}`); return url; } return url; }

// Use the full extraction logic, adapted slightly for retry context logging
async function extractVideoProviderUrl_Internal(initialUrl) {
    logger.info(`[RetryExtractor] Re-extracting from: ${initialUrl}`);
    if (!initialUrl || typeof initialUrl !== 'string' || (!initialUrl.startsWith('http://') && !initialUrl.startsWith('https://'))) {
        const error = new Error(`Invalid or non-HTTP(S) URL provided: ${initialUrl}`);
        logger.error(`[RetryExtractor] ${error.message}`);
        throw error;
    }
    let finalUrl = initialUrl;
    let providerEmbedUrl = null;
    try {
        const response = await fetchWithSSLBypass(initialUrl, { redirect: 'follow' });
        finalUrl = response.url;
        if (!response.ok) {
            logger.warn(`[RetryExtractor] Fetch failed: ${response.status} for ${initialUrl} -> ${finalUrl}`);
            return null; // Indicate failure to find
        }
        const html = await response.text();
        if (!html) {
            logger.warn(`[RetryExtractor] Empty body for ${initialUrl} -> ${finalUrl}`);
            return null;
        }
        const dom = new JSDOM(html, { url: finalUrl });
        const document = dom.window.document;

        // Strategy 1: Iframe
        const iframe = document.querySelector('iframe');
        if (iframe?.src) {
            try {
                let iframeSrc = new URL(iframe.src.trim(), finalUrl).toString();
                logger.debug(`[RetryExtractor] Found iframe source: ${iframeSrc}`);
                if (potentialUrlLooksLikeEmbed(iframeSrc)) {
                    providerEmbedUrl = iframeSrc;
                    logger.debug(`[RetryExtractor] Using iframe source as potential embed URL.`);
                }
            } catch (e) {
                logger.warn(`[RetryExtractor] Could not parse or resolve iframe src: ${iframe.src}. Error: ${e.message}`);
            }
        }

        // Strategy 2: JWPlayer (if not found yet)
        if (!providerEmbedUrl) {
             const scriptTags = document.querySelectorAll('script');
             const jwPlayerSetupRegex = /jwplayer\s*\([^)]+\)\s*\.\s*setup\s*\(\s*(\{[\s\S]*?\})\s*\)\s*;?/i;
             const imageUrlRegex = /image\s*:\s*["'](https?:\/\/[^"'\s]+)["']/i;
             const baseLinkRegexes = [ /aboutlink\s*:\s*["'](https?:\/\/[^"'\s]+)["']/i, /logo\s*:\s*\{[^}]*link\s*:\s*["'](https?:\/\/[^"'\s]+)["'][^}]*\}/i, /(?:file|src)\s*:\s*["'](https?:\/\/[^"'\s]+)["']/i ];

             for (const script of scriptTags) {
                 const scriptContent = script.textContent;
                 if (!scriptContent) continue;
                 const match = scriptContent.match(jwPlayerSetupRegex);
                 if (match?.[1]) {
                     const setupConfigString = match[1];
                     logger.debug(`[RetryExtractor] Found potential JWPlayer setup config.`);
                     const imageMatch = setupConfigString.match(imageUrlRegex);
                     let baseLinkMatch = null, sourcesFileUrl = null, baseLink = null;
                     for (const regex of baseLinkRegexes) {
                         baseLinkMatch = setupConfigString.match(regex);
                         if (baseLinkMatch?.[1]) { if (regex === baseLinkRegexes[2]) sourcesFileUrl = baseLinkMatch[1]; break; }
                     }
                     baseLink = baseLinkMatch?.[1];
                     if (!baseLink && sourcesFileUrl) {
                         logger.debug(`[RetryExtractor] Attempting base domain derivation from sources file: ${sourcesFileUrl}`);
                         try { const fileUrl = new URL(sourcesFileUrl); const hostnameParts = fileUrl.hostname.split('.'); if (hostnameParts.length >= 2) { const baseDomain = hostnameParts.slice(-2).join('.'); baseLink = `${fileUrl.protocol}//${baseDomain}/`; logger.debug(`[RetryExtractor] Using base domain derived from sources file: ${baseLink}`); } } catch (e) { logger.warn(`[RetryExtractor] Could not parse base domain from sources file URL: ${sourcesFileUrl}. Error: ${e.message}`); }
                     }
                     if (imageMatch?.[1] && baseLink) {
                         const imageUrl = imageMatch[1]; logger.debug(`[RetryExtractor] Found Image URL: ${imageUrl}`); logger.debug(`[RetryExtractor] Found Base Link: ${baseLink}`);
                         try { const imagePath = new URL(imageUrl).pathname; const videoId = path.parse(path.basename(imagePath)).name; if (videoId && videoId.length > 3) { const providerBase = baseLink.endsWith('/') ? baseLink : baseLink + '/'; providerEmbedUrl = providerBase + 'embed-' + videoId; logger.debug(`[RetryExtractor] Constructed provider embed URL via JWPlayer: ${providerEmbedUrl}`); break; } else { logger.warn(`[RetryExtractor] Could not extract valid video ID from image filename: ${path.basename(imagePath)}`); } } catch (e) { logger.error(`[RetryExtractor] Error parsing image URL or constructing final URL from JWPlayer: ${e.message}`); }
                     } else { logger.debug(`[RetryExtractor] Could not find sufficient info (image URL & base link/source) in JWPlayer config.`); }
                 }
             }
        }

        // Strategy 3: JS Redirects (if not found yet)
        if (!providerEmbedUrl) {
             const scriptTags = document.querySelectorAll('script');
             for (const script of scriptTags) {
                 const scriptContent = script.textContent; if (!scriptContent) continue; const redirectMatch = scriptContent.match(/window\.location\.(?:href|replace)\s*=\s*['"]([^'"]+)['"]/i);
                 if (redirectMatch?.[1]) { try { const absoluteRedirectUrl = new URL(redirectMatch[1], finalUrl).toString(); if (potentialUrlLooksLikeEmbed(absoluteRedirectUrl)) { logger.debug(`[RetryExtractor] Found potential embed URL in JS redirect: ${absoluteRedirectUrl}`); providerEmbedUrl = absoluteRedirectUrl; break; } } catch (e) { logger.warn(`[RetryExtractor] Could not parse or resolve JS redirect URL: ${redirectMatch[1]}. Error: ${e.message}`); } }
             }
        }

        // Strategy 4: Final URL (if not found yet)
        if (!providerEmbedUrl && finalUrl !== initialUrl) {
             logger.debug(`[RetryExtractor] Checking final URL after redirects: ${finalUrl}`);
             if (potentialUrlLooksLikeEmbed(finalUrl)) { logger.debug(`[RetryExtractor] Final URL itself looks like a valid embed URL.`); providerEmbedUrl = finalUrl; }
        }

        // Clean and Return
        if (providerEmbedUrl) {
            const cleanedUrl = cleanProviderUrl(providerEmbedUrl);
            logger.info(`    => Re-extracted URL: ${cleanedUrl}`);
            return cleanedUrl;
        } else {
            logger.warn(`[RetryExtractor] No provider URL found for: ${initialUrl}`);
            return null;
        }
    } catch (error) {
        logger.error(`[RetryExtractor] Error during re-extraction of ${initialUrl}: ${error.message}`);
        // Log specific errors if needed, but re-throw to be caught by the main retry logic
        throw error;
    }
    // No finally block needed here
}
// --- End Copied Extraction Logic ---


/** Logs clone retry failures */
const logRetryFailure = (logFilePath, error, contextData) => { try { const logEntry = { retryTimestamp: new Date().toISOString(), originalContext: contextData, retryError: { message: error.message, name: error.name, }, }; fs.appendFileSync(logFilePath, JSON.stringify(logEntry) + '\n', 'utf-8'); logger.warn(`Logged clone retry failure to ${logFilePath}. Code: ${contextData?.fileCodeToClone || 'N/A'}`); } catch (logError) { logger.error(`FATAL: Could not write to retry failure log file ${logFilePath}: ${logError.message}`); } };

// ==================================================================================
//                              API HELPER FUNCTIONS (with Rate Limit Handling)
// ==================================================================================
async function makeApiCall(url, description, retries = 0) { /* ... (unchanged) ... */ logger.debug(`API Call (Try ${retries + 1}/${MAX_RATE_LIMIT_RETRIES + 1}): ${description} - URL: ${url}`); try { const response = await fetch(url, { method: 'GET' }); if (response.status === RATE_LIMIT_STATUS && retries < MAX_RATE_LIMIT_RETRIES) { logger.warn(`!!! RATE LIMIT HIT for ${description}. Waiting ${RATE_LIMIT_RETRY_DELAY_MS}ms before retry...`); await sleep(RATE_LIMIT_RETRY_DELAY_MS); return await makeApiCall(url, description, retries + 1); } if (!response.ok) { let errorMsg = `HTTP ${response.status} ${response.statusText}`; try { const body = await response.json(); errorMsg = `API Error (${response.status}): ${body.msg || errorMsg}`; } catch (e) { /* ignore */ } logger.error(`API Call Failed: ${description} - ${errorMsg}`); return { success: false, error: errorMsg, data: null, status: response.status }; } const data = await response.json(); logger.debug(`API Success: ${description} - Response: ${JSON.stringify(data)}`); if (data.status !== 200) { logger.warn(`API Call Warning: ${description} - Status ${data.status}, Msg: ${data.msg}`); if (data.msg?.toLowerCase().includes('limit reached') && retries < MAX_RATE_LIMIT_RETRIES) { logger.warn(`!!! RATE LIMIT indicated by API msg for ${description}. Waiting ${RATE_LIMIT_RETRY_DELAY_MS}ms before retry...`); await sleep(RATE_LIMIT_RETRY_DELAY_MS); return await makeApiCall(url, description, retries + 1); } return { success: false, error: `API Status ${data.status}: ${data.msg || 'Unknown API error'}`, data: data, status: data.status }; } return { success: true, error: null, data: data.result, status: 200 }; } catch (error) { logger.error(`Network/Fetch Error on API Call (${description}): ${error.message}`); return { success: false, error: error.message, data: null, status: null }; } }
async function listFolderApiCall(apiKey, parentFolderId) { const params = new URLSearchParams({ key: apiKey, fld_id: parentFolderId }); const url = `${FOLDER_LIST_API_ENDPOINT}?${params.toString()}`; return await makeApiCall(url, `List Folder (ID: ${parentFolderId})`); }
async function createFolderApiCall(apiKey, folderName, parentFolderId) { const params = new URLSearchParams({ key: apiKey, name: folderName, parent_id: parentFolderId }); const url = `${FOLDER_CREATE_API_ENDPOINT}?${params.toString()}`; return await makeApiCall(url, `Create Folder "${folderName}" (Parent ID: ${parentFolderId})`); }
async function cloneFileApiCall(apiKey, fileCodeToClone, targetFolderId) { const params = new URLSearchParams({ key: apiKey, file_code: fileCodeToClone }); if (typeof targetFolderId === 'number' && !isNaN(targetFolderId)) { params.append('fld_id', targetFolderId.toString()); } else { logger.warn(`Invalid or missing targetFolderId (${targetFolderId}) for ${fileCodeToClone}. Cloning to root.`); } const url = `${CLONE_API_ENDPOINT}?${params.toString()}`; return await makeApiCall(url, `Clone File "${fileCodeToClone}" into FldID ${targetFolderId ?? 'Root'}`); }

// ==================================================================================
//                      FOLDER ORGANIZATION LOGIC (with Delay)
// ==================================================================================
async function getOrCreateFolderId(folderName, parentFolderId, cache, apiKey) { /* ... (unchanged) ... */ const sanitizedName = sanitizeFolderName(folderName); const lowerCaseName = sanitizedName.toLowerCase(); const cacheKey = `${parentFolderId}_${lowerCaseName}`; if (cache[cacheKey]) { logger.debug(`Cache hit for folder "${sanitizedName}" (Parent: ${parentFolderId}): ID ${cache[cacheKey]}`); return cache[cacheKey]; } logger.info(`Checking for folder "${sanitizedName}" in parent folder ID: ${parentFolderId}`); logger.debug(`Waiting ${DELAY_BETWEEN_ACTIONS_MS}ms before folder list...`); await sleep(DELAY_BETWEEN_ACTIONS_MS); const listResult = await listFolderApiCall(apiKey, parentFolderId); if (listResult.success && listResult.data?.folders) { const existingFolder = listResult.data.folders.find(f => f.name?.toLowerCase() === lowerCaseName); if (existingFolder && typeof existingFolder.fld_id === 'string') { const folderId = parseInt(existingFolder.fld_id, 10); if (!isNaN(folderId)) { logger.info(` -> Found existing folder "${existingFolder.name}" with ID: ${folderId}`); cache[cacheKey] = folderId; return folderId; } else { logger.warn(` -> Found existing folder "${existingFolder.name}" but its ID "${existingFolder.fld_id}" is not valid.`); } } } else if (!listResult.success) { logger.error(` -> Failed to list folders in parent ${parentFolderId}. Error: ${listResult.error}`); return null; } logger.info(` -> Folder "${sanitizedName}" not found or ID invalid. Creating...`); logger.debug(`Waiting ${DELAY_BETWEEN_ACTIONS_MS}ms before folder create...`); await sleep(DELAY_BETWEEN_ACTIONS_MS); const createResult = await createFolderApiCall(apiKey, sanitizedName, parentFolderId); if (createResult.success && createResult.data?.fld_id && typeof createResult.data.fld_id === 'string') { const newFolderId = parseInt(createResult.data.fld_id, 10); if (!isNaN(newFolderId)) { logger.info(` -> Successfully created folder "${sanitizedName}" with ID: ${newFolderId}`); cache[cacheKey] = newFolderId; return newFolderId; } else { logger.error(` -> Created folder "${sanitizedName}" but received invalid ID "${createResult.data.fld_id}".`); return null; } } else { logger.error(` -> Failed to create folder "${sanitizedName}". Error: ${createResult.error || 'Unknown error'}`); return null; } }
async function getOrCreateFolderIdByPath(pathElements, rootFolderId, cache, apiKey) { /* ... (unchanged) ... */ let currentParentId = rootFolderId; for (const elementName of pathElements) { if (!elementName) continue; const folderId = await getOrCreateFolderId(elementName, currentParentId, cache, apiKey); if (folderId === null || typeof folderId !== 'number') { logger.error(`Failed path element "${elementName}". Aborting path.`); return null; } currentParentId = folderId; } return (typeof currentParentId === 'number' && !isNaN(currentParentId)) ? currentParentId : null; }

// ==================================================================================
//                              CORE PROCESSING LOGIC (with Clone Retry)
// ==================================================================================
async function processJsonFile(config, alreadyClonedSet) {
    const { name, jsonFile, type, topLevelFolder } = config;
    logger.info(`\n--- Processing JSON file for ${name}: ${jsonFile} ---`);
    logger.info(`Top Level Folder: ${topLevelFolder}`);

    if (!fs.existsSync(jsonFile)) { logger.warn(`Input file not found: ${jsonFile}. Skipping.`); return; }

    let dataArray = [];
    try { const fileContent = fs.readFileSync(jsonFile, 'utf-8'); if (fileContent.trim() === '') { logger.warn(`Input file ${jsonFile} is empty.`); return; } dataArray = JSON.parse(fileContent); if (!Array.isArray(dataArray)) { logger.error(`Content of ${jsonFile} not array.`); return; } logger.info(`Found ${dataArray.length} entries in ${jsonFile}`); }
    catch (error) { logger.error(`Error reading JSON ${jsonFile}: ${error.message}`); return; }

    let successCount = 0, skippedCount = 0, errorCount = 0, folderErrorCount = 0, retrySuccessCount = 0, retryErrorCount = 0;
    let fileNeedsSaving = false; // Flag if dataArray is modified

    for (let i = 0; i < dataArray.length; i++) {
        const item = dataArray[i];
        const originalUrl = item?.processedUrl?.originalUrl;
        let extractedUrl = item?.processedUrl?.extractedProviderUrl; // Use let
        const documentTitle = item?.documentTitle;
        const season = item?.episode?.season;

        logger.debug(`Processing item ${i + 1}/${dataArray.length}: DocID=${item?.documentId}, Title=${documentTitle}, ExtractedURL=${extractedUrl}`);
        if (!extractedUrl || !documentTitle || !originalUrl) { logger.debug(` -> Skip: missing URL/Title.`); continue; }

        let fileCodeToClone = extractFileCode(extractedUrl);
        logger.debug(` -> Initial file code: ${fileCodeToClone}`);
        if (!fileCodeToClone) { logger.warn(` -> Skip: bad initial code.`); continue; }

        const isAlreadyCloned = alreadyClonedSet.has(fileCodeToClone);
        logger.debug(` -> Cloned check (${fileCodeToClone}): ${isAlreadyCloned}. Force: ${FORCE_CLONE}`);
        if (!FORCE_CLONE && isAlreadyCloned) { logger.debug(` -> Skip: already cloned.`); skippedCount++; continue; }

        let targetFolderId = TARGET_FOLDER_ID_OVERRIDE; let determinedFolderId = null;
        if (typeof targetFolderId !== 'number') {
            let pathElements = []; if (!documentTitle) { logger.warn(` -> Skip: missing title for folder path.`); folderErrorCount++; continue; }
            if (type === 'media') pathElements = [topLevelFolder, documentTitle]; else if (type === 'series' || type === 'anime') { pathElements = [topLevelFolder, documentTitle]; if (season) pathElements.push(`Season ${season.toString().padStart(2, '0')}`); }
            if (pathElements.length > 0) { logger.info(` -> Determining folder path: ${pathElements.join(' / ')} (Root: ${ROOT_FOLDER_ID})`); determinedFolderId = await getOrCreateFolderIdByPath(pathElements, ROOT_FOLDER_ID, folderCache, ONEUPLOAD_API_KEY); if (determinedFolderId === null) { logger.error(` -> Skip: folder error.`); folderErrorCount++; continue; } logger.info(` -> Target Folder ID: ${determinedFolderId}`); }
            else { determinedFolderId = ROOT_FOLDER_ID; logger.info(` -> Target root folder: ${determinedFolderId}`); }
            targetFolderId = determinedFolderId;
        } else { logger.info(` -> Using override target folder: ${targetFolderId}`); }
        if (typeof targetFolderId !== 'number' || isNaN(targetFolderId)) { logger.error(` -> Skip: invalid target folder ID (${targetFolderId}).`); folderErrorCount++; continue; }

        logger.info(`>>> PREPARING TO CLONE (Attempt 1): Code='${fileCodeToClone}', TargetFldID='${targetFolderId}'`);
        logger.debug(`Waiting ${DELAY_BETWEEN_ACTIONS_MS}ms before clone...`); await sleep(DELAY_BETWEEN_ACTIONS_MS);
        let cloneResult = await cloneFileApiCall(ONEUPLOAD_API_KEY, fileCodeToClone, targetFolderId);

        // --- Retry Logic for "404 - No file" ---
        if (!cloneResult.success && cloneResult.status === 404 && cloneResult.error?.includes('No file') && RETRY_FAILED_CLONE) {
            logger.warn(`!!! CLONE FAILED (Attempt 1) for Code='${fileCodeToClone}' - 404 No file. Attempting re-extraction and retry...`);
            errorCount++; // Count initial failure
            const retryContext = { documentId: item?.documentId, originalIntermediateUrl: originalUrl, initialFileCode: fileCodeToClone };
            try {
                logger.debug(`Waiting ${DELAY_BETWEEN_ACTIONS_MS}ms before re-extraction...`); // Use action delay
                await sleep(DELAY_BETWEEN_ACTIONS_MS);
                const newExtractedUrl = await extractVideoProviderUrl_Internal(originalUrl); // Re-run extraction
                if (newExtractedUrl) {
                    const newFileCode = extractFileCode(newExtractedUrl); logger.debug(` -> Re-extracted code: ${newFileCode}`);
                    if (newFileCode && newFileCode !== fileCodeToClone) {
                        logger.info(` -> Re-extraction successful. New code: ${newFileCode}. Retrying clone...`);
                        logger.info(`>>> PREPARING TO CLONE (Attempt 2): Code='${newFileCode}', TargetFldID='${targetFolderId}'`);
                        logger.debug(`Waiting ${DELAY_BETWEEN_ACTIONS_MS}ms before retry clone...`); await sleep(DELAY_BETWEEN_ACTIONS_MS);
                        cloneResult = await cloneFileApiCall(ONEUPLOAD_API_KEY, newFileCode, targetFolderId); // Use new code
                        if (cloneResult.success) {
                            logger.info(`    -> RETRY SUCCESSFUL! Cloned with new code ${newFileCode}.`);
                            retrySuccessCount++;
                            // Update the JSON data *in memory*
                            dataArray[i].processedUrl.extractedProviderUrl = newExtractedUrl; // Update URL
                            dataArray[i].cloneRetry = { success: true, newFileCode: newFileCode, timestamp: new Date().toISOString() };
                            fileNeedsSaving = true; // Mark file for saving
                            if (!FORCE_CLONE) { appendClonedState(stateFilePath, newFileCode); alreadyClonedSet.add(newFileCode); } // Log NEW code
                        } else {
                            logger.warn(`!!! CLONE FAILED (Attempt 2) New Code='${newFileCode}'. Error: ${cloneResult.error}`);
                            retryErrorCount++; logRetryFailure(retryFailureLogPath, new Error(cloneResult.error || 'Clone failed after re-extraction'), { ...retryContext, newFileCodeAttempted: newFileCode });
                            dataArray[i].cloneRetry = { success: false, newFileCodeAttempted: newFileCode, error: cloneResult.error, timestamp: new Date().toISOString() }; fileNeedsSaving = true;
                        }
                    } else if (newFileCode && newFileCode === fileCodeToClone) { logger.warn(` -> Re-extraction yielded same code (${newFileCode}). Skip retry.`); retryErrorCount++; logRetryFailure(retryFailureLogPath, new Error('Re-extraction yielded same failing code'), retryContext); dataArray[i].cloneRetry = { success: false, error: 'Re-extraction yielded same failing code', timestamp: new Date().toISOString() }; fileNeedsSaving = true; }
                    else { logger.warn(` -> Re-extraction failed to get new code from URL: ${newExtractedUrl}.`); retryErrorCount++; logRetryFailure(retryFailureLogPath, new Error('Failed to extract code after re-extraction'), { ...retryContext, newExtractedUrl: newExtractedUrl }); dataArray[i].cloneRetry = { success: false, error: 'Failed to extract code after re-extraction', timestamp: new Date().toISOString() }; fileNeedsSaving = true; }
                } else { logger.warn(` -> Re-extraction failed for ${originalUrl}. Cannot retry.`); retryErrorCount++; logRetryFailure(retryFailureLogPath, new Error('Re-extraction returned null'), retryContext); dataArray[i].cloneRetry = { success: false, error: 'Re-extraction returned null', timestamp: new Date().toISOString() }; fileNeedsSaving = true; }
            } catch (retryExtractionError) { logger.error(` -> Error during re-extraction process for ${originalUrl}: ${retryExtractionError.message}`); retryErrorCount++; logRetryFailure(retryFailureLogPath, retryExtractionError, retryContext); dataArray[i].cloneRetry = { success: false, error: `Re-extraction exception: ${retryExtractionError.message}`, timestamp: new Date().toISOString() }; fileNeedsSaving = true; }
        } // End Retry Logic
        else if (cloneResult.success) { successCount++; if (!FORCE_CLONE) { appendClonedState(stateFilePath, fileCodeToClone); alreadyClonedSet.add(fileCodeToClone); } }
        else { errorCount++; logger.warn(`!!! CLONE FAILED (Attempt 1) for Code='${fileCodeToClone}'. Error: ${cloneResult.error}`); }
    } // End loop

    if (fileNeedsSaving) { logger.info(`Attempting to save updated data back to ${jsonFile}`); writeJsonFile(jsonFile, dataArray); }
    else { logger.info(`No data changes were made in memory for ${jsonFile}, skipping write.`); }

    logger.info(`--- Finished processing ${name} ---`); logger.info(`Summary: Initial Clones: ${successCount}, Skipped: ${skippedCount}, Initial Errors: ${errorCount}, Folder Errors: ${folderErrorCount}, Retry Success: ${retrySuccessCount}, Retry Errors: ${retryErrorCount}`);
}

// ==================================================================================
//                                  MAIN EXECUTION
// ==================================================================================
async function main() {
    console.log("==========================================================");
    console.log("      Starting OneUpload File Cloning & Organizing Script ");
    console.log("                 (with Clone Retry Logic)                 ");
    console.log("==========================================================");

    if (!ONEUPLOAD_API_KEY) { logger.error("ONEUPLOAD_API_KEY not found."); process.exit(1); }
    if (ACTIVE_FILES_CONFIG.length === 0) { logger.warn("No JSON input files specified."); return; }

    const mode = IS_OVERWRITE_MODE ? "OVERWRITE (State File Cleared)" : (FORCE_CLONE ? "FORCE CLONE (State File Ignored)" : "STANDARD (Checking State)");
    logger.info(`RUNNING IN ${mode} MODE`);
    logger.info(`Processing JSON files for: [${ACTIVE_FILES_CONFIG.map(c => c.name).join(', ')}]`);
    logger.info(`Input/Output directory: ${baseInputDir}`);
    logger.info(`State file: ${stateFilePath}`);
    logger.info(`Clone Retry Failure Log: ${retryFailureLogPath}`);
    logger.info(`Delay between API actions: ${DELAY_BETWEEN_ACTIONS_MS}ms`);
    logger.info(`Retry Clone on 404: ${RETRY_FAILED_CLONE}`);
    if (TARGET_FOLDER_ID_OVERRIDE !== undefined) { logger.info(`Target Folder ID Override: ${TARGET_FOLDER_ID_OVERRIDE}`); }
    else { logger.info(`Root Folder ID for Organization: ${ROOT_FOLDER_ID}`); logger.info(`Top-Level Folders: Movies='${MOVIE_FOLDER_NAME}', Series='${SERIES_FOLDER_NAME}', Anime='${ANIME_FOLDER_NAME}'`); }

    ensureDirectoryExists(baseInputDir);
    // Optional: Clear retry failure log at start
    // fs.writeFileSync(retryFailureLogPath, '', 'utf-8'); logger.info(`Cleared retry failure log: ${retryFailureLogPath}`);

    let alreadyClonedSet = new Set();
    if (IS_OVERWRITE_MODE) { logger.info("Overwrite mode: Clearing state file."); initializeStateFile(stateFilePath); }
    else { alreadyClonedSet = loadClonedState(stateFilePath); if (FORCE_CLONE) { logger.info("Force clone: State loaded but ignored."); } }

    for (const config of ACTIVE_FILES_CONFIG) { await processJsonFile(config, alreadyClonedSet); }

    logger.info("\n--- All specified JSON files processed ---");

    console.log("==========================================================");
    console.log("                     Script Finished                      ");
    console.log("==========================================================");
}

// Run the main function
main().catch(error => { logger.error(`Unhandled error in main execution: ${error.message}`); logger.error(error.stack); process.exit(1); });