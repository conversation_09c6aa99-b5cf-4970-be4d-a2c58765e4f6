// src/workers/scrapeWorker.js - MODIFIED FOR PROTOTYPE TO CLOSE BROWSER
const Bull = require('bull');
const axios = require('axios');
const { scrapeAll, SCRAPE_MODE } = require('../scrapers/services/scrapeService'); // Adjust path if needed
const { SCRAPE_INTERVAL } = require('../config/constants'); // Adjust path
const configService = require('../services/configService'); // Use config service instead
const logger = require('../utils/logger'); // Adjust path
const TrendingItem = require('../db/models/TrendingItem'); // Adjust path
const { closeBrowser } = require('../utils/browserUtils'); // << IMPORT BROWSER UTILS

// ... (Keep Redis config, queue setup) ...
const redisHost = process.env.REDIS_HOST || 'localhost';
const redisPort = process.env.REDIS_PORT || 6379;
const redisPassword = process.env.REDIS_PASSWORD || undefined;

const redisConfig = {
    host: redisHost,
    port: redisPort,
    ...(redisPassword && { password: redisPassword })
};

const scrapeQueue = new Bull('scrape-queue', { redis: redisConfig });
const trendingQueue = new Bull('trending-updates', { redis: redisConfig });


// --- Regular Scrape Job Processing ---
async function processScrapeJob(job) {
    // Scheduled scrapes default to LATEST mode
    const modeToRun = SCRAPE_MODE.LATEST;
    logger.info(`Scrape worker processing REGULAR job in '${modeToRun}' mode...`, { jobId: job.id });
    try {
        // scrapeAll in the prototype might call the scripts directly
        // Ensure scrapeAll (or the scripts it calls) handle Puppeteer internally now
        await scrapeAll(modeToRun); // Make sure this function calls the updated scripts
        logger.info(`Scrape worker finished REGULAR job successfully.`, { jobId: job.id });
    } catch (error) {
         logger.error(`Scrape worker REGULAR job failed: ${error.message}`, { jobId: job.id, stack: error.stack });
         throw error;
    } finally {
        // Attempt to close browser after each scrape job in case it was left open
        await closeBrowser().catch(err => logger.error('[Scrape Worker] Error closing browser after job:', err));
    }
}

// --- Trending Data Update Job Processing ---
async function updateTrendingData() {
    logger.info('Starting TMDb trending data update job...');

    const tmdbApiKey = await configService.getTmdbApiKey();
    if (!tmdbApiKey) {
        logger.error('TMDb API Key missing, cannot update trending data.');
        return;
    }

    const mediaTypes = ['movie', 'tv'];
    const timeWindow = 'day';
    let totalAdded = 0;

    try {
        // First, create a new batch of trending items
        for (const mediaType of mediaTypes) {
            const url = `https://api.themoviedb.org/3/trending/${mediaType}/${timeWindow}?api_key=${tmdbApiKey}&language=fr-FR`;
            
            try {
                const response = await axios.get(url, { 
                    timeout: 15000,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                const results = response.data?.results || [];
                
                if (results.length > 0) {
                    const itemsToInsert = results
                        .filter(item => item.id && typeof item.id === 'number')
                        .map((item, index) => ({
                            tmdbId: item.id,
                            mediaType: mediaType,
                            rank: index,
                            source: 'tmdb',
                            fetchedAt: new Date()
                        }));

                    if (itemsToInsert.length > 0) {
                        // Delete old items for this media type before inserting new ones
                        await TrendingItem.deleteMany({ 
                            source: 'tmdb', 
                            mediaType: mediaType 
                        });

                        // Insert new items
                        await TrendingItem.insertMany(itemsToInsert, { 
                            ordered: false,
                            timeout: 30000 // 30 second timeout for bulk operation
                        });

                        logger.info(`Inserted ${itemsToInsert.length} trending ${mediaType} items.`);
                        totalAdded += itemsToInsert.length;
                    }
                } else {
                    logger.warn(`No trending results received for ${mediaType} from TMDb.`);
                }

                // Add delay between requests to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 1000));

            } catch (fetchError) {
                logger.error(`Error fetching trending ${mediaType} from TMDb: ${fetchError.message}`, { 
                    status: fetchError.response?.status,
                    data: fetchError.response?.data
                });
            }
        }

        // Cleanup any items older than 24 hours
        const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        await TrendingItem.deleteMany({
            fetchedAt: { $lt: twentyFourHoursAgo }
        });

        logger.info(`Finished TMDb trending data update job. Total items added: ${totalAdded}`);
    } catch (error) {
        logger.error(`General error during trending data update: ${error.message}`, { 
            stack: error.stack 
        });
        throw error;
    }
}

// --- Worker Setup and Scheduling ---
async function setupWorkers() {
  try {
    // Connect DB logic might be elsewhere in prototype (e.g., server.js)
    // Ensure DB is connected before processing starts

    // 1. Setup Regular Scrape Worker
    scrapeQueue.process(processScrapeJob);
    // ... (Keep repeatable job setup logic from prototype's worker) ...
    const scrapeRepeatableJobs = await scrapeQueue.getRepeatableJobs();
    for (const job of scrapeRepeatableJobs) { /* ... remove old ... */ }
    await scrapeQueue.add({}, { repeat: { every: SCRAPE_INTERVAL } }); // Use SCRAPE_INTERVAL from constants
    logger.info(`Regular scrape worker processor set up.`);

    // 2. Setup Trending Update Worker
    trendingQueue.process(updateTrendingData);
    // ... (Keep repeatable job setup logic from prototype's worker) ...
     const trendingRepeatableJobs = await trendingQueue.getRepeatableJobs();
     for (const job of trendingRepeatableJobs) { /* ... remove old ... */ }
     await trendingQueue.add({}, { repeat: { every: 3 * 60 * 60 * 1000 } }); // Example: 3 hours
    logger.info(`Trending update worker processor set up.`);

    // Optional initial trending update trigger
    setTimeout(() => { trendingQueue.add({}); }, 15000);

    // Event listeners
    [scrapeQueue, trendingQueue].forEach(queue => {
        queue.on('completed', (job) => { /* ... log ... */ });
        queue.on('failed', (job, err) => { /* ... log ... */ });
        queue.on('error', (error) => { /* ... log ... */ });
    });

  } catch (error) {
    logger.error(`Error setting up Bull workers: ${error.message}`, { stack: error.stack });
  }
}

// Export function to start workers if needed by prototype's main process
module.exports = {
  startWorkers: setupWorkers,
  scrapeQueue,
  trendingQueue,
  updateTrendingData // Export the updateTrendingData function
};

// Add graceful shutdown for the worker process if run standalone
async function gracefulShutdown() {
    logger.info('Shutting down prototype scrape worker...');
    try {
        await Promise.all([
            scrapeQueue.close(),
            trendingQueue.close(),
            closeBrowser() // <<< CLOSE BROWSER
        ]);
        // Disconnect DB if connection managed here
         if (require('mongoose').connection.readyState === 1) {
           await require('mongoose').disconnect();
           logger.info('MongoDB disconnected via worker shutdown.');
         }
        logger.info('Prototype worker shut down gracefully.');
        process.exit(0);
    } catch (error) {
        logger.error('Error during prototype worker shutdown:', error);
        process.exit(1);
    }
}

// Listen for shutdown signals if this file can be run directly
if (require.main === module) {
     // Ensure DB connection first if run standalone
     require('dotenv').config();
     const mongoose = require('mongoose');
     const { mongoUri } = require('../config/env'); // Corrected path
     mongoose.connect(mongoUri).then(() => {
         logger.info('Standalone worker connected to DB.');
         setupWorkers();
     }).catch(err => {
          logger.error('Standalone worker DB connection failed:', err);
          process.exit(1);
     });

    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);
}