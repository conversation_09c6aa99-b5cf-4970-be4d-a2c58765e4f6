// File: /home/<USER>/NetStream/db/services/dbService.js
const mongoose = require('mongoose');
const Movie = require('../models/Movie');
const Series = require('../models/Series');
const Anime = require('../models/Anime');
const LiveTV = require('../models/LiveTV');
const logger = require('../../utils/logger');

async function saveMovies(movies) {
  try {
    if (!movies || !movies.length) {
      logger.warn('No movies to save');
      return;
    }
    const upserted = await Promise.all(movies.map(async (movie) => {
      const query = movie.tmdb?.id ? { 'tmdb.id': movie.tmdb.id } : { detailUrlPath: movie.detailUrlPath };
      const result = await Movie.updateOne(
        query,
        { $set: { ...movie, updatedAt: new Date() } },
        { upsert: true }
      );
      return result;
    }));
    const newCount = upserted.filter(r => r.upsertedCount > 0).length;
    logger.info(`Upserted ${upserted.length} movies (${newCount} new)`);
  } catch (error) {
    logger.error(`Error saving movies: ${error.message}`);
  }
}

async function saveSeries(series) {
  try {
    if (!series || !series.length) {
      logger.warn('No series to save');
      return;
    }
    const upserted = await Promise.all(series.map(async (serie) => {
      const query = serie.tmdb?.id ? { 'tmdb.id': serie.tmdb.id } : { detailUrlPath: serie.detailUrlPath };
      
      // Check if we need to update tmdbSeasons
      let updateData = {
        ...serie,
        episodes: serie.episodes && serie.episodes.length ? serie.episodes : [],
        metadata: serie.metadata || {},
        updatedAt: new Date()
      };
      
      // If we have new season data, handle it properly
      if (serie.tmdbSeasons && serie.tmdbSeasons.length) {
        // Get existing document to check current seasons
        const existingSeries = await Series.findOne(query);
        
        if (existingSeries && existingSeries.tmdbSeasons && existingSeries.tmdbSeasons.length) {
          // Merge existing and new seasons, avoiding duplicates
          const existingSeasonNumbers = existingSeries.tmdbSeasons.map(s => s.season_number);
          const newSeasons = serie.tmdbSeasons.filter(s => !existingSeasonNumbers.includes(s.season_number));
          
          if (newSeasons.length > 0) {
            updateData.tmdbSeasons = [...existingSeries.tmdbSeasons, ...newSeasons];
            logger.info(`Adding ${newSeasons.length} new TMDB seasons to series ${serie.title}`);
          } else {
            updateData.tmdbSeasons = existingSeries.tmdbSeasons;
          }
        }
        // If no existing seasons, just use the new ones
      }
      
      const result = await Series.updateOne(
        query,
        { $set: updateData },
        { upsert: true }
      );
      return result;
    }));
    const newCount = upserted.filter(r => r.upsertedCount > 0).length;
    logger.info(`Upserted ${upserted.length} series (${newCount} new)`);
  } catch (error) {
    logger.error(`Error saving series: ${error.message}`);
  }
}

async function saveAnime(animes) {
  try {
    if (!animes || !animes.length) {
      logger.warn('No anime to save');
      return;
    }
    const upserted = await Promise.all(animes.map(async (anime) => {
      const query = anime.jikan?.mal_id ? { 'jikan.mal_id': anime.jikan.mal_id } : { detailUrlPath: anime.detailUrlPath };
      const result = await Anime.updateOne(
        query,
        { $set: { ...anime, updatedAt: new Date() } },
        { upsert: true }
      );
      return result;
    }));
    const newCount = upserted.filter(r => r.upsertedCount > 0).length;
    logger.info(`Upserted ${upserted.length} animes (${newCount} new)`);
  } catch (error) {
    logger.error(`Error saving anime: ${error.message}`);
  }
}

async function saveLiveTV(channels) {
  try {
    if (!channels || !channels.length) {
      logger.warn('No live TV channels to save');
      return;
    }
    const upserted = await Promise.all(channels.map(async (channel) => {
      const query = channel.tmdb?.id ? { 'tmdb.id': channel.tmdb.id } : { detailUrlPath: channel.detailUrlPath };
      const result = await LiveTV.updateOne(
        query,
        { $set: { ...channel, updatedAt: new Date() } },
        { upsert: true }
      );
      return result;
    }));
    const newCount = upserted.filter(r => r.upsertedCount > 0).length;
    logger.info(`Upserted ${upserted.length} channels (${newCount} new)`);
  } catch (error) {
    logger.error(`Error saving live TV: ${error.message}`);
  }
}

async function clearMovies() {
  try {
    const result = await Movie.deleteMany({});
    logger.info(`Deleted ${result.deletedCount} movies from NetStream.movies`);
  } catch (error) {
    logger.error(`Error deleting movies: ${error.message}`);
    throw error;
  }
}

async function clearSeries() {
  try {
    const result = await Series.deleteMany({});
    logger.info(`Deleted ${result.deletedCount} series from NetStream.series`);
  } catch (error) {
    logger.error(`Error deleting series: ${error.message}`);
    throw error;
  }
}

async function clearAnime() {
  try {
    const result = await Anime.deleteMany({});
    logger.info(`Deleted ${result.deletedCount} animes from NetStream.animes`);
  } catch (error) {
    logger.error(`Error deleting anime: ${error.message}`);
    throw error;
  }
}

async function clearLiveTV() {
  try {
    const result = await LiveTV.deleteMany({});
    logger.info(`Deleted ${result.deletedCount} channels from NetStream.livetv`);
  } catch (error) {
    logger.error(`Error deleting live TV: ${error.message}`);
    throw error;
  }
}

// Log model registration
logger.info('Registered models:', {
  Movie: Movie.collection.collectionName,
  Series: Series.collection.collectionName,
  Anime: Anime.collection.collectionName,
  LiveTV: LiveTV.collection.collectionName
});

module.exports = {
  saveMovies,
  saveSeries,
  saveAnime,
  saveLiveTV,
  clearMovies,
  clearSeries,
  clearAnime,
  clearLiveTV
};