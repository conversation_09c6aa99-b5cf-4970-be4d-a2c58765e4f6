import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/mongodb';

export async function GET() {
  try {
    const db = await connectToDatabase();
    
    // Get the latest scraping job
    const latestJob = await db.collection('scraping_jobs')
      .findOne({}, { sort: { startedAt: -1 } });

    // Get scraping statistics
    const totalJobs = await db.collection('scraping_jobs').countDocuments();
    const successfulJobs = await db.collection('scraping_jobs').countDocuments({ status: 'completed' });
    const failedJobs = await db.collection('scraping_jobs').countDocuments({ status: 'failed' });
    const runningJobs = await db.collection('scraping_jobs').countDocuments({ status: 'running' });

    // Calculate success rate
    const successRate = totalJobs > 0 ? Math.round((successfulJobs / totalJobs) * 100) : 0;

    // Get average duration
    const completedJobs = await db.collection('scraping_jobs')
      .find({ status: 'completed', completedAt: { $exists: true } })
      .toArray();

    let averageDuration = '0 minutes';
    if (completedJobs.length > 0) {
      const totalDuration = completedJobs.reduce((sum, job) => {
        const duration = job.completedAt - job.startedAt;
        return sum + duration;
      }, 0);
      const avgMs = totalDuration / completedJobs.length;
      averageDuration = `${Math.round(avgMs / 60000)} minutes`;
    }

    // Get last successful run
    const lastSuccessfulJob = await db.collection('scraping_jobs')
      .findOne({ status: 'completed' }, { sort: { completedAt: -1 } });

    // Determine current status
    let status = 'idle';
    if (runningJobs > 0) {
      status = 'running';
    } else if (latestJob) {
      status = latestJob.status;
    }

    const statusData = {
      status,
      lastRun: latestJob ? latestJob.startedAt.toISOString() : 'Never',
      itemsScraped: latestJob?.itemsScraped || 0,
      successRate: `${successRate}%`,
      currentJob: runningJobs > 0 ? latestJob : null,
      statistics: {
        totalJobs,
        successfulJobs,
        failedJobs,
        runningJobs,
        averageDuration,
        lastSuccessfulRun: lastSuccessfulJob ? lastSuccessfulJob.completedAt.toISOString() : null
      }
    };

    return NextResponse.json({
      success: true,
      data: statusData
    });

  } catch (error) {
    console.error('Scraping status error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to fetch scraping status: ${error.message}`
    }, { status: 500 });
  }
} 