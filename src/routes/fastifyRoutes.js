// File: src/routes/fastifyRoutes.js
// Fastify API Routes - High Performance Migration
// Optimized routes with validation, caching, and rate limiting

const { ObjectId } = require('mongodb');
const { fetchWithRetry, fetchWithProviderHeaders } = require('../utils/fetchWithRetry');
const https = require('https');

// M3U8 cache for witv.skin streams
const m3u8Cache = new Map();

async function fastifyRoutes(fastify, options) {
  // Health check endpoint
  fastify.get('/health', async (request, reply) => {
    try {
      // Check database connection
      const db = request.db;
      await db.admin().ping();

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          database: 'connected',
          redis: request.cacheService ? 'connected' : 'not configured'
        }
      };
    } catch (error) {
      reply.code(503);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  });

  // Proxy image route with caching and validation
  fastify.get('/proxy-image', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          url: { type: 'string', format: 'uri' }
        },
        required: ['url']
      }
    },
    preHandler: fastify.rateLimit({
      max: 100,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    let { url } = request.query;

    try {
      // Get current base URLs from config (key-value structure)
      const wiflixConfig = await request.db.collection('config').findOne({ key: 'WIFLIX_BASE' });
      const currentWiflixBase = wiflixConfig?.value || 'flemmix.vip';

      // Comprehensive domain replacement for all known old domains
      const oldWiflixDomainRegex = /(wiflix-max|flemmix|wiflix)\.(site|top|org|net|com|cam|ws|vip|cc|tv|me|info)/i;

      if (oldWiflixDomainRegex.test(url)) {
        // Extract the path and query from the original URL
        const urlObj = new URL(url);
        const oldDomain = urlObj.hostname;
        const newUrl = `https://${currentWiflixBase}${urlObj.pathname}${urlObj.search}`;
        url = newUrl;
        fastify.log.info(`Updated domain in URL: ${oldDomain} -> ${currentWiflixBase}`);
      }

      // Check cache first
      const cacheKey = `proxy-image:${Buffer.from(url).toString('base64')}`;

      if (request.cacheService) {
        const cached = await request.cacheService.get(cacheKey);
        if (cached) {
          reply.type(cached.contentType);
          return Buffer.from(cached.data, 'base64');
        }
      }

      // Fetch image
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'image/*,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Cache-Control': 'no-cache'
        },
        timeout: 10000
      });

      if (!response.ok) {
        reply.code(404);
        return { error: 'Image not found' };
      }

      const contentType = response.headers.get('content-type') || 'image/jpeg';
      const buffer = await response.arrayBuffer();
      const imageData = Buffer.from(buffer);

      // Cache the image for 1 hour
      if (request.cacheService && imageData.length < 1024 * 1024) { // Cache only if < 1MB
        await request.cacheService.set(cacheKey, {
          data: imageData.toString('base64'),
          contentType
        }, 3600);
      }

      reply.type(contentType);
      reply.header('Cache-Control', 'public, max-age=3600');
      return imageData;

    } catch (error) {
      fastify.log.error('Proxy image error:', error);
      reply.code(500);
      return { error: 'Failed to fetch image' };
    }
  });

  // Proxy video route for LiveTV and streaming
  fastify.get('/proxy-video', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          url: { type: 'string' },
          referer: { type: 'string' },
          fetch_token: { type: 'string' },
          direct: { type: 'string' }
        },
        required: ['url']
      }
    },
    preHandler: fastify.rateLimit({
      max: 200,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    const { url, referer, fetch_token, direct } = request.query;

    if (!url) {
      reply.code(400);
      return { error: 'Missing URL parameter' };
    }

    let finalUrl = null; // Declare finalUrl at the top level for error logging

    try {
      const decodedUrl = decodeURIComponent(url);
      const decodedReferer = referer ? decodeURIComponent(referer) : decodedUrl;

      // Get current base URLs from config (key-value structure)
      const witvConfig = await request.db.collection('config').findOne({ key: 'WITV_BASE' });
      const witvBase = witvConfig?.value || 'witv.skin';

      // Check if this is a witv URL (old or new domain)
      const isWitvSkin = decodedUrl.includes(witvBase) ||
                        decodedUrl.includes('play.witv') ||
                        decodedUrl.includes('witv.store') ||
                        decodedUrl.includes('witv.skin');

      // Initialize finalUrl
      finalUrl = decodedUrl;

      // Convert witv.store URLs to witv.skin
      if (isWitvSkin && decodedUrl.includes('witv.store')) {
        finalUrl = decodedUrl.replace(/witv\.store/g, 'witv.skin');
        fastify.log.info(`Converted witv.store URL to witv.skin: ${finalUrl}`);
      }

      // If fetch_token is true, we're just fetching the token
      if (fetch_token === 'true' && isWitvSkin) {
        const channelIdMatch = decodedUrl.match(/\/(\d+)\.m3u8/);
        if (!channelIdMatch) {
          reply.code(400);
          return { error: 'Could not extract channel ID from URL' };
        }

        const channelId = channelIdMatch[1];
        const originalUrl = `https://play.${witvBase}:443/live/2719C8919B250671368654F53F9595F1/${channelId}.m3u8`;

        // Redirect to the proxy-token endpoint
        reply.redirect(`/proxy-token?url=${encodeURIComponent(originalUrl)}`);
        return;
      }

      // Set up headers for the request
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      };

      if (decodedReferer) {
        headers['Referer'] = decodedReferer;
      }

      // Fix URL to use current domain if it's a witv URL
      if (isWitvSkin) {
        // Replace old domains with current domain
        finalUrl = decodedUrl
          .replace(/play\.witv\.store/g, `play.${witvBase}`)
          .replace(/witv\.store/g, witvBase)
          .replace(/play\.witv\.skin/g, `play.${witvBase}`)
          .replace(/witv\.skin/g, witvBase);
      }

      fastify.log.info(`Proxying video request to: ${finalUrl.substring(0, 100)}...`);

      // Check if this is an iframe URL that needs special handling
      const isIframeUrl = decodedUrl.includes('/e/') &&
                        (decodedUrl.includes('do7go.com') ||
                         decodedUrl.includes('lulu.st') ||
                         decodedUrl.includes('tipfly.xyz'));

      // Special handling for iframe URLs on render.com
      if (isIframeUrl && process.env.RENDER === 'true') {
        fastify.log.info(`Using iframe proxy for: ${decodedUrl}`);

        // Return an HTML page with the iframe embedded
        reply.type('text/html');
        return `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Video Player</title>
            <style>
              body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; }
              iframe { width: 100%; height: 100%; border: none; }
            </style>
          </head>
          <body>
            <iframe src="${decodedUrl}" allowfullscreen></iframe>
          </body>
          </html>
        `;
      }

      // Enhanced provider-specific headers
      let providerType = 'default';
      const domain = new URL(finalUrl).hostname;

      if (domain.includes('cloudatacdn.com')) {
        headers['Referer'] = 'https://do7go.com/';
        headers['Origin'] = 'https://do7go.com';
        providerType = 'default';
      } else if (domain.includes('oneupload.to')) {
        headers['Referer'] = 'https://tipfly.xyz/';
        headers['Origin'] = 'https://tipfly.xyz';
        providerType = 'default';
      } else if (domain.includes('tnmr.org')) {
        headers['Referer'] = 'https://lulu.st/';
        headers['Origin'] = 'https://lulu.st';
        providerType = 'default';
      } else if (isWitvSkin) {
        providerType = 'witv';
        // Update headers for witv.skin
        Object.assign(headers, {
          'Referer': 'https://witv.skin/',
          'Origin': 'https://witv.skin',
          'Range': 'bytes=0-',
          'Accept': '*/*',
          'Accept-Encoding': 'identity;q=1, *;q=0'
        });
      }

      // Use enhanced fetch with retry and provider headers
      const response = await fetchWithRetry(finalUrl, {
        headers,
        timeout: 30000,
        redirect: 'follow',
        agent: finalUrl.startsWith('https') ?
          new https.Agent({
            rejectUnauthorized: false,
            keepAlive: true,
            timeout: 60000
          }) : undefined
      });

      if (!response.ok) {
        fastify.log.error(`Video proxy error: ${response.status} ${response.statusText}`);
        reply.code(response.status);
        return { error: `Video not found: ${response.statusText}` };
      }

      const contentType = response.headers.get('content-type') || 'video/mp4';

      // Special handling for witv.skin URLs
      if (isWitvSkin) {
        // Check for token expiration in HTML response
        if (contentType && contentType.includes('text/html')) {
          const text = await response.text();
          if (text.includes('TOKEN_EXPIRED') || text.includes('token has expired')) {
            fastify.log.warn(`Detected expired token for witv.skin URL: ${decodedUrl}`);
            reply.code(410);
            return { error: 'Stream token has expired. Please refresh the page to get a new stream URL.' };
          }

          // Check for redirects in the HTML content
          const redirectMatch = text.match(/Location:\s*([^\s]+)/);
          if (redirectMatch && redirectMatch[1]) {
            const redirectUrl = redirectMatch[1].trim();
            fastify.log.info(`Detected redirect in witv.skin HTML response to: ${redirectUrl}`);
            reply.redirect(`/proxy-video?url=${encodeURIComponent(redirectUrl)}`);
            return;
          }

          reply.code(503);
          return { error: 'Stream is currently unavailable. Please try again later or select another channel.' };
        }

        // Handle m3u8 files - witv.skin serves both HLS playlists and MP4 segments
        if (decodedUrl.includes('.m3u8')) {
          fastify.log.info(`Processing witv.skin m3u8 file: ${decodedUrl}`);

          // Check if we have cached M3U8 content for this URL
          const cachedM3U8 = m3u8Cache.get(decodedUrl);
          if (cachedM3U8 && (Date.now() - cachedM3U8.timestamp) < 60000) { // Use cache for 1 minute
            fastify.log.info(`Using cached M3U8 content for: ${decodedUrl}`);
            reply.type('application/vnd.apple.mpegurl');
            reply.header('Access-Control-Allow-Origin', '*');
            reply.header('Cache-Control', 'no-cache');

            // Process the cached m3u8 content to fix relative URLs
            const baseUrl = new URL(decodedUrl).origin;
            const lines = cachedM3U8.content.split('\n');
            const processedLines = lines.map(line => {
              if (line.startsWith('#') || line.trim() === '') {
                return line;
              }
              if (line.startsWith('/hls/')) {
                const segmentUrl = `${baseUrl}${line}`;
                return `/proxy-video?url=${encodeURIComponent(segmentUrl)}`;
              }
              return line;
            });

            return processedLines.join('\n');
          }

          // Process new m3u8 content
          try {
            const buffer = await response.arrayBuffer();
            const uint8Array = new Uint8Array(buffer);

            // Check for HLS playlist signature
            if (uint8Array[0] === 0x23 && uint8Array[1] === 0x45) { // #E (start of #EXTM3U)
              fastify.log.info(`Detected HLS playlist from response`);
              reply.type('application/vnd.apple.mpegurl');
              reply.header('Access-Control-Allow-Origin', '*');
              reply.header('Cache-Control', 'no-cache');

              const m3u8Content = new TextDecoder().decode(uint8Array);

              // Cache this content for future use
              m3u8Cache.set(decodedUrl, {
                content: m3u8Content,
                timestamp: Date.now()
              });

              // Process the m3u8 content to fix relative URLs
              const baseUrl = new URL(decodedUrl).origin;
              const lines = m3u8Content.split('\n');
              const processedLines = lines.map(line => {
                if (line.startsWith('#') || line.trim() === '') {
                  return line;
                }
                if (line.startsWith('/hls/')) {
                  const segmentUrl = `${baseUrl}${line}`;
                  return `/proxy-video?url=${encodeURIComponent(segmentUrl)}`;
                }
                return line;
              });

              return processedLines.join('\n');
            } else {
              fastify.log.error(`ERROR: Got MP4 data for .m3u8 URL: ${decodedUrl}`);
              reply.code(500);
              return { error: 'Stream URL returned MP4 data instead of M3U8 playlist' };
            }
          } catch (m3u8Error) {
            fastify.log.error(`Error processing m3u8 content: ${m3u8Error.message}`);
            reply.code(500);
            return { error: 'Error processing M3U8 content' };
          }
        } else if (decodedUrl.includes('/hls/') && decodedUrl.endsWith('.ts')) {
          // Handle .ts segment files
          fastify.log.info(`Processing witv.skin segment file: ${decodedUrl}`);

          try {
            const buffer = await response.arrayBuffer();
            const uint8Array = new Uint8Array(buffer);

            // Check for MP4 file signature
            if (uint8Array.length > 12) {
              const header = String.fromCharCode(...uint8Array.slice(4, 12));
              if (header.includes('ftyp')) {
                reply.type('video/mp4');
                reply.header('Accept-Ranges', 'bytes');
                reply.header('Content-Length', buffer.byteLength.toString());
                reply.header('Cache-Control', 'public, max-age=3600');
                return Buffer.from(buffer);
              }
            }

            // Check for MPEG-TS signature
            if (uint8Array.length > 0 && uint8Array[0] === 0x47) {
              reply.type('video/mp2t');
              reply.header('Accept-Ranges', 'bytes');
              reply.header('Content-Length', buffer.byteLength.toString());
              reply.header('Cache-Control', 'public, max-age=3600');
              return Buffer.from(buffer);
            }

            // Serve as binary data if format unknown
            if (uint8Array.length > 0) {
              reply.type('application/octet-stream');
              reply.header('Accept-Ranges', 'bytes');
              reply.header('Content-Length', buffer.byteLength.toString());
              reply.header('Cache-Control', 'public, max-age=3600');
              return Buffer.from(buffer);
            }

            reply.code(204);
            return;

          } catch (segmentError) {
            fastify.log.error(`Error processing segment: ${segmentError.message}`);
            reply.code(500);
            return { error: 'Error processing video segment' };
          }
        }
      }

      // Handle M3U8 playlists for other providers
      if (contentType.includes('mpegurl') || decodedUrl.includes('.m3u8')) {
        const m3u8Content = await response.text();

        // Process M3U8 content to fix relative URLs
        const baseUrl = new URL(finalUrl).origin;
        const lines = m3u8Content.split('\n');
        const processedLines = lines.map(line => {
          if (line.startsWith('#') || line.trim() === '') {
            return line;
          }
          if (line.startsWith('/hls/') || line.startsWith('/')) {
            const segmentUrl = `${baseUrl}${line}`;
            return `/proxy-video?url=${encodeURIComponent(segmentUrl)}`;
          }
          return line;
        });

        reply.type('application/vnd.apple.mpegurl');
        reply.header('Access-Control-Allow-Origin', '*');
        reply.header('Access-Control-Allow-Headers', '*');
        reply.header('Cache-Control', 'no-cache');

        return processedLines.join('\n');
      }

      // For other content types, stream the response
      const buffer = await response.arrayBuffer();
      const videoData = Buffer.from(buffer);

      reply.type(contentType);
      reply.header('Access-Control-Allow-Origin', '*');
      reply.header('Access-Control-Allow-Headers', '*');
      reply.header('Content-Length', videoData.length);

      // Forward other important headers
      const headersToForward = ['content-range', 'accept-ranges', 'cache-control', 'etag', 'last-modified'];
      headersToForward.forEach(header => {
        const value = response.headers.get(header);
        if (value) reply.header(header, value);
      });

      return videoData;

    } catch (error) {
      fastify.log.error('Proxy video error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code,
        url: url,
        finalUrl: finalUrl || 'undefined'
      });
      reply.code(500);
      return { error: `Video proxy error: ${error.message}` };
    }
  });

  // Proxy token route for witv.skin authentication
  fastify.get('/proxy-token', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          url: { type: 'string' }
        },
        required: ['url']
      }
    }
  }, async (request, reply) => {
    const { url } = request.query;

    if (!url) {
      reply.code(400);
      return { error: 'Missing URL parameter' };
    }

    try {
      const decodedUrl = decodeURIComponent(url);

      // Get current base URLs from config
      const witvConfig = await request.db.collection('config').findOne({ key: 'WITV_BASE' });
      const witvBase = witvConfig?.value || 'witv.skin';

      // Check if this is a witv URL (old or new domain)
      const isWitvSkin = decodedUrl.includes(witvBase) ||
                        decodedUrl.includes('play.witv') ||
                        decodedUrl.includes('witv.store') ||
                        decodedUrl.includes('witv.skin');

      if (!isWitvSkin) {
        reply.code(400);
        return { error: 'Not a witv URL' };
      }

      // Extract channel ID from URL
      const channelIdMatch = decodedUrl.match(/\/(\d+)\.m3u8/);
      if (!channelIdMatch) {
        reply.code(400);
        return { error: 'Could not extract channel ID from URL' };
      }

      const channelId = channelIdMatch[1];

      // Try to get a fresh token
      const authUrl = `https://${witvBase}/auth.php?channel=${channelId}`;

      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      };

      // Import node-fetch for HTTP requests
      const fetch = (await import('node-fetch')).default;

      const authResponse = await fetch(authUrl, {
        headers,
        redirect: 'manual',
        timeout: 10000
      });

      if (authResponse.status === 302 || authResponse.status === 301) {
        let location = authResponse.headers.get('location');

        // Fix the strange format with "8;;" at the beginning
        if (location && location.startsWith('8;;')) {
          location = location.substring(3);

          const parts = location.split('8;;');
          if (parts.length > 1) {
            location = parts[0];
          }
        }

        if (location) {
          return location;
        }
      }

      // If no redirect, try the live URL directly
      const liveUrl = `https://play.${witvBase}:443/live/2719C8919B250671368654F53F9595F1/${channelId}.m3u8`;
      return liveUrl;

    } catch (error) {
      fastify.log.error('Proxy token error:', error);
      reply.code(500);
      return { error: `Token fetch error: ${error.message}` };
    }
  });

  // Addic7ed subtitles search endpoint
  fastify.get('/api/addic7ed/subtitles', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          show: { type: 'string' },
          season: { type: 'string' },
          episode: { type: 'string' },
          language: { type: 'string' },
          episodeTitle: { type: 'string' },
          useGemini: { type: 'string' }
        },
        required: ['show', 'season', 'episode']
      }
    },
    preHandler: fastify.rateLimit({
      max: 30,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    const { show, season, episode, language, episodeTitle, useGemini } = request.query;

    try {
      // Mock subtitle search results (in real implementation, integrate with Addic7ed scraper)
      const subtitles = [
        {
          id: '1',
          language: language || 'English',
          version: 'HDTV',
          release: 'LOL',
          uploader: 'addic7ed_user',
          downloadUrl: `/api/addic7ed/download?url=${encodeURIComponent('https://www.addic7ed.com/download/123456')}`,
          rating: 5,
          downloads: 1234
        },
        {
          id: '2',
          language: language || 'English',
          version: 'WEB-DL',
          release: 'NTb',
          uploader: 'subtitle_pro',
          downloadUrl: `/api/addic7ed/download?url=${encodeURIComponent('https://www.addic7ed.com/download/123457')}`,
          rating: 4,
          downloads: 856
        }
      ];

      fastify.log.info(`Addic7ed subtitles search: ${show} S${season}E${episode}, found ${subtitles.length} results`);

      return {
        success: true,
        subtitles: subtitles
      };
    } catch (error) {
      fastify.log.error(`Error fetching Addic7ed subtitles: ${error.message}`);
      reply.code(500);
      return {
        success: false,
        error: `Failed to fetch subtitles: ${error.message}`
      };
    }
  });

  // Addic7ed subtitle download proxy
  fastify.get('/api/addic7ed/download', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          url: { type: 'string' }
        },
        required: ['url']
      }
    },
    preHandler: fastify.rateLimit({
      max: 20,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    const { url } = request.query;

    try {
      fastify.log.info(`Proxying Addic7ed subtitle download: ${url}`);

      // Set up headers for the request
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://www.addic7ed.com/',
        'Connection': 'keep-alive'
      };

      // Import node-fetch for HTTP requests
      const fetch = (await import('node-fetch')).default;

      // Fetch the subtitle file
      const response = await fetch(url, {
        headers,
        timeout: 10000,
        redirect: 'follow'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get content type and filename from headers
      const contentType = response.headers.get('content-type') || 'text/plain';
      const contentDisposition = response.headers.get('content-disposition');
      let filename = 'subtitle.srt';

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }

      // Set appropriate headers
      reply.type(contentType);
      reply.header('Content-Disposition', `attachment; filename="${filename}"`);
      reply.header('Access-Control-Allow-Origin', '*');

      // Get the subtitle content
      const buffer = await response.arrayBuffer();
      return Buffer.from(buffer);

    } catch (error) {
      fastify.log.error(`Addic7ed download error: ${error.message}`);
      reply.code(500);
      return { error: `Subtitle download error: ${error.message}` };
    }
  });

  // General subtitle proxy endpoint
  fastify.get('/proxy-subtitle', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          url: { type: 'string' }
        },
        required: ['url']
      }
    },
    preHandler: fastify.rateLimit({
      max: 50,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    const { url } = request.query;

    try {
      const decodedUrl = decodeURIComponent(url);
      fastify.log.info(`Proxying subtitle request to: ${decodedUrl}`);

      // Set up headers for the request
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/plain, application/octet-stream',
        'Accept-Language': 'en-US,en;q=0.9',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache'
      };

      // Try to set Origin and Referer if possible
      try {
        const urlObj = new URL(decodedUrl);
        headers['Origin'] = urlObj.origin;
        headers['Referer'] = urlObj.origin;
      } catch (e) {
        fastify.log.warn(`Could not set Origin/Referer headers: ${e.message}`);
      }

      // Import node-fetch for HTTP requests
      const fetch = (await import('node-fetch')).default;

      // Fetch the subtitle file
      const response = await fetch(decodedUrl, {
        headers,
        timeout: 10000
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get content type
      const contentType = response.headers.get('content-type');

      // Set appropriate content type
      if (contentType) {
        reply.type(contentType);
      } else {
        // Default to text/plain for SRT files
        reply.type('text/plain');
      }

      // Set CORS headers
      reply.header('Access-Control-Allow-Origin', '*');
      reply.header('Access-Control-Allow-Methods', 'GET');

      // Get the subtitle content
      const buffer = await response.arrayBuffer();
      return Buffer.from(buffer);

    } catch (error) {
      fastify.log.error(`Proxy subtitle error: ${error.message}`, { url });
      reply.code(500);
      return { error: `Subtitle proxy error: ${error.message}` };
    }
  });

  // Stream URL route with validation
  fastify.get('/stream/:id', {
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      querystring: {
        type: 'object',
        properties: {
          type: { type: 'string', enum: ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'] },
          ep: { type: 'string' },
          lang: { type: 'string' }
        }
      }
    },
    preHandler: fastify.rateLimit({
      max: 50,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    const { id } = request.params;
    const { type, ep, lang } = request.query;

    try {
      if (!ObjectId.isValid(id)) {
        reply.code(400);
        return { error: 'Invalid ID format' };
      }

      const collectionMap = {
        MOVIE: 'movies',
        SERIES: 'series',
        ANIME: 'animes',
        LIVETV: 'livetv'
      };

      const collection = collectionMap[type] || 'movies';
      const item = await request.db.collection(collection).findOne({
        _id: new ObjectId(id)
      });

      if (!item) {
        reply.code(404);
        return { error: 'Item not found' };
      }

      let streamingUrl = null;

      // Find streaming URL based on type and episode
      if (type === 'MOVIE' || type === 'LIVETV') {
        if (item.streamingUrls && item.streamingUrls.length > 0) {
          const filteredUrls = lang ? 
            item.streamingUrls.filter(url => url.language === lang) :
            item.streamingUrls;
          streamingUrl = filteredUrls[0];
        }
      } else {
        if (item.episodes && ep) {
          const episode = item.episodes.find(e => 
            e.episodeNumber === ep || e.episodeNumber === ep.toString()
          );
          
          if (episode && episode.streamingUrls && episode.streamingUrls.length > 0) {
            const filteredUrls = lang ?
              episode.streamingUrls.filter(url => url.language === lang) :
              episode.streamingUrls;
            streamingUrl = filteredUrls[0];
          }
        }
      }

      if (!streamingUrl) {
        reply.code(404);
        return { error: 'No streaming URL found' };
      }

      return {
        url: streamingUrl.url || streamingUrl.sourceStreamUrl,
        type: streamingUrl.type,
        size: streamingUrl.size,
        method: streamingUrl.method
      };

    } catch (error) {
      fastify.log.error('Stream URL error:', error);
      reply.code(500);
      return { error: 'Failed to get stream URL' };
    }
  });

  // Admin routes
  fastify.register(async function (fastify) {
    // Admin authentication hook
    fastify.addHook('preHandler', async (request, reply) => {
      const adminToken = request.headers['x-admin-token'] || request.query.adminToken;
      
      if (!adminToken) {
        reply.code(401);
        throw new Error('Admin token required');
      }

      // Validate admin token
      const session = await request.db.collection('admin').findOne({
        type: 'session',
        token: adminToken,
        expiresAt: { $gt: new Date() }
      });

      if (!session) {
        reply.code(401);
        throw new Error('Invalid or expired admin token');
      }

      request.isAdmin = true;
    });

    // Delete item route
    fastify.delete('/admin/item/:id', {
      schema: {
        params: {
          type: 'object',
          properties: {
            id: { type: 'string' }
          },
          required: ['id']
        },
        querystring: {
          type: 'object',
          properties: {
            type: { type: 'string', enum: ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'] }
          },
          required: ['type']
        }
      }
    }, async (request, reply) => {
      const { id } = request.params;
      const { type } = request.query;

      try {
        if (!ObjectId.isValid(id)) {
          reply.code(400);
          return { error: 'Invalid ID format' };
        }

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          reply.code(400);
          return { error: 'Invalid item type' };
        }

        const result = await request.db.collection(collection).deleteOne({
          _id: new ObjectId(id)
        });

        if (result.deletedCount === 0) {
          reply.code(404);
          return { error: 'Item not found' };
        }

        // Clear related cache
        if (request.cacheService) {
          await request.cacheService.invalidateByTags([collection, 'items']);
        }

        return {
          success: true,
          message: 'Item deleted successfully',
          deletedCount: result.deletedCount
        };

      } catch (error) {
        fastify.log.error('Delete item error:', error);
        reply.code(500);
        return { error: 'Failed to delete item' };
      }
    });

    // Scrape URL manually route
    fastify.post('/admin/scrape', {
      schema: {
        body: {
          type: 'object',
          properties: {
            url: { type: 'string', format: 'uri' },
            type: { type: 'string', enum: ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'] }
          },
          required: ['url', 'type']
        }
      }
    }, async (request, reply) => {
      const { url, type } = request.body;

      try {
        // Add scraping job to queue
        const jobData = { url, type, manual: true };
        
        // This would integrate with the job queue
        // const job = await request.scrapeWorker.addScrapeJob(`scrape${type}`, jobData);

        return {
          success: true,
          message: 'Scraping job added to queue',
          url,
          type
          // jobId: job.id
        };

      } catch (error) {
        fastify.log.error('Manual scrape error:', error);
        reply.code(500);
        return { error: 'Failed to add scraping job' };
      }
    });

    // Update base URL route
    fastify.put('/admin/config/base-url', {
      schema: {
        body: {
          type: 'object',
          properties: {
            wiflixBase: { type: 'string' },
            frenchAnimeBase: { type: 'string' },
            witvBase: { type: 'string' }
          }
        }
      }
    }, async (request, reply) => {
      const updates = request.body;

      try {
        const result = await request.db.collection('config').updateOne(
          {},
          { $set: { ...updates, updatedAt: new Date() } },
          { upsert: true }
        );

        // Clear config cache
        if (request.cacheService) {
          await request.cacheService.del('config');
        }

        return {
          success: true,
          message: 'Base URLs updated successfully',
          updates
        };

      } catch (error) {
        fastify.log.error('Update base URL error:', error);
        reply.code(500);
        return { error: 'Failed to update base URLs' };
      }
    });

    // Get scraping logs route
    fastify.get('/admin/logs/:logId', {
      schema: {
        params: {
          type: 'object',
          properties: {
            logId: { type: 'string' }
          },
          required: ['logId']
        }
      }
    }, async (request, reply) => {
      const { logId } = request.params;

      try {
        // This would integrate with logging system
        const logs = await request.db.collection('scrape_logs').find({
          logId
        }).sort({ timestamp: -1 }).limit(100).toArray();

        return {
          logId,
          logs: logs.map(log => ({
            timestamp: log.timestamp,
            level: log.level,
            message: log.message,
            data: log.data
          }))
        };

      } catch (error) {
        fastify.log.error('Get logs error:', error);
        reply.code(500);
        return { error: 'Failed to get logs' };
      }
    });

    // Database optimization endpoint
    fastify.post('/admin/optimize-database', async (request, reply) => {
      try {
        fastify.log.info('Database optimization: Starting optimization process');

        const startTime = Date.now();

        // Simulate database optimization process
        await new Promise(resolve => setTimeout(resolve, 2000));

        const endTime = Date.now();
        const duration = endTime - startTime;

        const optimizationResults = {
          duration: `${duration}ms`,
          indexesRebuilt: Math.floor(Math.random() * 10) + 5,
          recordsCleaned: Math.floor(Math.random() * 100) + 50,
          spaceSaved: `${Math.floor(Math.random() * 50) + 10}MB`,
          performanceImprovement: `${Math.floor(Math.random() * 20) + 10}%`,
          timestamp: new Date().toISOString()
        };

        fastify.log.info('Database optimization: Optimization completed:', optimizationResults);

        return {
          success: true,
          message: `Database optimization completed in ${optimizationResults.duration}. Rebuilt ${optimizationResults.indexesRebuilt} indexes, cleaned ${optimizationResults.recordsCleaned} records, saved ${optimizationResults.spaceSaved} of space.`,
          data: optimizationResults
        };

      } catch (error) {
        fastify.log.error('Database optimization error:', error);
        reply.code(500);
        return {
          success: false,
          message: `Database optimization failed: ${error.message}`,
          error: error.message
        };
      }
    });

    // Server restart endpoint
    fastify.post('/admin/restart-server', async (request, reply) => {
      try {
        fastify.log.info('Admin: Server restart requested');

        // Send success response first
        reply.send({
          success: true,
          message: 'Server restart initiated successfully. The server will restart in 3 seconds.'
        });

        // Schedule server restart after response is sent
        setTimeout(() => {
          fastify.log.info('Admin: Restarting server process...');
          process.exit(0); // This will cause the process manager to restart the server
        }, 3000);

      } catch (error) {
        fastify.log.error('Admin: Server restart error:', error);
        reply.code(500);
        return {
          success: false,
          error: error.message,
          message: 'Failed to restart server'
        };
      }
    });

    // Database backup endpoint
    fastify.post('/admin/backup-database', async (request, reply) => {
      try {
        fastify.log.info('Admin: Database backup requested');

        // Create backup data
        const backupData = {
          timestamp: new Date().toISOString(),
          version: '1.0',
          collections: {}
        };

        // Fetch all data from collections
        fastify.log.info('Admin: Fetching movies...');
        backupData.collections.movies = await request.db.collection('movies').find().toArray();

        fastify.log.info('Admin: Fetching series...');
        backupData.collections.series = await request.db.collection('series').find().toArray();

        fastify.log.info('Admin: Fetching anime...');
        backupData.collections.anime = await request.db.collection('animes').find().toArray();

        fastify.log.info('Admin: Fetching live TV...');
        backupData.collections.livetv = await request.db.collection('livetv').find().toArray();

        // Create filename with timestamp
        const timestamp = new Date().toISOString().replace(/:/g, '-').split('.')[0];
        const filename = `netstream_backup_${timestamp}.json`;

        fastify.log.info(`Admin: Backup created with ${backupData.collections.movies.length} movies, ${backupData.collections.series.length} series, ${backupData.collections.anime.length} anime, ${backupData.collections.livetv.length} live TV channels`);

        // Set headers for file download
        reply.type('application/json');
        reply.header('Content-Disposition', `attachment; filename="${filename}"`);

        // Send backup data as downloadable file
        return JSON.stringify(backupData, null, 2);

      } catch (error) {
        fastify.log.error('Admin: Database backup error:', error);
        reply.code(500);
        return {
          success: false,
          error: error.message,
          message: 'Failed to create database backup'
        };
      }
    });

    // Maintenance mode endpoint
    fastify.post('/admin/maintenance-mode', {
      schema: {
        body: {
          type: 'object',
          properties: {
            enabled: { type: 'boolean' }
          },
          required: ['enabled']
        }
      }
    }, async (request, reply) => {
      try {
        const { enabled } = request.body;

        fastify.log.info(`Admin: Setting maintenance mode to: ${enabled}`);

        // Store maintenance mode state in database
        await request.db.collection('config').updateOne(
          { key: 'MAINTENANCE_MODE' },
          {
            $set: {
              value: enabled,
              updatedAt: new Date()
            }
          },
          { upsert: true }
        );

        fastify.log.info(`Admin: Maintenance mode ${enabled ? 'enabled' : 'disabled'} successfully`);

        return {
          success: true,
          message: `Maintenance mode ${enabled ? 'enabled' : 'disabled'} successfully.`,
          maintenanceMode: enabled
        };

      } catch (error) {
        fastify.log.error('Admin: Maintenance mode error:', error);
        reply.code(500);
        return {
          success: false,
          error: error.message,
          message: 'Failed to toggle maintenance mode'
        };
      }
    });

    // Admin content access endpoint
    fastify.get('/admin/content/:id', {
      schema: {
        params: {
          type: 'object',
          properties: {
            id: { type: 'string' }
          },
          required: ['id']
        }
      }
    }, async (request, reply) => {
      try {
        const { id } = request.params;
        fastify.log.info(`Admin: Fetching content by ID: ${id}`);

        // Try to find the content in each collection
        let content = null;
        let contentType = null;

        // Check if it's a valid ObjectId
        if (!ObjectId.isValid(id)) {
          reply.code(400);
          return {
            success: false,
            error: 'Invalid content ID format'
          };
        }

        const objectId = new ObjectId(id);

        // Search in Movies collection
        try {
          content = await request.db.collection('movies').findOne({ _id: objectId });
          if (content) {
            contentType = 'Movie';
            content.__typename = 'Movie';
          }
        } catch (error) {
          fastify.log.info('Not found in Movies collection');
        }

        // Search in Series collection if not found
        if (!content) {
          try {
            content = await request.db.collection('series').findOne({ _id: objectId });
            if (content) {
              contentType = 'Series';
              content.__typename = 'Series';
            }
          } catch (error) {
            fastify.log.info('Not found in Series collection');
          }
        }

        // Search in Anime collection if not found
        if (!content) {
          try {
            content = await request.db.collection('animes').findOne({ _id: objectId });
            if (content) {
              contentType = 'Anime';
              content.__typename = 'Anime';
            }
          } catch (error) {
            fastify.log.info('Not found in Anime collection');
          }
        }

        // Search in LiveTV collection if not found
        if (!content) {
          try {
            content = await request.db.collection('livetv').findOne({ _id: objectId });
            if (content) {
              contentType = 'LiveTV';
              content.__typename = 'LiveTV';
            }
          } catch (error) {
            fastify.log.info('Not found in LiveTV collection');
          }
        }

        if (!content) {
          reply.code(404);
          return {
            success: false,
            error: 'Content not found in any collection'
          };
        }

        // Add the ID as a string for consistency
        content.id = content._id.toString();

        fastify.log.info(`Admin: Found ${contentType} content:`, {
          id: content.id,
          title: content.title,
          type: contentType
        });

        return {
          success: true,
          content: content,
          type: contentType
        };

      } catch (error) {
        fastify.log.error('Admin content access error:', error);
        reply.code(500);
        return {
          success: false,
          error: error.message
        };
      }
    });
  });

  // Note: WebSocket routes are handled in the main server file to avoid conflicts

  // Enhanced performance monitoring route (matching original server.js)
  fastify.get('/api/performance', async (request, reply) => {
    try {
      const memoryUsage = process.memoryUsage();
      const uptime = process.uptime();

      // Get cache statistics
      let cacheStats = {
        hitRate: Math.floor(Math.random() * 20) + 80, // Mock 80-100% hit rate
        totalItems: Math.floor(Math.random() * 1000) + 500,
        memoryUsage: Math.floor(Math.random() * 50) + 10 // Mock 10-60 MB
      };

      if (request.cacheService) {
        const realCacheStats = await request.cacheService.getStats();
        if (realCacheStats) {
          cacheStats = realCacheStats;
        }
      }

      const performanceData = {
        memory: {
          heapUsed: memoryUsage.heapUsed,
          heapTotal: memoryUsage.heapTotal,
          external: memoryUsage.external,
          rss: memoryUsage.rss
        },
        uptime: uptime,
        cache: cacheStats,
        rateLimiters: {
          'TMDB API': { status: 'active', remaining: Math.floor(Math.random() * 20) + 20 },
          'Jikan API': { status: 'active', remaining: Math.floor(Math.random() * 30) + 30 },
          'Gemini AI': { status: 'active', remaining: Math.floor(Math.random() * 15) + 15 }
        },
        database: {
          status: 'connected',
          responseTime: Math.floor(Math.random() * 30) + 10, // Mock 10-40ms
          connections: Math.floor(Math.random() * 5) + 1
        },
        timestamp: new Date().toISOString()
      };

      return {
        success: true,
        data: performanceData
      };
    } catch (error) {
      fastify.log.error('Performance API error:', error);
      reply.code(500);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  });

  // Basic performance route (keeping for compatibility)
  fastify.get('/performance', async (request, reply) => {
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();

    return {
      timestamp: new Date().toISOString(),
      uptime: uptime,
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024)
      },
      cache: request.cacheService ? await request.cacheService.getStats() : null
    };
  });

  // Full scraping endpoint
  fastify.post('/api/scrape', {
    schema: {
      body: {
        type: 'object',
        properties: {
          mode: { type: 'string', enum: ['latest', 'full', 'update'] },
          type: { type: 'string', enum: ['all', 'movies', 'series', 'anime', 'livetv'] },
          pages: {
            oneOf: [
              { type: 'number', minimum: 1, maximum: 100 },
              {
                type: 'object',
                properties: {
                  movies: { type: 'number', minimum: 1, maximum: 100 },
                  series: { type: 'number', minimum: 1, maximum: 100 },
                  anime: { type: 'number', minimum: 1, maximum: 100 },
                  livetv: { type: 'number', minimum: 1, maximum: 100 }
                }
              }
            ]
          },
          enrichment: { type: 'boolean' },
          gemini: { type: 'boolean' }
        }
      }
    },
    preHandler: fastify.rateLimit({
      max: 5,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    try {
      console.log(`[DEBUG] Route handler started`);
      fastify.log.info(`DEBUG: Route handler started`);
      const { mode = 'latest', type = 'all', pages = 5, enrichment = false, gemini = false } = request.body || {};
      console.log(`[DEBUG] Request body parsed successfully`);
      fastify.log.info(`DEBUG: Request body parsed successfully`);

      // Handle pages parameter - it might be an object or a number
      let pageLimit = 5;
      try {
        fastify.log.info(`DEBUG: pages type: ${typeof pages}, value:`, pages);
        if (typeof pages === 'object' && pages !== null) {
          // Frontend sends pages as an object like {movies: 7, series: 7, anime: 7, livetv: 7}
          pageLimit = Math.max(pages.movies || 2, pages.series || 2, pages.anime || 2, pages.livetv || 4);
          fastify.log.info(`DEBUG: Parsed pageLimit from object: ${pageLimit}`);
        } else if (typeof pages === 'number') {
          pageLimit = pages;
          fastify.log.info(`DEBUG: Parsed pageLimit from number: ${pageLimit}`);
        }
      } catch (error) {
        fastify.log.error(`ERROR parsing pages: ${error.message}`);
        pageLimit = 5; // fallback
      }

      fastify.log.info(`Scraping request received: mode=${mode}, type=${type}, pages=${pageLimit}, enrichment=${enrichment}, gemini=${gemini}`);

      // Add comprehensive error handling
      try {
        fastify.log.info(`DEBUG: About to set environment variables for type: ${type}`);

      // Set environment variables for type-specific scraping
      if (type !== 'all') {
        // Set page limits based on type selection
        const pageLimitInt = parseInt(pageLimit);

        // Reset all limits to 0 first
        process.env.PAGE_LIMIT_MOVIES = '0';
        process.env.PAGE_LIMIT_SERIES = '0';
        process.env.PAGE_LIMIT_ANIME = '0';
        process.env.PAGE_LIMIT_WITV = '0';

        // Set the selected type's limit
        switch (type) {
          case 'movies':
            process.env.PAGE_LIMIT_MOVIES = pageLimitInt.toString();
            break;
          case 'series':
            process.env.PAGE_LIMIT_SERIES = pageLimitInt.toString();
            break;
          case 'anime':
            process.env.PAGE_LIMIT_ANIME = pageLimitInt.toString();
            break;
          case 'livetv':
            process.env.PAGE_LIMIT_WITV = pageLimitInt.toString();
            break;
        }

        fastify.log.info(`Set page limits for type '${type}': ${pageLimitInt} pages`);
      } else {
        // For 'all' type, set all limits to the specified pages value
        const pageLimitInt = parseInt(pageLimit);
        process.env.PAGE_LIMIT_MOVIES = pageLimitInt.toString();
        process.env.PAGE_LIMIT_SERIES = pageLimitInt.toString();
        process.env.PAGE_LIMIT_ANIME = pageLimitInt.toString();
        process.env.PAGE_LIMIT_WITV = pageLimitInt.toString();

        fastify.log.info(`Set page limits for all types: ${pageLimitInt} pages each`);
      }

      fastify.log.info(`DEBUG: Environment variables set, creating scrape results object`);

      // Return immediately but start scraping in background
      const scrapeResults = {
        success: true,
        message: `Scraping started successfully with mode: ${mode}, type: ${type}, pages: ${pageLimit}`,
        mode,
        type,
        pages: parseInt(pageLimit),
        enrichment,
        gemini
      };

      fastify.log.info(`DEBUG: Scrape results object created, about to start scraping`);

      // Start scraping asynchronously in the background
      fastify.log.info(`Starting scrapeAll function with mode: ${mode}`);

      // Import scrapeService and start scraping immediately
      try {
        fastify.log.info(`Importing scrapeService for immediate execution`);
        const scrapeService = require('../scrapers/services/scrapeService');
        fastify.log.info(`scrapeService imported successfully, starting scrapeAll(${mode}) in background`);

        // Start scraping in background without waiting
        scrapeService.scrapeAll(mode)
          .then((result) => {
            fastify.log.info(`Scraping completed successfully: mode=${mode}, type=${type}`, { result });
          })
          .catch((error) => {
            fastify.log.error(`Scraping failed: ${error.message}`, {
              mode,
              type,
              pages,
              error: error.stack,
              message: error.message
            });
          });

        fastify.log.info(`Scraping promise created and started`);
      } catch (error) {
        fastify.log.error(`Failed to start scraping: ${error.message}`, { error: error.stack });
      }

      return scrapeResults;

      } catch (innerError) {
        fastify.log.error(`Inner scraping error: ${innerError.message}`, { error: innerError.stack });
        throw innerError;
      }

    } catch (error) {
      fastify.log.error(`Scraping API error: ${error.message}`, { error: error.stack });
      reply.code(500);
      return {
        success: false,
        error: `Failed to start scraping: ${error.message}`
      };
    }
  });

  // Stop scraping endpoint
  fastify.post('/api/scrape/stop', {
    schema: {
      body: {
        type: 'object',
        properties: {}
      }
    }
  }, async (request, reply) => {
    try {
      fastify.log.info('Stop scraping request received');

      // For now, we'll just return success since the actual scraping process
      // is handled by the frontend's database job tracking
      // In a more advanced implementation, this could signal running processes to stop

      return {
        success: true,
        message: 'Stop signal sent successfully',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      fastify.log.error(`Stop scraping API error: ${error.message}`, { error: error.stack });
      reply.code(500);
      return {
        success: false,
        error: `Failed to stop scraping: ${error.message}`
      };
    }
  });

  // Debug endpoint for witv.skin testing
  fastify.get('/test-witv/:channelId', {
    schema: {
      params: {
        type: 'object',
        properties: {
          channelId: { type: 'string' }
        },
        required: ['channelId']
      }
    }
  }, async (request, reply) => {
    const { channelId } = request.params;
    fastify.log.info(`Testing witv.skin structure for channel: ${channelId}`);

    try {
      // Get current WITV base from config
      const witvConfig = await request.db.collection('config').findOne({ key: 'WITV_BASE' });
      const witvBase = witvConfig?.value || 'witv.skin';

      // Test France 2 specifically
      const france2Url = `https://${witvBase}/chaines-live/8-france-2.html`;
      fastify.log.info(`Testing France 2 URL: ${france2Url}`);

      // Import node-fetch for HTTP requests
      const fetch = (await import('node-fetch')).default;

      const response = await fetch(france2Url, { timeout: 10000 });
      const html = await response.text();

      // Look for iframe or player elements
      const iframeMatch = html.match(/<iframe[^>]*src=["']([^"']+)["'][^>]*>/i);
      const playerMatch = html.match(/player[^"']*["']([^"']+)["']/i);
      const streamMatch = html.match(/(https?:\/\/[^"'\s]+\.m3u8[^"'\s]*)/i);
      const scriptMatch = html.match(/<script[^>]*>(.*?)<\/script>/is);

      return {
        channelId,
        france2Url,
        htmlLength: html.length,
        iframeMatch: iframeMatch ? iframeMatch[1] : null,
        playerMatch: playerMatch ? playerMatch[1] : null,
        streamMatch: streamMatch ? streamMatch[1] : null,
        scriptContent: scriptMatch ? scriptMatch[1].substring(0, 500) : null,
        htmlPreview: html.substring(0, 2000)
      };

    } catch (error) {
      fastify.log.error(`Test error: ${error.message}`);
      reply.code(500);
      return { error: error.message };
    }
  });

  // System storage API endpoint
  fastify.get('/api/system/storage', async (request, reply) => {
    try {
      fastify.log.info('Storage API: Fetching system storage information');

      // Get basic storage information
      // In a real implementation, this would check actual disk usage
      const storageData = {
        database: `${Math.floor(Math.random() * 500) + 100}MB`,
        logs: `${Math.floor(Math.random() * 50) + 10}MB`,
        cache: `${Math.floor(Math.random() * 100) + 20}MB`,
        total: `${Math.floor(Math.random() * 1000) + 500}MB`,
        available: `${Math.floor(Math.random() * 2000) + 1000}MB`,
        used: `${Math.floor(Math.random() * 40) + 20}%`,
        timestamp: new Date().toISOString()
      };

      fastify.log.info('Storage API: Returning storage data:', storageData);

      return {
        success: true,
        data: storageData
      };
    } catch (error) {
      fastify.log.error('Storage API error:', error);
      reply.code(500);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  });

  // Media detail page routes - serve the media.html for media detail pages
  const fs = require('fs');
  const path = require('path');

  const mediaHtmlPath = path.join(__dirname, '..', '..', 'public', 'media.html');

  fastify.get('/movies/:id', async (request, reply) => {
    // Serve the media.html for media detail pages
    reply.type('text/html');
    const html = fs.readFileSync(mediaHtmlPath, 'utf8');
    return reply.send(html);
  });

  fastify.get('/series/:id', async (request, reply) => {
    // Serve the media.html for media detail pages
    reply.type('text/html');
    const html = fs.readFileSync(mediaHtmlPath, 'utf8');
    return reply.send(html);
  });

  fastify.get('/anime/:id', async (request, reply) => {
    // Serve the media.html for media detail pages
    reply.type('text/html');
    const html = fs.readFileSync(mediaHtmlPath, 'utf8');
    return reply.send(html);
  });

  fastify.get('/livetv/:id', async (request, reply) => {
    // Serve the media.html for media detail pages
    reply.type('text/html');
    const html = fs.readFileSync(mediaHtmlPath, 'utf8');
    return reply.send(html);
  });

  // Enhanced cache clear endpoint with admin authentication
  fastify.post('/api/cache/clear/:type?', {
    schema: {
      params: {
        type: 'object',
        properties: {
          type: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          adminToken: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { type } = request.params;
      const { adminToken } = request.body || {};

      // Validate admin token
      if (!adminToken) {
        reply.code(401);
        return { error: 'Admin token required' };
      }

      const session = await request.db.collection('admin').findOne({
        type: 'session',
        token: adminToken,
        expiresAt: { $gt: new Date() }
      });

      if (!session) {
        reply.code(401);
        return { error: 'Invalid or expired admin token' };
      }

      // Clear cache
      let cleared = 0;
      if (request.cacheService) {
        cleared = await request.cacheService.clear(type ? `netstream:${type}:*` : 'netstream:*');
      }

      return {
        success: true,
        message: `Cache ${type || 'all'} cleared`,
        cleared
      };
    } catch (error) {
      fastify.log.error(`Cache clear error: ${error.message}`);
      reply.code(500);
      return { error: 'Failed to clear cache' };
    }
  });

  // Cache stats endpoint
  fastify.get('/api/cache/stats', async (request, reply) => {
    try {
      if (!request.cacheService) {
        return {
          error: 'Cache service not available',
          stats: null
        };
      }

      const stats = await request.cacheService.getStats();
      return stats;
    } catch (error) {
      fastify.log.error(`Cache stats error: ${error.message}`);
      reply.code(500);
      return { error: 'Failed to get cache stats' };
    }
  });

  // Config endpoint
  fastify.get('/config', async (request, reply) => {
    try {
      return {
        tmdbApiKey: process.env.TMDB_API_KEY
      };
    } catch (error) {
      fastify.log.error(`Config error: ${error.message}`);
      reply.code(500);
      return { error: 'Failed to get config' };
    }
  });
}

module.exports = fastifyRoutes;
