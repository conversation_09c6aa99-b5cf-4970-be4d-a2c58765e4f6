import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../lib/mongodb';

export async function GET() {
  try {
    const db = await connectToDatabase();
    const configCollection = db.collection('config');
    
    // Get all configuration values from database
    const configs = await configCollection.find({}).toArray();
    const configMap = configs.reduce((acc, config) => {
      acc[config.key] = config.value;
      return acc;
    }, {});
    
    // Get system settings from environment or defaults
    const systemSettings = {
      maintenanceMode: process.env.MAINTENANCE_MODE === 'true',
      debugMode: process.env.NODE_ENV === 'development',
      autoBackup: process.env.AUTO_BACKUP !== 'false',
      logLevel: process.env.LOG_LEVEL || 'info'
    };

    const configData = {
      baseUrls: {
        wiflix: configMap.WIFLIX_BASE || process.env.WIFLIX_BASE || 'wiflix-max.cam',
        frenchAnime: configMap.FRENCH_ANIME_BASE || process.env.FRENCH_ANIME_BASE || 'french-anime.com',
        witv: configMap.WITV_BASE || process.env.WITV_BASE || 'witv.skin'
      },
      apiKeys: {
        tmdb: configMap.TMDB_API_KEY ? '••••••••••••••••••••••••••••••••' : 'Not configured',
        gemini: configMap.GEMINI_API_KEY ? '••••••••••••••••••••••••••••••••' : 'Not configured'
      },
      scraping: configMap.SCRAPING_CONFIG ? JSON.parse(configMap.SCRAPING_CONFIG) : {
        pages: { movies: 2, series: 2, anime: 2, livetv: 4 },
        enrichment: true,
        gemini: true
      },
      systemSettings,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: configData
    });

  } catch (error) {
    console.error('Config fetch error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to fetch configuration: ${error.message}`
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const db = await connectToDatabase();
    const configCollection = db.collection('config');

    // Handle complex object updates (like scraping config)
    if (body.scraping) {
      const scrapingConfig = {
        key: 'SCRAPING_CONFIG',
        value: JSON.stringify(body.scraping)
      };

      const result = await configCollection.findOneAndUpdate(
        { key: scrapingConfig.key },
        { 
          $set: { 
            value: scrapingConfig.value,
            lastUpdated: new Date()
          }
        },
        { 
          upsert: true, 
          returnDocument: 'after' 
        }
      );

      console.log(`Scraping configuration updated:`, body.scraping);

      return NextResponse.json({
        success: true,
        message: 'Scraping configuration updated successfully',
        data: {
          key: scrapingConfig.key,
          value: body.scraping,
          status: 'updated',
          timestamp: new Date().toISOString()
        }
      });
    }

    // Handle simple key-value updates (legacy format)
    const { key, value } = body;

    if (!key || value === undefined) {
      return NextResponse.json({
        success: false,
        error: 'Key and value are required for simple updates, or provide a scraping object'
      }, { status: 400 });
    }

    // Update or insert configuration
    const result = await configCollection.findOneAndUpdate(
      { key },
      { 
        $set: { 
          value: value.toString(),
          lastUpdated: new Date()
        }
      },
      { 
        upsert: true, 
        returnDocument: 'after' 
      }
    );

    const updateData = {
      key,
      value: value.toString(),
      status: 'updated',
      timestamp: new Date().toISOString()
    };

    console.log(`Configuration updated: ${key} = ${value}`);

    return NextResponse.json({
      success: true,
      message: `Configuration updated successfully: ${key}`,
      data: updateData
    });

  } catch (error) {
    console.error('Config update error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to update configuration: ${error.message}`
    }, { status: 500 });
  }
} 