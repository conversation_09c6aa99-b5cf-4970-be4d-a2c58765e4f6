// scripts/free_ports.js
// <PERSON>ript to free up ports 3000 and 3001 before starting the server

const { killProcessOnPort } = require('./kill_port');

async function freePorts() {
  console.log('Attempting to free up ports 3000 and 3001...');
  
  // Try to free up port 3000 (WebSocket server)
  const port3000Success = await killProcessOnPort(3000);
  console.log(`Port 3000 (WebSocket): ${port3000Success ? 'Freed' : 'Not in use or could not be freed'}`);
  
  // Try to free up port 3001 (Express server)
  const port3001Success = await killProcessOnPort(3001);
  console.log(`Port 3001 (Express): ${port3001Success ? 'Freed' : 'Not in use or could not be freed'}`);
  
  console.log('Port freeing process completed.');
  
  if (port3000Success || port3001Success) {
    console.log('At least one port was successfully freed. You can now start the server.');
  } else {
    console.log('No ports were freed. If you still have issues, you may need to manually kill the processes.');
    console.log('Try running:');
    console.log('  - On Linux/Mac: sudo lsof -i :3000 -i :3001');
    console.log('  - On Windows: netstat -ano | findstr :3000 :3001');
  }
}

// Run the function
freePorts();
