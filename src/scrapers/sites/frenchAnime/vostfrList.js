// File: src/scrapers/sites/frenchAnime/vostfrList.js
const axios = require('axios');
const cheerio = require('cheerio');
const logger = require('../../../utils/logger');
const Config = require('../../../db/models/Config');
const { FRENCH_ANIME_BASE } = require('../../../config/env');

async function scrapeFrenchAnimeVostfrList(totalPages = 1) {
  const animes = [];
  const maxPages = totalPages < 1 ? 1 : totalPages; // Trust the passed value, no re-detection

  for (let page = 1; page <= maxPages; page++) {
    try {
      // Get the latest FRENCH_ANIME_BASE from the database
      const frenchAnimeBase = await Config.getValue('FRENCH_ANIME_BASE', FRENCH_ANIME_BASE);

      const url = `https://${frenchAnimeBase}/animes-vostfr/page/${page}/`;
      logger.info(`Scraping French Anime VOSTFR list from: ${url}`);
      const { data } = await axios.get(url, { timeout: 10000 });
      const $ = cheerio.load(data);

      $('.mov.clearfix').each((i, el) => {
        const title = $(el).find('.mov-t.nowrap').text().trim();
        const detailUrl = $(el).find('.mov-mask').attr('data-link');
        const image = $(el).find('.mov-i img').attr('src');
        const season = $(el).find('.block-sai').text().match(/Saison\s*(\d+)/)?.[1] || '1';
        const episode = $(el).find('.block-ep').text().match(/Episode\s*(\d+)/)?.[1] || '1';
        const animeLanguage = 'VOSTFR';

        if (title && detailUrl) {
          const absoluteDetailUrl = detailUrl.startsWith('http') ? detailUrl : `https://${frenchAnimeBase}${detailUrl}`;
          animes.push({
            title,
            detailUrl: absoluteDetailUrl,
            image: image?.startsWith('http') ? image : `https://${frenchAnimeBase}${image || '/default-image.jpg'}`,
            season,
            episodes: [{ episodeNumber: episode }],
            animeLanguage
          });
        } else {
          logger.warn(`Skipping item on page ${page} due to missing title or detailUrl: title=${title}, detailUrl=${detailUrl}`);
        }
      });

      logger.info(`Scraped French Anime VOSTFR page ${page} - Items: ${animes.length - (page - 1) * 10}`);
      if (animes.length === 0 && page > 1) break; // Stop if no items found on subsequent pages
    } catch (error) {
      if (error.response?.status === 404) break; // Exit on 404 (page not found)
      logger.error(`French Anime VOSTFR scrape error on page ${page}: ${error.message}`);
      break;
    }
  }

  logger.info(`Total VOSTFR animes scraped: ${animes.length}`);
  return animes;
}

module.exports = { scrapeFrenchAnimeVostfrList };