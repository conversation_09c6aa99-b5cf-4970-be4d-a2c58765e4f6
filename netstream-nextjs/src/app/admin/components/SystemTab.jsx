'use client';

import { useState, useEffect } from 'react';
import styles from './admin.module.css';

export default function SystemTab() {
  const [systemHealth, setSystemHealth] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchSystemHealth();
  }, []);

  const fetchSystemHealth = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/system/health');
      if (!response.ok) throw new Error(`System health API returned ${response.status}`);
      const result = await response.json();
      if (result.success) {
        setSystemHealth(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch system health');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestart = async () => {
    if (!confirm('Are you sure you want to restart the server? This will interrupt all current operations.')) {
      return;
    }

    try {
      const response = await fetch('/api/system/restart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) throw new Error(`Restart API returned ${response.status}`);
      const result = await response.json();
      if (result.success) {
        alert('Server restart initiated. Please wait for the system to come back online.');
      } else {
        throw new Error(result.error || 'Failed to restart server');
      }
    } catch (err) {
      setError(err.message);
    }
  };

  const handleBackup = async () => {
    try {
      const response = await fetch('/api/system/backup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) throw new Error(`Backup API returned ${response.status}`);
      const result = await response.json();
      if (result.success) {
        alert(`Database backup completed successfully!\nFilename: ${result.data.filename}\nSize: ${result.data.size}`);
        await fetchSystemHealth(); // Refresh data
      } else {
        throw new Error(result.error || 'Failed to create backup');
      }
    } catch (err) {
      setError(err.message);
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'online':
      case 'secure':
      case 'active':
        return 'text-green-500';
      case 'warning':
      case 'idle':
        return 'text-yellow-500';
      case 'error':
      case 'offline':
      case 'inactive':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div>
      <div className={styles['system-controls']}>
        <button className="button primary" onClick={fetchSystemHealth}>
          <i className="fas fa-sync"></i> Refresh Data
        </button>
        <button className="button warning" onClick={handleBackup}>
          <i className="fas fa-download"></i> Create Backup
        </button>
        <button className="button danger" onClick={handleRestart}>
          <i className="fas fa-power-off"></i> Restart Server
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Server Resources */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-microchip text-blue-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Server Resources</h3>
          </div>
          {isLoading ? (
            <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : systemHealth?.serverResources ? (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">CPU</span>
                <span className="text-lg font-semibold text-blue-500">{systemHealth.serverResources.cpu}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Memory</span>
                <span className="text-lg font-semibold text-green-500">{systemHealth.serverResources.memory}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Disk</span>
                <span className="text-lg font-semibold text-purple-500">{systemHealth.serverResources.disk}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Network</span>
                <span className="text-lg font-semibold text-red-500">{systemHealth.serverResources.network}</span>
              </div>
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No server resources data available</div>
          )}
        </div>

        {/* Storage Usage */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-hdd text-green-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Storage Usage</h3>
          </div>
          {isLoading ? (
            <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : systemHealth?.storageUsage ? (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Database</span>
                <span className="text-lg font-semibold text-blue-500">{systemHealth.storageUsage.database}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Logs</span>
                <span className="text-lg font-semibold text-green-500">{systemHealth.storageUsage.logs}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Cache</span>
                <span className="text-lg font-semibold text-purple-500">{systemHealth.storageUsage.cache}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Available</span>
                <span className="text-lg font-semibold text-yellow-500">{systemHealth.storageUsage.available}</span>
              </div>
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No storage usage data available</div>
          )}
        </div>

        {/* Network Status */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-network-wired text-purple-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Network Status</h3>
          </div>
          {isLoading ? (
            <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : systemHealth?.networkStatus ? (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Status</span>
                <span className={`text-lg font-semibold ${getStatusColor(systemHealth.networkStatus.status)}`}>
                  {systemHealth.networkStatus.status}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Latency</span>
                <span className="text-lg font-semibold text-blue-500">{systemHealth.networkStatus.latency}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Bandwidth</span>
                <span className="text-lg font-semibold text-green-500">{systemHealth.networkStatus.bandwidth}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Connections</span>
                <span className="text-lg font-semibold text-purple-500">{systemHealth.networkStatus.connections}</span>
              </div>
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No network status data available</div>
          )}
        </div>

        {/* Security Status */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-shield-alt text-red-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Security Status</h3>
          </div>
          {isLoading ? (
            <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : systemHealth?.securityStatus ? (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Status</span>
                <span className={`text-lg font-semibold ${getStatusColor(systemHealth.securityStatus.status)}`}>
                  {systemHealth.securityStatus.status}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Threats</span>
                <span className="text-lg font-semibold text-green-500">{systemHealth.securityStatus.threats}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Firewall</span>
                <span className={`text-lg font-semibold ${getStatusColor(systemHealth.securityStatus.firewall)}`}>
                  {systemHealth.securityStatus.firewall}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">SSL</span>
                <span className={`text-lg font-semibold ${getStatusColor(systemHealth.securityStatus.ssl)}`}>
                  {systemHealth.securityStatus.ssl}
                </span>
              </div>
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No security status data available</div>
          )}
        </div>

        {/* Running Processes */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-tasks text-yellow-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Running Processes</h3>
          </div>
          {isLoading ? (
            <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : systemHealth?.runningProcesses ? (
            <div className="space-y-3">
              {systemHealth.runningProcesses.map((process, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <div className="text-sm font-semibold text-white">{process.name}</div>
                    <div className="text-xs text-gray-400">{process.cpu} CPU, {process.memory}</div>
                  </div>
                  <span className={`text-sm font-semibold ${getStatusColor(process.status)}`}>
                    {process.status}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No process data available</div>
          )}
        </div>

        {/* System Errors */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-exclamation-triangle text-red-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">System Errors</h3>
          </div>
          {isLoading ? (
            <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : systemHealth?.systemErrors ? (
            <div className="space-y-3">
              {systemHealth.systemErrors.map((error, index) => (
                <div key={index} className="border-l-4 border-gray-600 pl-3">
                  <div className="flex justify-between items-start">
                    <span className={`text-sm font-semibold ${getStatusColor(error.level)}`}>
                      {error.level.toUpperCase()}
                    </span>
                    <span className="text-xs text-gray-400">
                      {new Date(error.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text-sm text-gray-300 mt-1">{error.message}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No system errors</div>
          )}
        </div>
      </div>

      {/* Database Statistics */}
      {systemHealth?.databaseStats && (
        <div className="bg-gray-800 rounded-lg p-6 mt-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-database text-blue-400 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Database Statistics</h3>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">{systemHealth.databaseStats.totalDocuments}</div>
              <div className="text-gray-400 text-sm">Total Documents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{systemHealth.databaseStats.totalSize}MB</div>
              <div className="text-gray-400 text-sm">Total Size</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-500">{Object.keys(systemHealth.databaseStats.collections).length}</div>
              <div className="text-gray-400 text-sm">Collections</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">{systemHealth.databaseStats.uptime}h</div>
              <div className="text-gray-400 text-sm">Uptime</div>
            </div>
          </div>
        </div>
      )}

      {/* Last Updated */}
      <div className="bg-gray-800 rounded-lg p-4 mt-6">
        <div className="flex items-center justify-between">
          <div className="text-gray-400">
            <i className="fas fa-clock mr-2"></i>
            System Health Status
          </div>
          <div className="text-gray-400 text-sm">
            Last updated: {systemHealth?.timestamp ? new Date(systemHealth.timestamp).toLocaleString() : 'Never'}
          </div>
        </div>
      </div>
    </div>
  );
}
