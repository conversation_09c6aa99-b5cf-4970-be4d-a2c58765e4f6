// File: scripts/validate-migration.js
// Migration validation script to ensure all functionality works correctly
// Comprehensive testing of migrated features

const { MongoClient } = require('mongodb');

class MigrationValidator {
  constructor() {
    this.results = {
      database: {},
      graphql: {},
      api: {},
      performance: {},
      features: {}
    };
    
    this.fastifyPort = process.env.FASTIFY_PORT || 3001;
    this.baseUrl = `http://localhost:${this.fastifyPort}`;
  }

  async validate() {
    console.log('🔍 Starting NetStream Migration Validation\n');
    
    try {
      await this.validateDatabase();
      await this.validateGraphQL();
      await this.validateAPI();
      await this.validatePerformance();
      await this.validateFeatures();
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Validation failed:', error);
      process.exit(1);
    }
  }

  async validateDatabase() {
    console.log('📊 Validating Database Connection...');
    
    try {
      const mongoClient = new MongoClient(process.env.MONGO_URI);
      await mongoClient.connect();
      const db = mongoClient.db();
      
      // Test database operations
      const collections = await db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);
      
      const expectedCollections = ['movies', 'series', 'animes', 'livetv', 'config'];
      const missingCollections = expectedCollections.filter(c => !collectionNames.includes(c));
      
      // Test data counts
      const counts = {};
      for (const collection of expectedCollections) {
        if (collectionNames.includes(collection)) {
          counts[collection] = await db.collection(collection).countDocuments();
        }
      }
      
      await mongoClient.close();
      
      this.results.database = {
        connected: true,
        collections: collectionNames,
        missingCollections,
        counts,
        status: missingCollections.length === 0 ? 'PASS' : 'WARN'
      };
      
      console.log(`   ✅ Database connected with ${collectionNames.length} collections`);
      if (missingCollections.length > 0) {
        console.log(`   ⚠️  Missing collections: ${missingCollections.join(', ')}`);
      }
      
    } catch (error) {
      this.results.database = {
        connected: false,
        error: error.message,
        status: 'FAIL'
      };
      console.log(`   ❌ Database connection failed: ${error.message}`);
    }
  }

  async validateGraphQL() {
    console.log('\n🔗 Validating GraphQL Functionality...');
    
    const queries = [
      {
        name: 'Database Stats',
        query: '{ databaseStats { movies series anime livetv totalItems } }'
      },
      {
        name: 'Config Query',
        query: '{ config { wiflixBase frenchAnimeBase witvBase } }'
      },
      {
        name: 'Available Genres',
        query: '{ availableGenres { movies series anime } }'
      },
      {
        name: 'Latest Movies',
        query: '{ latestMovies(limit: 5) { id title } }'
      },
      {
        name: 'Latest Series',
        query: '{ latestSeries(limit: 5) { id title } }'
      }
    ];

    this.results.graphql = {};
    
    for (const { name, query } of queries) {
      try {
        const response = await fetch(`${this.baseUrl}/graphql`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ query })
        });
        
        const result = await response.json();
        
        if (response.ok && result.data && !result.errors) {
          this.results.graphql[name] = { status: 'PASS', data: result.data };
          console.log(`   ✅ ${name}`);
        } else {
          this.results.graphql[name] = { 
            status: 'FAIL', 
            errors: result.errors || ['Unknown error'] 
          };
          console.log(`   ❌ ${name}: ${result.errors?.[0]?.message || 'Unknown error'}`);
        }
        
      } catch (error) {
        this.results.graphql[name] = { status: 'FAIL', error: error.message };
        console.log(`   ❌ ${name}: ${error.message}`);
      }
    }
  }

  async validateAPI() {
    console.log('\n🌐 Validating API Endpoints...');
    
    const endpoints = [
      { name: 'Health Check', url: '/health', method: 'GET' },
      { name: 'API Info', url: '/api', method: 'GET' },
      { name: 'Cache Stats', url: '/cache/stats', method: 'GET' },
      { name: 'Performance', url: '/performance', method: 'GET' },
      { name: 'Metrics', url: '/metrics', method: 'GET' }
    ];

    this.results.api = {};
    
    for (const { name, url, method } of endpoints) {
      try {
        const response = await fetch(`${this.baseUrl}${url}`, { method });
        
        if (response.ok) {
          const data = url === '/metrics' ? await response.text() : await response.json();
          this.results.api[name] = { status: 'PASS', statusCode: response.status };
          console.log(`   ✅ ${name} (${response.status})`);
        } else {
          this.results.api[name] = { status: 'FAIL', statusCode: response.status };
          console.log(`   ❌ ${name} (${response.status})`);
        }
        
      } catch (error) {
        this.results.api[name] = { status: 'FAIL', error: error.message };
        console.log(`   ❌ ${name}: ${error.message}`);
      }
    }
  }

  async validatePerformance() {
    console.log('\n⚡ Validating Performance...');
    
    const performanceTests = [
      { name: 'Health Check Response Time', url: '/health', threshold: 100 },
      { name: 'GraphQL Response Time', url: '/graphql', threshold: 500, method: 'POST', 
        body: JSON.stringify({ query: '{ databaseStats { totalItems } }' }) },
      { name: 'API Info Response Time', url: '/api', threshold: 50 }
    ];

    this.results.performance = {};
    
    for (const test of performanceTests) {
      try {
        const start = Date.now();
        
        const response = await fetch(`${this.baseUrl}${test.url}`, {
          method: test.method || 'GET',
          headers: test.body ? { 'Content-Type': 'application/json' } : {},
          body: test.body
        });
        
        const duration = Date.now() - start;
        const passed = duration <= test.threshold;
        
        this.results.performance[test.name] = {
          status: passed ? 'PASS' : 'WARN',
          duration,
          threshold: test.threshold,
          statusCode: response.status
        };
        
        const icon = passed ? '✅' : '⚠️';
        console.log(`   ${icon} ${test.name}: ${duration}ms (threshold: ${test.threshold}ms)`);
        
      } catch (error) {
        this.results.performance[test.name] = { status: 'FAIL', error: error.message };
        console.log(`   ❌ ${test.name}: ${error.message}`);
      }
    }
  }

  async validateFeatures() {
    console.log('\n🎯 Validating Key Features...');
    
    const features = [
      { name: 'CORS Headers', test: () => this.testCORS() },
      { name: 'Compression', test: () => this.testCompression() },
      { name: 'Rate Limiting', test: () => this.testRateLimiting() },
      { name: 'Error Handling', test: () => this.testErrorHandling() },
      { name: 'Static Files', test: () => this.testStaticFiles() }
    ];

    this.results.features = {};
    
    for (const feature of features) {
      try {
        const result = await feature.test();
        this.results.features[feature.name] = result;
        
        const icon = result.status === 'PASS' ? '✅' : result.status === 'WARN' ? '⚠️' : '❌';
        console.log(`   ${icon} ${feature.name}: ${result.message || result.status}`);
        
      } catch (error) {
        this.results.features[feature.name] = { status: 'FAIL', error: error.message };
        console.log(`   ❌ ${feature.name}: ${error.message}`);
      }
    }
  }

  async testCORS() {
    const response = await fetch(`${this.baseUrl}/health`, {
      method: 'OPTIONS'
    });
    
    const hasCORS = response.headers.get('access-control-allow-origin') !== null;
    return {
      status: hasCORS ? 'PASS' : 'FAIL',
      message: hasCORS ? 'CORS headers present' : 'CORS headers missing'
    };
  }

  async testCompression() {
    const response = await fetch(`${this.baseUrl}/api`, {
      headers: { 'Accept-Encoding': 'gzip' }
    });
    
    const hasCompression = response.headers.get('content-encoding') === 'gzip';
    return {
      status: hasCompression ? 'PASS' : 'WARN',
      message: hasCompression ? 'Compression enabled' : 'Compression not detected'
    };
  }

  async testRateLimiting() {
    // Make multiple rapid requests to test rate limiting
    const requests = Array(5).fill().map(() => 
      fetch(`${this.baseUrl}/health`)
    );
    
    const responses = await Promise.all(requests);
    const rateLimited = responses.some(r => r.status === 429);
    
    return {
      status: 'PASS', // Rate limiting is optional for health endpoint
      message: rateLimited ? 'Rate limiting active' : 'Rate limiting not triggered'
    };
  }

  async testErrorHandling() {
    const response = await fetch(`${this.baseUrl}/non-existent-endpoint`);
    
    const handles404 = response.status === 404;
    return {
      status: handles404 ? 'PASS' : 'FAIL',
      message: handles404 ? '404 errors handled correctly' : '404 errors not handled'
    };
  }

  async testStaticFiles() {
    const response = await fetch(`${this.baseUrl}/`);
    
    const servesStatic = response.status === 200 || response.status === 304;
    return {
      status: servesStatic ? 'PASS' : 'WARN',
      message: servesStatic ? 'Static files served' : 'Static files not accessible'
    };
  }

  generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('📋 MIGRATION VALIDATION REPORT');
    console.log('='.repeat(80));
    
    const sections = [
      { name: 'Database', results: this.results.database },
      { name: 'GraphQL', results: this.results.graphql },
      { name: 'API Endpoints', results: this.results.api },
      { name: 'Performance', results: this.results.performance },
      { name: 'Features', results: this.results.features }
    ];
    
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;
    let warnings = 0;
    
    for (const section of sections) {
      console.log(`\n${section.name.toUpperCase()}:`);
      
      if (section.results.status) {
        // Single result
        totalTests++;
        if (section.results.status === 'PASS') passedTests++;
        else if (section.results.status === 'FAIL') failedTests++;
        else warnings++;
        
        console.log(`   ${this.getStatusIcon(section.results.status)} ${section.name}`);
      } else {
        // Multiple results
        for (const [test, result] of Object.entries(section.results)) {
          totalTests++;
          if (result.status === 'PASS') passedTests++;
          else if (result.status === 'FAIL') failedTests++;
          else warnings++;
          
          console.log(`   ${this.getStatusIcon(result.status)} ${test}`);
        }
      }
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 SUMMARY:');
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   ✅ Passed: ${passedTests}`);
    console.log(`   ❌ Failed: ${failedTests}`);
    console.log(`   ⚠️  Warnings: ${warnings}`);
    console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests === 0) {
      console.log('\n🎉 Migration validation completed successfully!');
    } else {
      console.log('\n⚠️  Migration validation completed with issues.');
    }
    
    // Save report
    this.saveReport();
    
    console.log('='.repeat(80));
  }

  getStatusIcon(status) {
    switch (status) {
      case 'PASS': return '✅';
      case 'FAIL': return '❌';
      case 'WARN': return '⚠️';
      default: return '❓';
    }
  }

  saveReport() {
    const fs = require('fs');
    const path = require('path');
    
    const reportData = {
      timestamp: new Date().toISOString(),
      results: this.results,
      summary: this.calculateSummary()
    };
    
    try {
      const reportPath = path.join(process.cwd(), 'migration-validation-report.json');
      fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
      console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    } catch (error) {
      console.warn('Failed to save validation report:', error.message);
    }
  }

  calculateSummary() {
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;
    let warnings = 0;
    
    for (const section of Object.values(this.results)) {
      if (section.status) {
        totalTests++;
        if (section.status === 'PASS') passedTests++;
        else if (section.status === 'FAIL') failedTests++;
        else warnings++;
      } else {
        for (const result of Object.values(section)) {
          totalTests++;
          if (result.status === 'PASS') passedTests++;
          else if (result.status === 'FAIL') failedTests++;
          else warnings++;
        }
      }
    }
    
    return {
      totalTests,
      passedTests,
      failedTests,
      warnings,
      successRate: ((passedTests / totalTests) * 100).toFixed(1)
    };
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new MigrationValidator();
  validator.validate().catch(console.error);
}

module.exports = MigrationValidator;
