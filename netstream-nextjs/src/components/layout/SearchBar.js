'use client'
import { useState, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useLazyQuery } from '@apollo/client'
import { SEARCH_MEDIA } from '@/lib/queries'
import { useDebounce } from 'react-use'
import { getImageUrl } from '@/utils/helpers'

export default function SearchBar() {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [results, setResults] = useState([])
  const [isMobile, setIsMobile] = useState(false)
  const router = useRouter()
  const searchRef = useRef(null)

  const [searchMedia, { loading }] = useLazyQuery(SEARCH_MEDIA, {
    onCompleted: (data) => {
      setResults(data.search?.items || [])
    }
  })

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Debounced search
  useDebounce(
    () => {
      if (query.trim().length > 2) {
        searchMedia({ variables: { query: query.trim() } })
        setIsOpen(true)
      } else {
        setResults([])
        setIsOpen(false)
      }
    },
    300,
    [query]
  )

  // Close search on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleResultClick = (item) => {
    const type = item.__typename.toLowerCase()
    // Convert singular to plural for URL routing
    const urlType = type === 'movie' ? 'movies' :
                   type === 'series' ? 'series' :
                   type === 'anime' ? 'anime' : type
    router.push(`/${urlType}/${item.id}`)
    setQuery('')
    setIsOpen(false)
  }

  const showMobileSearch = () => {
    if (window.showMobileSearch) {
      window.showMobileSearch()
    }
  }

  if (isMobile) {
    return (
      <div className="bg-gray-800 p-4 border-b border-gray-700">
        <button
          onClick={showMobileSearch}
          className="w-full flex items-center p-3 bg-gray-700 rounded-lg text-gray-400"
        >
          <i className="fas fa-search mr-3"></i>
          <span>Search movies, series, anime...</span>
        </button>
      </div>
    )
  }

  return (
    <div className="bg-gray-800 p-4 border-b border-gray-700">
      <div className="relative max-w-md" ref={searchRef}>
        <div className="relative">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search movies, series, anime..."
            className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 text-white placeholder-gray-400"
          />
          <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          {loading && (
            <i className="fas fa-spinner fa-spin absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          )}
        </div>

        {/* Search Results */}
        {isOpen && results.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
            {results.map((item) => (
              <button
                key={`${item.__typename}-${item.id}`}
                onClick={() => handleResultClick(item)}
                className="w-full flex items-center p-3 hover:bg-gray-700 transition-colors text-left"
              >
                <img
                  src={getImageUrl(item)}
                  alt={item.title || item.displayTitle}
                  className="w-12 h-16 object-cover rounded mr-3"
                  onError={(e) => {
                    e.target.src = '/default-thumbnail.svg'
                  }}
                />
                <div className="flex-1">
                  <h4 className="font-medium text-white">
                    {item.title || item.displayTitle}
                  </h4>
                  <p className="text-sm text-gray-400">{item.__typename}</p>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
