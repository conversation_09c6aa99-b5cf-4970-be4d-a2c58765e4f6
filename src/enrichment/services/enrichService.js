// File: src/enrichment/services/enrichService.js
const pLimit = require('p-limit');
const axios = require('axios');
const {
  enrichWithTmdb,
  fetchAllTmdbSeasons,
  formatTmdbSeasonsForDB
} = require('../tmdb/tmdbService');
//const { cleanTitle: cleanTitleGemini } = require('../gemini/geminiService');
const { cleanTitleFallback } = require('../regex/regexUtils');
const jikanService = require('../jikan/jikanService');
const logger = require('../../utils/logger');
const Anime = require('../../db/models/Anime');
const { getConfig } = require('../config/enrichmentConfig');

// Get configuration options
const config = getConfig();
const USE_GEMINI = config.USE_GEMINI;
const FETCH_SEASONS = config.FETCH_SEASONS;
const limit = pLimit(config.MAX_CONCURRENT_ENRICHMENTS);

async function cleanTitleRegex(rawTitle, preserveMore = false) {
    try {
        let cleaned = rawTitle
            .replace(/\s*\(\d{4}\)/, '') // Remove year in parentheses (e.g., "(2023)")
            .trim()
            .normalize('NFD').replace(/[\u0300-\u036f]/g, ''); // Normalize accents

        if (!preserveMore) {
            cleaned = cleaned
                .replace(/\s*(saison|season)\s*\d+/i, '') // Remove "Season 1" for series
                .replace(/\s*(Episode\s*\d+|Part\s*\d+)$/i, ''); // Remove "Episode 1" or "Part 1"
        }
        // Preserve subtitles like "The Hustle" or "VOSTFR/VF" if meaningful
        return cleaned || cleanTitleFallback(rawTitle);
    } catch (error) {
        logger.error(`Regex clean error for ${rawTitle}: ${error.message}`);
        return cleanTitleFallback(rawTitle);
    }
}

async function cleanTitle(rawTitle, preserveMore = false) {
    return USE_GEMINI ? await cleanTitleGemini(rawTitle) || cleanTitleFallback(rawTitle) : await cleanTitleRegex(rawTitle, preserveMore);
}

/**
 * Enriches an item with TMDB and/or Jikan data
 * @param {Object} item - The item to enrich
 * @param {string} type - The type of item ('movie', 'series', or 'anime')
 * @param {Object} options - Options for enrichment
 * @param {boolean} options.useGemini - Whether to use Gemini AI for advanced matching
 * @param {boolean} options.fetchSeasons - Whether to fetch seasons data
 * @returns {Promise<Object>} - The enriched item
 */
async function enrichItem(item, type, options = {}) {
    // Set default options
    const useGemini = options.useGemini ?? USE_GEMINI;
    const fetchSeasons = options.fetchSeasons ?? FETCH_SEASONS;

    const rawTitle = item.title;
    const cleanedTitle = await cleanTitle(rawTitle, true); // Preserve more for movies and anime
    const tmdbType = type === 'movie' ? 'movie' : 'tv';
    const tmdbData = await limit(() => enrichWithTmdb(cleanedTitle, tmdbType, item.season || item.metadata?.year));

    logger.info(`Cleaned title: ${rawTitle} -> ${cleanedTitle} (using ${useGemini ? 'Gemini' : 'Regex'})`);
    if (!tmdbData) {
        logger.warn(`No TMDb match for ${cleanedTitle} (type: ${tmdbType}, year: ${item.metadata?.year || 'none'})`);
    } else {
        logger.info(`Enriched ${cleanedTitle} with TMDb ID: ${tmdbData.id}`);
    }

    let jikanData = null;
    if (type === 'anime') {
        try {
            logger.debug(`Attempting Jikan enrichment for ${cleanedTitle} (language: ${item.animeLanguage}, year: ${item.metadata?.year})`);

            // Use the new function that includes seasons data if requested
            if (fetchSeasons) {
                jikanData = await jikanService.enrichAnimeWithJikanDataAndSeasons({ ...item, cleanedTitle }, true);
                if (jikanData && jikanData.jikan && jikanData.jikanSeasons) {
                    logger.info(`Enriched anime ${cleanedTitle} with Jikan data: MAL ID ${jikanData.jikan.mal_id} and ${jikanData.jikanSeasons.length} seasons`);
                } else if (jikanData && jikanData.jikan) {
                    logger.info(`Enriched anime ${cleanedTitle} with Jikan data: MAL ID ${jikanData.jikan.mal_id} but no seasons found`);
                } else {
                    logger.warn(`No Jikan data found for ${cleanedTitle}`);
                }
            } else {
                jikanData = await jikanService.enrichAnimeWithJikanData({ ...item, cleanedTitle });
                if (jikanData && jikanData.jikan) {
                    logger.info(`Enriched anime ${cleanedTitle} with Jikan data: MAL ID ${jikanData.jikan.mal_id}`);
                } else {
                    logger.warn(`No Jikan data found for ${cleanedTitle}`);
                }
            }
        } catch (error) {
            logger.error(`Failed to enrich anime ${cleanedTitle} with Jikan: ${error.message}`);
        }
    }

    // If no data was found, return the item with empty data
    if (!tmdbData && !jikanData) {
        return {
            ...item,
            cleanedTitle,
            tmdb: {},
            jikan: {},
            metadata: item.metadata || {}
        };
    }

    // Format metadata
    const metadata = tmdbData ? {
        synopsis: tmdbData.overview || item.metadata?.synopsis || '',
        actors: tmdbData.credits?.cast?.slice(0, 5).map(c => c.name) || item.metadata?.actors || [],
        year: (tmdbData.release_date || tmdbData.first_air_date)?.split('-')[0] || item.metadata?.year || '',
        genre: tmdbData.genres?.map(g => g.name).join(', ') || item.metadata?.genre || '', // Still used for metadata
        origin: tmdbData.origin_country?.join(', ') || item.metadata?.origin || '',
        creator: tmdbType === 'tv'
            ? tmdbData.created_by?.map(c => c.name).join(', ') || item.metadata?.creator || ''
            : tmdbData.credits?.crew?.find(c => c.job === 'Director')?.name || item.metadata?.creator || ''
    } : item.metadata || {};

    // Format TMDB data
    const tmdb = tmdbData ? {
        id: tmdbData.id,
        title: tmdbData.title,
        overview: tmdbData.overview,
        release_date: tmdbData.release_date || tmdbData.first_air_date,
        poster_path: tmdbData.poster_path,
        vote_average: tmdbData.vote_average,
        genres: tmdbData.genres?.map(g => g.name) || [] // Extract genre names for tmdb.genres
    } : {};

    // Create the enriched item
    const enrichedItem = {
        ...item,
        cleanedTitle,
        tmdb,
        jikan: jikanData?.jikan || {},
        metadata
    };

    // Fetch and add TMDB seasons data if requested and applicable
    if (fetchSeasons && tmdbData && (type === 'series' || type === 'anime')) {
        try {
            logger.info(`Fetching TMDB seasons data for ${cleanedTitle} (TMDB ID: ${tmdbData.id})`);
            const seasonsData = await fetchAllTmdbSeasons(tmdbData.id);

            if (seasonsData && seasonsData.length > 0) {
                const formattedSeasons = formatTmdbSeasonsForDB(seasonsData);
                logger.info(`Found ${formattedSeasons.length} TMDB seasons for ${cleanedTitle}`);

                // Add seasons data to the enriched item
                enrichedItem.tmdbSeasons = formattedSeasons;

                // If we have a specific season number, also set the tmdbSeason field for backward compatibility
                if (item.season) {
                    const seasonNumber = parseInt(item.season, 10);
                    if (!isNaN(seasonNumber)) {
                        const specificSeason = formattedSeasons.find(s => s.season_number === seasonNumber);
                        if (specificSeason) {
                            enrichedItem.tmdbSeason = specificSeason;
                            logger.info(`Set tmdbSeason field for season ${seasonNumber}`);
                        }
                    }
                }
            } else {
                logger.warn(`No TMDB seasons found for ${cleanedTitle} (TMDB ID: ${tmdbData.id})`);
            }
        } catch (error) {
            logger.error(`Error fetching TMDB seasons for ${cleanedTitle}: ${error.message}`);
        }
    }

    // Add Jikan seasons data if it was fetched
    if (jikanData && jikanData.jikanSeasons) {
        enrichedItem.jikanSeasons = jikanData.jikanSeasons;
    }

    return enrichedItem;
}

/**
 * Enriches an item with TMDB and/or Jikan data, using either basic or advanced enrichment
 * @param {Object} item - The item to enrich
 * @param {string} type - The type of item ('movie', 'series', or 'anime')
 * @param {Object} options - Options for enrichment
 * @param {boolean} options.useAdvanced - Whether to use advanced enrichment with Gemini AI
 * @param {boolean} options.useGemini - Whether to use Gemini AI for title cleaning
 * @param {boolean} options.fetchSeasons - Whether to fetch seasons data
 * @returns {Promise<Object>} - The enriched item
 */
async function enrichItemWithOptions(item, type, options = {}) {
    // Dynamically import the advanced enrichment service to avoid circular dependency
    const advancedEnrichService = require('./advancedEnrichService');

    // Check if advanced enrichment is requested and available
    if (options.useAdvanced && advancedEnrichService.isGeminiAvailable()) {
        logger.info(`Using advanced enrichment for ${item.title}`);
        return advancedEnrichService.advancedEnrichItem(item, type, options);
    }

    // Otherwise use basic enrichment
    return enrichItem(item, type, options);
}

module.exports = {
    enrichItem,
    enrichItemWithOptions,
    // Export configuration options for external control
    config: {
        USE_GEMINI,
        FETCH_SEASONS
    },
    // Export the configuration getter
    getConfig
};