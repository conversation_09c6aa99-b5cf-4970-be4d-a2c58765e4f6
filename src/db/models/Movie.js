// NetStream_GraphQL/src/db/models/Movie.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const StreamingUrlSchema = new Schema({
    url: { type: String, required: true },
    provider: { type: String, default: 'unknown' },
    language: { type: String, default: 'unknown' },
    lastChecked: { type: Date, default: Date.now },
    isActive: { type: Boolean, default: true },
    sourceStreamUrl: { type: String, default: null },
    size: { type: String, default: null },   // Added
    type: { type: String, default: null },   // Added (HLS or MP4)
    method: { type: String, default: null } // Added
});

const MetadataSchema = new Schema({
    synopsis: { type: String, default: '' },
    actors: [String],
    year: { type: String, default: '' },
    genre: { type: String, default: '' },
    origin: { type: String, default: '' },
    creator: { type: String, default: '' },
    duration: { type: String, default: '' }
});

const TmdbSchema = new Schema({
    id: Number,
    title: String,
    overview: String,
    release_date: String,
    poster_path: String,
    vote_average: Number,
    genres: [{ type: String }] // Keep as array of strings
});

const MovieSchema = new Schema({
    title: { type: String, required: true },
    detailUrl: { type: String, required: true },
    detailUrlPath: { type: String, required: false },
    cleanedTitle: String,
    thumbnail: { type: String, default: '' }, // Keep for consistency
    image: String,
    streamingUrls: [StreamingUrlSchema],
    metadata: MetadataSchema,
    tmdb: TmdbSchema,
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
}, {
    collection: 'movies',
    timestamps: true // Use timestamps for createdAt and updatedAt
});

// Add pre-save hook to ensure updatedAt is set
MovieSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    next();
});

// Performance Indexes
MovieSchema.index({ updatedAt: -1 }); // For latest queries
MovieSchema.index({ createdAt: -1 }); // For creation-based sorting
MovieSchema.index({ title: 1 }); // For alphabetical sorting
MovieSchema.index({ cleanedTitle: 1 }); // For search optimization
MovieSchema.index({ 'tmdb.id': 1 }); // For TMDB lookups
MovieSchema.index({ 'tmdb.release_date': -1 }); // For release date sorting
MovieSchema.index({ 'tmdb.genres': 1 }); // For genre-based queries
MovieSchema.index({ detailUrlPath: 1 }); // For URL-based lookups
MovieSchema.index({ 'metadata.year': 1 }); // For year-based filtering

// Compound indexes for complex queries
MovieSchema.index({ 'tmdb.genres': 1, updatedAt: -1 }); // Genre + latest
MovieSchema.index({ detailUrl: 1, updatedAt: -1 }); // URL filtering + latest

// Text index for search functionality
MovieSchema.index({
    title: 'text',
    cleanedTitle: 'text',
    'metadata.actors': 'text',
    'tmdb.title': 'text'
}, {
    weights: {
        title: 10,
        cleanedTitle: 8,
        'tmdb.title': 6,
        'metadata.actors': 2
    }
});

module.exports = mongoose.model('Movie', MovieSchema);