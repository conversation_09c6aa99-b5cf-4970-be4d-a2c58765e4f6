'use client'
import { useState, useEffect } from 'react'

export default function HeroSection({ type = 'movies' }) {
  const [currentHero, setCurrentHero] = useState(0)

  const heroData = {
    movies: {
      title: 'Discover Amazing Movies',
      description: 'Explore our vast collection of movies from around the world. From blockbusters to indie gems, find your next favorite film.',
      primaryAction: 'Start Exploring',
      secondaryAction: 'View Trending'
    },
    series: {
      title: 'Binge-Worthy Series',
      description: 'Dive into captivating storylines and unforgettable characters. From drama to comedy, find your next obsession.',
      primaryAction: 'Find Series',
      secondaryAction: 'Popular Now'
    },
    anime: {
      title: 'Anime Universe',
      description: 'Enter the world of anime with epic adventures, heartfelt stories, and stunning animation. Your journey starts here.',
      primaryAction: 'Explore Anime',
      secondaryAction: 'Trending Anime'
    }
  }

  const hero = heroData[type]

  return (
    <div className="relative h-96 bg-gradient-to-r from-blue-900 via-purple-900 to-pink-900 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-blue-500/10 to-purple-500/20"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 flex items-center h-full">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
              {hero.title}
            </h1>
            <p className="text-lg md:text-xl text-gray-200 mb-8 leading-relaxed">
              {hero.description}
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <button 
                onClick={() => document.querySelector('#search-input')?.focus()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
              >
                <i className="fas fa-search mr-3"></i>
                {hero.primaryAction}
              </button>
              <button 
                onClick={() => {
                  const firstCarousel = document.querySelector('.carousel-container')
                  firstCarousel?.scrollIntoView({ behavior: 'smooth' })
                }}
                className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 flex items-center justify-center"
              >
                <i className="fas fa-fire mr-3"></i>
                {hero.secondaryAction}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-gray-900/80 via-transparent to-transparent"></div>
    </div>
  )
}
