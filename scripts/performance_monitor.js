#!/usr/bin/env node

/**
 * NetStream Performance Monitor and Optimizer
 * Monitors application performance and provides optimization recommendations
 */

const mongoose = require('mongoose');
const { performance } = require('perf_hooks');
require('dotenv').config();

// Import models
const Movie = require('../src/db/models/Movie');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');
const LiveTV = require('../src/db/models/LiveTV');

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      database: {},
      memory: {},
      queries: {},
      indexes: {}
    };
  }

  async connect() {
    try {
      const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI || 'mongodb://localhost:27017/NetStream';
      await mongoose.connect(mongoUri);
      console.log('✅ Connected to MongoDB for performance analysis');
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error.message);
      console.error('Make sure MongoDB is running and MONGODB_URI is set in .env file');
      process.exit(1);
    }
  }

  /**
   * Analyze database performance
   */
  async analyzeDatabasePerformance() {
    console.log('\n🔍 Analyzing Database Performance...\n');

    const collections = [
      { name: 'movies', model: Movie },
      { name: 'series', model: Series },
      { name: 'anime', model: Anime },
      { name: 'livetv', model: LiveTV }
    ];

    for (const { name, model } of collections) {
      console.log(`📊 Analyzing ${name} collection:`);

      // Collection stats
      try {
        const stats = await mongoose.connection.db.collection(name).stats();
        console.log(`  - Documents: ${stats.count.toLocaleString()}`);
        console.log(`  - Size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
        console.log(`  - Average document size: ${(stats.avgObjSize / 1024).toFixed(2)} KB`);
      } catch (error) {
        // Fallback to count if stats is not available
        const count = await mongoose.connection.db.collection(name).countDocuments();
        console.log(`  - Documents: ${count.toLocaleString()}`);
        console.log(`  - Size: Unable to determine`);
        console.log(`  - Average document size: Unable to determine`);
      }

      // Index analysis
      const indexes = await mongoose.connection.db.collection(name).indexes();
      console.log(`  - Indexes: ${indexes.length}`);

      // Query performance test
      await this.testQueryPerformance(name, model);

      console.log('');
    }
  }

  /**
   * Test query performance
   */
  async testQueryPerformance(collectionName, model) {
    const queries = [
      {
        name: 'Latest items',
        query: () => model.find({}).sort({ updatedAt: -1 }).limit(20).lean()
      },
      {
        name: 'Search by title',
        query: () => model.find({ title: /test/i }).limit(10).lean()
      },
      {
        name: 'Genre filter',
        query: () => model.find({ 'tmdb.genres': { $exists: true } }).limit(10).lean()
      }
    ];

    for (const { name, query } of queries) {
      const start = performance.now();
      try {
        await query();
        const duration = performance.now() - start;
        console.log(`    ${name}: ${duration.toFixed(2)}ms`);

        if (duration > 1000) {
          console.log(`    ⚠️  Slow query detected (${duration.toFixed(2)}ms)`);
        }
      } catch (error) {
        console.log(`    ❌ ${name}: Error - ${error.message}`);
      }
    }
  }

  /**
   * Analyze missing indexes
   */
  async analyzeMissingIndexes() {
    console.log('\n🔍 Analyzing Missing Indexes...\n');

    const recommendations = [
      {
        collection: 'movies',
        indexes: [
          { fields: { updatedAt: -1 }, reason: 'Latest movies queries' },
          { fields: { 'tmdb.genres': 1 }, reason: 'Genre filtering' },
          { fields: { 'tmdb.release_date': -1 }, reason: 'Release date sorting' },
          { fields: { title: 'text', cleanedTitle: 'text' }, reason: 'Text search' }
        ]
      },
      {
        collection: 'series',
        indexes: [
          { fields: { updatedAt: -1 }, reason: 'Latest series queries' },
          { fields: { 'tmdb.genres': 1 }, reason: 'Genre filtering' },
          { fields: { 'tmdb.first_air_date': -1 }, reason: 'Air date sorting' }
        ]
      },
      {
        collection: 'anime',
        indexes: [
          { fields: { updatedAt: -1 }, reason: 'Latest anime queries' },
          { fields: { 'jikan.genres': 1 }, reason: 'Genre filtering' },
          { fields: { 'jikan.aired.from': -1 }, reason: 'Air date sorting' }
        ]
      }
    ];

    for (const { collection, indexes } of recommendations) {
      console.log(`📋 Recommended indexes for ${collection}:`);

      const existingIndexes = await mongoose.connection.db.collection(collection).indexes();
      const existingIndexNames = existingIndexes.map(idx => JSON.stringify(idx.key));

      for (const { fields, reason } of indexes) {
        const indexKey = JSON.stringify(fields);
        const exists = existingIndexNames.includes(indexKey);

        console.log(`  ${exists ? '✅' : '❌'} ${JSON.stringify(fields)} - ${reason}`);

        if (!exists) {
          console.log(`    💡 Create with: db.${collection}.createIndex(${JSON.stringify(fields)})`);
        }
      }
      console.log('');
    }
  }

  /**
   * Memory usage analysis
   */
  analyzeMemoryUsage() {
    console.log('\n🧠 Memory Usage Analysis...\n');

    const usage = process.memoryUsage();
    console.log(`RSS (Resident Set Size): ${(usage.rss / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Heap Total: ${(usage.heapTotal / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Heap Used: ${(usage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    console.log(`External: ${(usage.external / 1024 / 1024).toFixed(2)} MB`);

    const heapUsagePercent = (usage.heapUsed / usage.heapTotal * 100).toFixed(2);
    console.log(`Heap Usage: ${heapUsagePercent}%`);

    if (heapUsagePercent > 80) {
      console.log('⚠️  High heap usage detected - consider memory optimization');
    }

    if (usage.heapTotal > 512 * 1024 * 1024) { // 512MB
      console.log('⚠️  Large heap size - monitor for memory leaks');
    }
  }

  /**
   * Generate optimization recommendations
   */
  generateRecommendations() {
    console.log('\n💡 Performance Optimization Recommendations...\n');

    const recommendations = [
      {
        category: 'Database',
        items: [
          'Implement compound indexes for frequently used query combinations',
          'Use .lean() for read-only queries to reduce memory usage',
          'Implement field selection to reduce document transfer size',
          'Consider aggregation pipelines for complex queries',
          'Set up database connection pooling with appropriate limits'
        ]
      },
      {
        category: 'Caching',
        items: [
          'Implement Redis for shared caching across instances',
          'Cache GraphQL query results with appropriate TTL',
          'Use CDN for static assets and images',
          'Implement browser caching headers',
          'Cache API responses from TMDB, Jikan, and Gemini'
        ]
      },
      {
        category: 'API Optimization',
        items: [
          'Implement intelligent rate limiting with backoff',
          'Batch API requests where possible',
          'Use request deduplication to avoid duplicate calls',
          'Implement circuit breakers for external APIs',
          'Add request/response compression'
        ]
      },
      {
        category: 'Frontend',
        items: [
          'Implement lazy loading for images and carousels',
          'Use virtual scrolling for large lists',
          'Minimize GraphQL query payloads',
          'Implement client-side caching',
          'Use image optimization and WebP format'
        ]
      },
      {
        category: 'Server',
        items: [
          'Enable gzip compression',
          'Implement HTTP/2 for better multiplexing',
          'Use clustering to utilize multiple CPU cores',
          'Optimize garbage collection settings',
          'Monitor and limit concurrent operations'
        ]
      }
    ];

    recommendations.forEach(({ category, items }) => {
      console.log(`🎯 ${category}:`);
      items.forEach(item => console.log(`  • ${item}`));
      console.log('');
    });
  }

  /**
   * Create performance indexes
   */
  async createPerformanceIndexes() {
    console.log('\n🔧 Creating Performance Indexes...\n');

    const indexOperations = [
      {
        collection: 'movies',
        indexes: [
          { updatedAt: -1 },
          { 'tmdb.genres': 1, updatedAt: -1 },
          { 'tmdb.release_date': -1 },
          { title: 'text', cleanedTitle: 'text', 'tmdb.title': 'text' }
        ]
      },
      {
        collection: 'series',
        indexes: [
          { updatedAt: -1 },
          { 'tmdb.genres': 1, updatedAt: -1 },
          { 'tmdb.first_air_date': -1 }
        ]
      },
      {
        collection: 'anime',
        indexes: [
          { updatedAt: -1 },
          { 'jikan.genres': 1, updatedAt: -1 },
          { 'jikan.aired.from': -1 }
        ]
      },
      {
        collection: 'livetv',
        indexes: [
          { updatedAt: -1 },
          { title: 1 }
        ]
      }
    ];

    for (const { collection, indexes } of indexOperations) {
      console.log(`Creating indexes for ${collection}...`);

      for (const indexSpec of indexes) {
        try {
          await mongoose.connection.db.collection(collection).createIndex(indexSpec);
          console.log(`  ✅ Created index: ${JSON.stringify(indexSpec)}`);
        } catch (error) {
          if (error.code === 85) { // Index already exists
            console.log(`  ℹ️  Index already exists: ${JSON.stringify(indexSpec)}`);
          } else {
            console.log(`  ❌ Failed to create index ${JSON.stringify(indexSpec)}: ${error.message}`);
          }
        }
      }
    }
  }

  /**
   * Run complete performance analysis
   */
  async runAnalysis() {
    console.log('🚀 NetStream Performance Analysis Starting...\n');

    await this.connect();

    this.analyzeMemoryUsage();
    await this.analyzeDatabasePerformance();
    await this.analyzeMissingIndexes();
    this.generateRecommendations();

    console.log('✅ Performance analysis complete!\n');
  }

  /**
   * Run optimization
   */
  async runOptimization() {
    console.log('🔧 NetStream Performance Optimization Starting...\n');

    await this.connect();
    await this.createPerformanceIndexes();

    console.log('✅ Performance optimization complete!\n');
  }
}

// CLI interface
const monitor = new PerformanceMonitor();

const command = process.argv[2];

switch (command) {
  case 'analyze':
    monitor.runAnalysis().then(() => process.exit(0));
    break;
  case 'optimize':
    monitor.runOptimization().then(() => process.exit(0));
    break;
  default:
    console.log('Usage: node performance_monitor.js [analyze|optimize]');
    console.log('  analyze  - Run performance analysis');
    console.log('  optimize - Create performance indexes');
    process.exit(1);
}

module.exports = PerformanceMonitor;
