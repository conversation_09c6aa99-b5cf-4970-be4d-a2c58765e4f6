import HeroSection from '@/components/home/<USER>'
import GenreCarousels from '@/components/media/GenreCarousels'
import WishlistCarousels from '@/components/features/WishlistCarousels'
import TrendingCarousel from '@/components/media/TrendingCarousel'
import LatestCarousel from '@/components/media/LatestCarousel'

export default function SeriesPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <HeroSection type="series" />
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-white mb-8">Series</h1>

        {/* Trending Series Carousel */}
        <TrendingCarousel type="series" />

        {/* Latest Series Carousel */}
        <LatestCarousel type="series" />

        {/* Wishlist Carousel */}
        <WishlistCarousels filterType="series" />

        {/* Genre-based carousels */}
        <GenreCarousels type="series" />
      </div>
    </div>
  )
}
