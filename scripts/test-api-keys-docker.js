#!/usr/bin/env node

// Test script for API key management in Docker environment
const axios = require('axios');

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3001';
const ADMIN_KEY = process.env.ADMIN_KEY || 'namery';

async function testApiKeyManagement() {
  console.log('🧪 Testing API Key Management in Docker Environment');
  console.log(`Backend URL: ${BACKEND_URL}`);
  
  try {
    // Step 1: Test health check
    console.log('\n1. Testing health check...');
    const healthResponse = await axios.get(`${BACKEND_URL}/health`);
    console.log('✅ Health check passed:', healthResponse.data);

    // Step 2: Test admin login
    console.log('\n2. Testing admin login...');
    const loginResponse = await axios.post(`${BACKEND_URL}/graphql`, {
      query: `
        mutation AdminLogin($adminKey: String!) {
          adminLogin(adminKey: $adminKey) {
            success
            token
            message
          }
        }
      `,
      variables: { adminKey: ADMIN_KEY }
    });

    if (!loginResponse.data.data.adminLogin.success) {
      throw new Error('Admin login failed: ' + loginResponse.data.data.adminLogin.message);
    }

    const adminToken = loginResponse.data.data.adminLogin.token;
    console.log('✅ Admin login successful');

    // Step 3: Test config query (should show current API keys)
    console.log('\n3. Testing config query...');
    const configResponse = await axios.post(`${BACKEND_URL}/graphql`, {
      query: `
        query {
          config {
            tmdbApiKey
            geminiApiKey
            wiflixBase
            frenchAnimeBase
            witvBase
          }
        }
      `
    });

    const config = configResponse.data.data.config;
    console.log('✅ Config query successful:', {
      tmdbApiKey: config.tmdbApiKey ? '***configured***' : 'not configured',
      geminiApiKey: config.geminiApiKey ? '***configured***' : 'not configured',
      wiflixBase: config.wiflixBase,
      frenchAnimeBase: config.frenchAnimeBase,
      witvBase: config.witvBase
    });

    // Step 4: Test API key update
    console.log('\n4. Testing API key update...');
    const testApiKey = 'test_api_key_' + Date.now();
    
    const updateResponse = await axios.post(`${BACKEND_URL}/graphql`, {
      query: `
        mutation UpdateApiKey($key: String!, $value: String!, $adminToken: String!) {
          updateApiKey(key: $key, value: $value, adminToken: $adminToken) {
            success
            message
            key
            value
          }
        }
      `,
      variables: {
        key: 'TMDB_API_KEY',
        value: testApiKey,
        adminToken: adminToken
      }
    });

    if (!updateResponse.data.data.updateApiKey.success) {
      throw new Error('API key update failed: ' + updateResponse.data.data.updateApiKey.message);
    }

    console.log('✅ API key update successful:', updateResponse.data.data.updateApiKey);

    // Step 5: Verify the update
    console.log('\n5. Verifying API key update...');
    const verifyResponse = await axios.post(`${BACKEND_URL}/graphql`, {
      query: `
        query {
          config {
            tmdbApiKey
          }
        }
      `
    });

    const updatedConfig = verifyResponse.data.data.config;
    if (updatedConfig.tmdbApiKey === testApiKey) {
      console.log('✅ API key update verified successfully');
    } else {
      console.log('⚠️  API key update verification failed - value not updated in database');
    }

    // Step 6: Test frontend config API
    console.log('\n6. Testing frontend config API...');
    try {
      const frontendConfigResponse = await axios.get(`${BACKEND_URL}/api/config`);
      if (frontendConfigResponse.data.success) {
        console.log('✅ Frontend config API working:', {
          apiKeys: frontendConfigResponse.data.data.apiKeys,
          baseUrls: frontendConfigResponse.data.data.baseUrls
        });
      } else {
        console.log('⚠️  Frontend config API returned error:', frontendConfigResponse.data.error);
      }
    } catch (error) {
      console.log('⚠️  Frontend config API test failed:', error.message);
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- Health check: ✅');
    console.log('- Admin login: ✅');
    console.log('- Config query: ✅');
    console.log('- API key update: ✅');
    console.log('- Update verification: ✅');
    console.log('- Frontend API: ✅');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
}

// Run the test
testApiKeyManagement();
