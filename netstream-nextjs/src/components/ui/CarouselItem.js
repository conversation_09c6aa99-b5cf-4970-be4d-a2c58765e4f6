'use client'
import { useState } from 'react'
import Link from 'next/link'
import { getThumbnailUrl, getTitleWithState, formatRating, getYear, getLanguageEpisodeInfo } from '@/utils/helpers'
import WishlistButton from '@/components/features/WishlistButton'

export default function CarouselItem({ item, type }) {
  const [imageError, setImageError] = useState(false)

  const imageUrl = getThumbnailUrl(item)
  const title = getTitleWithState(item, type)
  const rating = formatRating(item)
  const year = getYear(item)
  const languageEpisodeInfo = getLanguageEpisodeInfo(item, type)

  // Ensure URL uses plural form for routing
  const getUrlType = (type) => {
    const lowerType = type.toLowerCase()
    if (lowerType === 'movie') return 'movies'
    if (lowerType === 'serie') return 'series'
    if (lowerType === 'anima') return 'anime'
    return lowerType // Already plural or other types like 'livetv'
  }

  return (
    <Link href={`/${getUrlType(type)}/${item.id}`}>
      <div className="flex-shrink-0 w-56 group cursor-pointer">
        <div className="relative rounded-xl bg-gray-800 shadow-lg group-hover:shadow-2xl transition-all duration-300 overflow-hidden">
          {/* Image Container - Keep it simple and working */}
          <div className="relative">
            <img
              src={imageError ? '/default-thumbnail.jpg' : imageUrl}
              alt={title}
              className="w-full h-80 object-cover rounded-xl transition-transform duration-500 group-hover:scale-110"
              style={{
                display: 'block',
                position: 'relative',
                zIndex: 1
              }}
              onLoad={(e) => {
                console.log('✅ Image loaded:', {
                  id: item?.id,
                  src: e.target.src,
                  width: e.target.naturalWidth,
                  height: e.target.naturalHeight
                })
              }}
              onError={(e) => {
                console.log('❌ Image error:', {
                  id: item?.id,
                  src: e.target.src
                })
                setImageError(true)
              }}
            />

            {/* Trending Rank Badge - Top priority */}
            {item.trendingRankDisplay && (
              <div
                className="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg"
                style={{ zIndex: 11 }}
              >
                <i className="fas fa-trophy mr-1"></i>
                {item.trendingRankDisplay}
              </div>
            )}

            {/* Rating Badge - Simple overlay */}
            {rating !== 'N/A' && (
              <div
                className={`absolute top-3 ${item.trendingRankDisplay ? 'left-20' : 'left-3'} bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg`}
                style={{ zIndex: 10 }}
              >
                <i className="fas fa-star mr-1"></i>
                {rating}
              </div>
            )}

            {/* Gradient Overlay - Subtle */}
            <div
              className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-xl"
              style={{ zIndex: 2 }}
            ></div>

            {/* Play Button - Center overlay */}
            <div
              className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300"
              style={{ zIndex: 5 }}
            >
              <div className="bg-blue-600 rounded-full p-4 shadow-lg transform scale-75 group-hover:scale-100 transition-transform duration-300">
                <i className="fas fa-play text-white text-2xl ml-1"></i>
              </div>
            </div>

            {/* Info Overlay - Bottom panel */}
            <div
              className="absolute bottom-0 left-0 right-0 p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 rounded-b-xl"
              style={{ zIndex: 6, background: 'linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.7))' }}
            >
              <h3
                className="font-bold text-white text-lg mb-2 line-clamp-2"
                dangerouslySetInnerHTML={{ __html: title }}
              />

              <div className="flex items-center justify-between">
                <span className="text-gray-300 text-sm">{year}</span>
                <span className="text-blue-400 text-sm font-semibold capitalize">{type}</span>
              </div>
            </div>

            {/* Wishlist Button - Simple overlay */}
            <div
              className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300"
              style={{ zIndex: 10 }}
            >
              <WishlistButton item={item} type={type} className="bg-black/50 hover:bg-black/70 backdrop-blur-sm" />
            </div>
          </div>
        </div>

        {/* External Info (Always Visible) */}
        <div className="mt-4 px-1">
          <h3
            className="font-semibold text-white text-base line-clamp-2 group-hover:text-blue-400 transition-colors leading-tight"
            dangerouslySetInnerHTML={{ __html: title }}
          />

          {/* Language/Episode Info - Always visible for series/anime */}
          {languageEpisodeInfo.length > 0 && (
            <div className="mt-2">
              {languageEpisodeInfo.map((info, index) => (
                <span
                  key={index}
                  className="season-episode"
                  dangerouslySetInnerHTML={{ __html: info.display }}
                />
              ))}
            </div>
          )}

          <div className="flex items-center justify-between mt-2">
            <span className="text-gray-400 text-sm">{year}</span>
            <span className="text-gray-500 text-sm capitalize">{type}</span>
          </div>
        </div>
      </div>
    </Link>
  )
}
