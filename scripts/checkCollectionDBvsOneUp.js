// OmniDev reporting for duty.

// Purpose: Checks MongoDB database (Movies, Series, Anime) against a OneUpload account
// via API to identify potentially missing content based on expected folder structures.
// Assumes folders were created like: Movies/[Title], Series/[Title]/Season XX, Animes/[Title]/Season XX.
// *FIXED MongoDB projection error.*

// Usage:
//   node scripts/checkMissingOneUpload.js [options]

// ==================================================================================
//                              DEPENDENCIES
// ==================================================================================
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const logger = require('../src/utils/logger.js'); // Adjust path if needed

// --- Import Mongoose Models ---
const Movie = require("../src/db/models/Movie.js"); // Adjust path as needed
const Series = require("../src/db/models/Series.js"); // Adjust path as needed
const Anime = require("../src/db/models/Anime.js"); // Adjust path as needed

// ==================================================================================
//                              CONFIGURATION
// ==================================================================================
dotenv.config({ path: path.resolve(__dirname, '../.env') });
const { MONGO_URI, ONEUPLOAD_API_KEY } = process.env;

const API_BASE_URL = 'https://oneupload.to/api';
const FOLDER_LIST_API_ENDPOINT = `${API_BASE_URL}/folder/list`;

const MOVIE_FOLDER_NAME = 'Movies';
const SERIES_FOLDER_NAME = 'Series';
const ANIME_FOLDER_NAME = 'Animes';

const REPORT_FILE_NAME = 'missing_content_report.txt';

// --- Command Line Arguments ---
const argv = yargs(hideBin(process.argv))
    .option('movies', { type: 'boolean', description: 'Check Movie collection/folder' })
    .option('series', { type: 'boolean', description: 'Check Series collection/folders' })
    .option('anime', { type: 'boolean', description: 'Check Anime collection/folders' })
    .option('outputDir', { type: 'string', default: './provider_url_output', description: 'Directory for report file' })
    .option('rootFldId', { type: 'number', default: 0, description: 'Parent folder ID for Movies/Series/Animes folders (0 = account root)' })
    .option('delay', { type: 'number', default: 1000, description: 'Delay (ms) between OneUpload API calls.' })
    .help().alias('h', 'help')
    .argv;

// Determine processing parameters
const processAll = !argv.movies && !argv.series && !argv.anime;
const collectionsToProcess = [];
if (processAll || argv.movies) collectionsToProcess.push('Movie');
if (processAll || argv.series) collectionsToProcess.push('Series');
if (processAll || argv.anime) collectionsToProcess.push('Anime');

const OUTPUT_DIRECTORY = path.resolve(argv.outputDir);
const reportFilePath = path.join(OUTPUT_DIRECTORY, REPORT_FILE_NAME);
const ROOT_FOLDER_ID = argv.rootFldId;
const DELAY_BETWEEN_API_MS = argv.delay;

const RATE_LIMIT_STATUS = 403;
const RATE_LIMIT_RETRY_DELAY_MS = 30000;
const MAX_RATE_LIMIT_RETRIES = 3;

// ==================================================================================
//                              DATABASE MODELS & CONFIG (Mapping)
// ==================================================================================
const ALL_MODEL_CONFIG = {
    'Movie': { model: Movie, type: 'media', topLevelFolder: MOVIE_FOLDER_NAME },
    'Series': { model: Series, type: 'series', topLevelFolder: SERIES_FOLDER_NAME },
    'Anime': { model: Anime, type: 'anime', topLevelFolder: ANIME_FOLDER_NAME },
};

const ACTIVE_MODEL_CONFIGS = collectionsToProcess.map(name => {
    const config = ALL_MODEL_CONFIG[name];
    if (!config) return null;
    return { name, ...config };
}).filter(Boolean);

// ==================================================================================
//                              GLOBAL CACHE & State
// ==================================================================================
const folderCache = {};
let missingItems = [];

// ==================================================================================
//                              UTILITY FUNCTIONS
// ==================================================================================
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const ensureDirectoryExists = (dirPath) => { /* ... (copied) ... */ if (!fs.existsSync(dirPath)) { logger.info(`Directory '${dirPath}' does not exist. Creating...`); try { fs.mkdirSync(dirPath, { recursive: true }); logger.info(`Directory created: ${dirPath}`); } catch (error) { logger.error(`Failed to create directory '${dirPath}': ${error.message}`); process.exit(1); } } else { logger.debug(`Directory already exists: ${dirPath}`); } };
const connectDB = async () => { /* ... (copied) ... */ if (!MONGO_URI) { logger.error("MONGO_URI not found."); process.exit(1); } try { logger.info('Connecting to MongoDB...'); await mongoose.connect(MONGO_URI); logger.info('MongoDB Connected.'); } catch (err) { logger.error('MongoDB Connection Error:', err.message); process.exit(1); } };
const sanitizeFolderName = (name) => { /* ... (copied) ... */ if (!name) return 'Untitled'; let sanitized = name.replace(/[<>:"/\\|?*]+/g, ''); sanitized = sanitized.replace(/\s+/g, ' ').replace(/\.+/g, '.').trim(); return sanitized || 'Untitled'; };
const appendToReport = (message) => { try { fs.appendFileSync(reportFilePath, message + '\n', 'utf-8'); } catch (error) { logger.error(`Failed to write to report file ${reportFilePath}: ${error.message}`); } };

// ==================================================================================
//                              API HELPER FUNCTIONS (Modified for Checking)
// ==================================================================================
async function makeApiCall(url, description, retries = 0) { /* ... (copied, includes delay before call and retry logic) ... */ logger.debug(`Waiting ${DELAY_BETWEEN_API_MS}ms before API call: ${description}`); await sleep(DELAY_BETWEEN_API_MS); logger.debug(`API Call (Try ${retries + 1}/${MAX_RATE_LIMIT_RETRIES + 1}): ${description} - URL: ${url}`); try { const response = await fetch(url, { method: 'GET' }); if (response.status === RATE_LIMIT_STATUS && retries < MAX_RATE_LIMIT_RETRIES) { logger.warn(`!!! RATE LIMIT HIT for ${description}. Waiting ${RATE_LIMIT_RETRY_DELAY_MS}ms before retry...`); await sleep(RATE_LIMIT_RETRY_DELAY_MS); return await makeApiCall(url, description, retries + 1); } if (!response.ok) { let errorMsg = `HTTP ${response.status} ${response.statusText}`; try { const body = await response.json(); errorMsg = `API Error (${response.status}): ${body.msg || errorMsg}`; } catch (e) { /* ignore */ } logger.error(`API Call Failed: ${description} - ${errorMsg}`); return { success: false, error: errorMsg, data: null, status: response.status }; } const data = await response.json(); logger.debug(`API Success: ${description} - Response: ${JSON.stringify(data).substring(0, 200)}...`); if (data.status !== 200) { logger.warn(`API Call Warning: ${description} - Status ${data.status}, Msg: ${data.msg}`); if (data.msg?.toLowerCase().includes('limit reached') && retries < MAX_RATE_LIMIT_RETRIES) { logger.warn(`!!! RATE LIMIT indicated by API msg for ${description}. Waiting ${RATE_LIMIT_RETRY_DELAY_MS}ms before retry...`); await sleep(RATE_LIMIT_RETRY_DELAY_MS); return await makeApiCall(url, description, retries + 1); } return { success: false, error: `API Status ${data.status}: ${data.msg || 'Unknown API error'}`, data: data, status: data.status }; } return { success: true, error: null, data: data.result, status: 200 }; } catch (error) { logger.error(`Network/Fetch Error on API Call (${description}): ${error.message}`); return { success: false, error: error.message, data: null, status: null }; } }
async function listFolderApiCall(apiKey, parentFolderId, includeFiles = false) { const params = new URLSearchParams({ key: apiKey, fld_id: parentFolderId }); if (includeFiles) { params.append('files', '1'); } const url = `${FOLDER_LIST_API_ENDPOINT}?${params.toString()}`; return await makeApiCall(url, `List Folder (ID: ${parentFolderId}, Files: ${includeFiles})`); }

// ==================================================================================
//                      FOLDER FINDING LOGIC (No Creation)
// ==================================================================================
async function findFolderId(folderName, parentFolderId, cache, apiKey) { /* ... (copied) ... */ const sanitizedName = sanitizeFolderName(folderName); const lowerCaseName = sanitizedName.toLowerCase(); const cacheKey = `${parentFolderId}_${lowerCaseName}`; if (cache[cacheKey]) { logger.debug(`Cache hit for folder "${sanitizedName}" (Parent: ${parentFolderId}): ID ${cache[cacheKey]}`); return cache[cacheKey]; } logger.info(`Checking for folder "${sanitizedName}" in parent folder ID: ${parentFolderId}`); const listResult = await listFolderApiCall(apiKey, parentFolderId, false); if (listResult.success && listResult.data?.folders) { const existingFolder = listResult.data.folders.find(f => f.name?.toLowerCase() === lowerCaseName); if (existingFolder && typeof existingFolder.fld_id === 'string') { const folderId = parseInt(existingFolder.fld_id, 10); if (!isNaN(folderId)) { logger.info(` -> Found existing folder "${existingFolder.name}" with ID: ${folderId}`); cache[cacheKey] = folderId; return folderId; } else { logger.warn(` -> Found existing folder "${existingFolder.name}" but its ID "${existingFolder.fld_id}" is not a valid number.`); } } else { logger.info(` -> Folder "${sanitizedName}" not found in parent ${parentFolderId}.`); } } else if (!listResult.success) { logger.error(` -> Failed to list folders in parent ${parentFolderId} to find "${sanitizedName}". Error: ${listResult.error}`); } else { logger.info(` -> No subfolders found in parent ${parentFolderId}. Folder "${sanitizedName}" does not exist.`); } cache[cacheKey] = null; return null; }
async function findFolderIdByPath(pathElements, rootFolderId, cache, apiKey) { /* ... (copied) ... */ let currentParentId = rootFolderId; for (const elementName of pathElements) { if (!elementName) continue; const folderId = await findFolderId(elementName, currentParentId, cache, apiKey); if (folderId === null) { logger.warn(` -> Path check failed: Element "${elementName}" not found under parent ${currentParentId}. Full path ${pathElements.join('/')} does not exist.`); return null; } currentParentId = folderId; } return currentParentId; }

// ==================================================================================
//                              CORE CHECKING LOGIC (Fixed Projection)
// ==================================================================================
/**
 * Checks a DB collection against OneUpload folder structure.
 * @param {object} config - Config object { name, model, type, topLevelFolder }
 */
async function checkCollection(config) {
    const { name, model, type, topLevelFolder } = config;
    logger.info(`\n--- Starting Check for ${name} ---`);

    let dbCount = 0;
    let missingFolderCount = 0;
    let missingFileCount = 0;

    // --- Corrected Projection Logic ---
    const projection = {
        _id: 1,
        title: 1,
        cleanedTitle: 1,
    };
    if (type !== 'media') { // Only include season/episodes for series/anime
        projection.season = 1;
        projection.episodes = 1; // Project the entire episodes array
    }
    // --- End Corrected Projection ---

    logger.debug(`[${name}] Using projection: ${JSON.stringify(projection)}`);

    let cursor;
    try {
        // Use the dynamically built projection
        cursor = model.find({}, projection).sort({ _id: 1 }).lean().cursor();
    } catch (queryError) {
        logger.error(`ERROR - Error creating cursor for ${name}: ${queryError.message}\n${queryError.stack}`);
        appendToReport(`ERROR: Failed to query database for ${name}: ${queryError.message}`);
        return; // Stop processing this collection if query fails
    }


    try {
        for await (const item of cursor) {
            dbCount++;
            const itemId = item._id.toString();
            const itemTitle = item.cleanedTitle || item.title;
            logger.debug(`Checking DB item: ${name} - ${itemTitle} (ID: ${itemId})`);

            if (!itemTitle) {
                logger.warn(` -> Skipping DB item ${itemId} due to missing title.`);
                continue;
            }

            let targetFolderId = null;
            let expectedPath = [];
            let missingReason = "";
            let checkContext = { dbItemId: itemId, dbItemTitle: itemTitle };

            // --- Determine expected path and find final folder ID ---
            if (type === 'media') { // Movies: /Movies/Movie Title/
                expectedPath = [topLevelFolder, itemTitle];
                targetFolderId = await findFolderIdByPath(expectedPath, ROOT_FOLDER_ID, folderCache, ONEUPLOAD_API_KEY);
                checkContext.expectedPath = expectedPath.join('/');
                if (targetFolderId === null) {
                    missingFolderCount++;
                    missingReason = `Expected folder path not found`; // Simplified reason
                    logger.warn(` -> MISSING (Folder): ${itemTitle} (${itemId}). Path: ${checkContext.expectedPath}`);
                    missingItems.push({ ...checkContext, reason: missingReason });
                    continue;
                }
            } else if (type === 'series' || type === 'anime') { // Series/Anime
                const showLevelPath = [topLevelFolder, itemTitle];
                const showFolderId = await findFolderIdByPath(showLevelPath, ROOT_FOLDER_ID, folderCache, ONEUPLOAD_API_KEY);

                if (showFolderId === null) {
                    missingFolderCount++; // Maybe count show folder once? Or count all expected eps? Let's count once for folder.
                    missingReason = `Expected show folder path not found`;
                    logger.warn(` -> MISSING (Show Folder): ${itemTitle} (${itemId}). Path: ${showLevelPath.join('/')}. Skipping episodes.`);
                    missingItems.push({ ...checkContext, reason: missingReason, expectedPath: showLevelPath.join('/') });
                    continue;
                }

                if (!item.episodes || item.episodes.length === 0) {
                    logger.debug(` -> DB item ${itemTitle} (${itemId}) has no episodes listed. Skipping file checks.`);
                    // Optionally report shows with no episodes?
                    // missingItems.push({ ...checkContext, reason: "Show folder exists, but no episodes listed in DB", expectedPath: showLevelPath.join('/') });
                    continue;
                }

                const episodesBySeason = {};
                for (const ep of item.episodes) {
                    const seasonNum = ep.season ? String(ep.season).match(/\d+/) : null;
                    const seasonKey = seasonNum ? `Season ${parseInt(seasonNum[0], 10).toString().padStart(2, '0')}` : 'Season Unknown';
                    if (!episodesBySeason[seasonKey]) { episodesBySeason[seasonKey] = []; }
                    episodesBySeason[seasonKey].push(ep);
                }

                for (const [seasonFolderName, episodesInSeason] of Object.entries(episodesBySeason)) {
                    expectedPath = [...showLevelPath, seasonFolderName];
                    targetFolderId = await findFolderIdByPath(expectedPath, ROOT_FOLDER_ID, folderCache, ONEUPLOAD_API_KEY); // Reuse targetFolderId variable
                    checkContext.expectedPath = expectedPath.join('/');

                    if (targetFolderId === null) {
                        missingReason = `Expected season folder path not found`;
                        logger.warn(` -> MISSING (Season Folder): ${itemTitle} - ${seasonFolderName}. Path: ${checkContext.expectedPath}`);
                        episodesInSeason.forEach(ep => {
                            missingItems.push({ dbItemId: itemId, dbItemTitle: itemTitle, season: ep.season, episode: ep.episodeNumber, episodeDbId: ep._id?.toString(), reason: missingReason, expectedPath: checkContext.expectedPath });
                            missingFolderCount++; // Count each episode as missing if season folder absent
                        });
                        continue; // Move to next season
                    }

                    logger.info(` -> Checking files in folder ID: ${targetFolderId} (${checkContext.expectedPath})`);
                    const listResult = await listFolderApiCall(ONEUPLOAD_API_KEY, targetFolderId, true);

                    if (listResult.success && (!listResult.data?.files || listResult.data.files.length === 0)) {
                        missingReason = `Expected season folder exists but contains no files`;
                        logger.warn(` -> MISSING (No Files): ${itemTitle} - ${seasonFolderName}. Path: ${checkContext.expectedPath} (ID: ${targetFolderId})`);
                        episodesInSeason.forEach(ep => {
                            missingItems.push({ dbItemId: itemId, dbItemTitle: itemTitle, season: ep.season, episode: ep.episodeNumber, episodeDbId: ep._id?.toString(), reason: missingReason, expectedPath: checkContext.expectedPath });
                            missingFileCount++; // Count each episode
                        });
                    } else if (!listResult.success) {
                         missingReason = `Failed to list files in expected folder: ${listResult.error}`;
                         logger.error(` -> CHECK FAILED: ${itemTitle} - ${seasonFolderName}. Folder: ${targetFolderId}. Error: ${listResult.error}`);
                        episodesInSeason.forEach(ep => {
                            missingItems.push({ dbItemId: itemId, dbItemTitle: itemTitle, season: ep.season, episode: ep.episodeNumber, episodeDbId: ep._id?.toString(), reason: missingReason, expectedPath: checkContext.expectedPath });
                            // Count these as errors rather than missing? Or both? Let's count as missing file for now.
                            missingFileCount++;
                        });
                    } else {
                        logger.info(` -> OK: Found files in folder ${targetFolderId} for ${itemTitle} - ${seasonFolderName}.`);
                        // Basic check passed. Finer check could involve matching episode numbers/filenames.
                    }
                } // End loop seasons
                continue; // Handled series/anime item
            } // End handling series/anime

            // --- File check for Movies ---
            if (type === 'media' && targetFolderId !== null) {
                logger.info(` -> Checking files in folder ID: ${targetFolderId} (${checkContext.expectedPath})`);
                const listResult = await listFolderApiCall(ONEUPLOAD_API_KEY, targetFolderId, true);

                if (listResult.success && (!listResult.data?.files || listResult.data.files.length === 0)) {
                    missingFileCount++;
                    missingReason = `Expected movie folder exists but contains no files`;
                    logger.warn(` -> MISSING (No Files): ${itemTitle} (${itemId}). Path: ${checkContext.expectedPath} (ID: ${targetFolderId})`);
                    missingItems.push({ ...checkContext, reason: missingReason });
                } else if (!listResult.success) {
                     missingReason = `Failed to list files in expected folder: ${listResult.error}`;
                     logger.error(` -> CHECK FAILED: ${itemTitle}. Folder: ${targetFolderId}. Error: ${listResult.error}`);
                      missingItems.push({ ...checkContext, reason: missingReason });
                      missingFileCount++; // Count as missing if we can't check
                } else {
                    logger.info(` -> OK: Found files in folder ${targetFolderId} for ${itemTitle}.`);
                }
            }

        } // End for await loop
    } catch (error) {
        logger.error(`ERROR - Error processing cursor for ${name}: ${error.message}\n${error.stack}`);
        appendToReport(`ERROR: Cursor processing failed for ${name}: ${error.message}`);
    } finally {
        // Ensure cursor is closed if possible
         if (cursor && !cursor.closed) {
             await cursor.close().catch(e => logger.warn(`Failed to explicitly close cursor for ${name}: ${e.message}`));
         }
        logger.info(`--- Finished Check for ${name}. DB Items Checked: ${dbCount}, Missing Folders Found: ${missingFolderCount}, Empty Folders/Missing Files Found: ${missingFileCount} ---`);
    }
}

// ==================================================================================
//                                  MAIN EXECUTION
// ==================================================================================
async function main() {
    console.log("==========================================================");
    console.log("          Starting OneUpload Missing Content Check        ");
    console.log("==========================================================");

    if (!ONEUPLOAD_API_KEY) { logger.error("ONEUPLOAD_API_KEY not found."); process.exit(1); }
    if (ACTIVE_MODEL_CONFIGS.length === 0) { logger.warn("No collection types specified (--movies, --series, --anime)."); return; }

    logger.info(`Checking types: [${ACTIVE_MODEL_CONFIGS.map(c => c.name).join(', ')}]`);
    logger.info(`Report file: ${reportFilePath}`);
    logger.info(`API Delay: ${DELAY_BETWEEN_API_MS}ms`);
    logger.info(`Root Folder ID: ${ROOT_FOLDER_ID}`);

    ensureDirectoryExists(OUTPUT_DIRECTORY);
    // Initialize report file
    fs.writeFileSync(reportFilePath, `Missing Content Report - ${new Date().toISOString()}\nRoot Folder ID Checked: ${ROOT_FOLDER_ID}\nTop Folders: Movies=${MOVIE_FOLDER_NAME}, Series=${SERIES_FOLDER_NAME}, Animes=${ANIME_FOLDER_NAME}\n${'-'.repeat(60)}\n`, 'utf-8');
    logger.info(`Initialized report file: ${reportFilePath}`);

    await connectDB();

    // Process each specified collection type
    for (const config of ACTIVE_MODEL_CONFIGS) {
        await checkCollection(config);
    }

    logger.info("\n--- All specified collections checked ---");

    // Write summary to report file
    appendToReport(`\n${'-'.repeat(60)}\n--- Summary ---`);
    appendToReport(`Checked Collections: ${ACTIVE_MODEL_CONFIGS.map(c => c.name).join(', ')}`);
    appendToReport(`Total Missing Items/Episodes Logged: ${missingItems.length}`);
    if (missingItems.length > 0) {
        logger.info(`\nFound ${missingItems.length} potentially missing items/episodes. Details logged to: ${reportFilePath}`);
        appendToReport(`\n--- Detailed Missing Items ---`);
        missingItems.forEach(item => {
            let detail = `Type: ${item.dbItemId ? (item.season ? 'Episode' : 'Movie/Show') : 'Unknown'} | DB_ID: ${item.dbItemId || 'N/A'} | Title: "${item.dbItemTitle || 'N/A'}"`;
            if (item.season) detail += ` | S: ${item.season}`;
            if (item.episode) detail += ` | E: ${item.episode}`;
            if (item.expectedPath) detail += ` | Path: ${item.expectedPath}`;
            detail += ` | Reason: ${item.reason || 'Unknown'}`;
            appendToReport(detail);
        });
    } else {
        logger.info("No missing items found based on folder structure and file presence check.");
        appendToReport("No missing items found.");
    }


    try { await mongoose.disconnect(); logger.info("MongoDB connection closed."); }
    catch (closeErr) { logger.error("Error closing MongoDB connection:", closeErr.message); }

    console.log("==========================================================");
    console.log(`                     Check Finished - Report: ${reportFilePath} `);
    console.log("==========================================================");
}

// Run the main function
main().catch(error => {
    logger.error(`Unhandled error in main execution: ${error.message}`);
    logger.error(error.stack);
    mongoose.connection?.close().finally(() => process.exit(1));
});