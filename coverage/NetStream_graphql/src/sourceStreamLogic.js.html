
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for NetStream_graphql/src/sourceStreamLogic.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">NetStream_graphql/src</a> sourceStreamLogic.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/51</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/41</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/5</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/49</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">const fetch = <span class="cstat-no" title="statement not covered" >require('node-fetch');</span>
const https = <span class="cstat-no" title="statement not covered" >require('https');</span>
const fs = <span class="cstat-no" title="statement not covered" >require('fs');</span> // For potential debug file writing
const { PROVIDER_CONFIG } = <span class="cstat-no" title="statement not covered" >require('./config/constants');</span>
&nbsp;
// Import individual fetch methods (adjust paths as needed)
const fetchVidplyStream = <span class="cstat-no" title="statement not covered" >require('../scripts/sourceStreamUrlLogic/vidplyFetchStream');</span>
const fetchUqloadStream = <span class="cstat-no" title="statement not covered" >require('../scripts/sourceStreamUrlLogic/uqloadFetchStream');</span>
const fetchVoeStream = <span class="cstat-no" title="statement not covered" >require('../scripts/sourceStreamUrlLogic/voeFetchStream');</span>
const fetchGenericStream = <span class="cstat-no" title="statement not covered" >require('../scripts/sourceStreamUrlLogic/genericFetchStream');</span>
const fetchStreamtapeStream = <span class="cstat-no" title="statement not covered" >require('../scripts/sourceStreamUrlLogic/streamtapeFetchStream');</span>
&nbsp;
const DEBUG_MODE = <span class="cstat-no" title="statement not covered" >false;</span>  // Control debug file creation
&nbsp;
// Custom axios instance (using node-fetch, but mimicking axios API)
const fetchWithSSLBypass = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >as</span>ync (url, options = <span class="branch-0 cbranch-no" title="branch not covered" >{})</span> =&gt; {</span>
    const httpsAgent = <span class="cstat-no" title="statement not covered" >new https.Agent({ rejectUnauthorized: false });</span>
<span class="cstat-no" title="statement not covered" >    return fetch(url, { ...options, agent: options.agent || (url.startsWith('https') ? httpsAgent : undefined) });</span>
};
&nbsp;
// Copied and adapted verifyDirectUrl from sourceStreamFetcher.js
async function <span class="fstat-no" title="function not covered" >verifyDirectUrl(</span>directUrl, referer, cookieHeader = <span class="branch-0 cbranch-no" title="branch not covered" >'',</span> extraHeaders = <span class="branch-0 cbranch-no" title="branch not covered" >{},</span> retries = <span class="branch-0 cbranch-no" title="branch not covered" >2)</span> {
<span class="cstat-no" title="statement not covered" >    for (let attempt = <span class="cstat-no" title="statement not covered" >0;</span> attempt &lt;= retries; attempt++) {</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >            if (typeof directUrl !== 'string' || !directUrl.startsWith('http')) {</span>
<span class="cstat-no" title="statement not covered" >                throw new Error('Invalid URL format');</span>
            }
&nbsp;
            const headResponse = <span class="cstat-no" title="statement not covered" >await fetchWithSSLBypass(directUrl, {</span>
                method: 'HEAD',
                headers: { 'User-Agent': 'Mozilla/5.0', 'Referer': referer, 'Cookie': cookieHeader, ...extraHeaders },
            });
            const contentType = <span class="cstat-no" title="statement not covered" >headResponse.headers.get('content-type') || '';</span>
            const isValid = <span class="cstat-no" title="statement not covered" >headResponse.status === 200 &amp;&amp; (</span>
                contentType.includes('video') ||
                contentType.includes('application/x-mpegURL') ||
                contentType.includes('application/vnd.apple.mpegurl') ||
                directUrl.match(/\.(mp4|m3u8)$/)
            );
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (!isValid) {</span>
<span class="cstat-no" title="statement not covered" >                throw new Error(`Invalid content type: ${contentType}`);</span>
            }
<span class="cstat-no" title="statement not covered" >            return {</span>
                url: directUrl,
                size: headResponse.headers.get('content-length') || 'Unknown',
                type: contentType.includes('x-mpegURL') || contentType.includes('mpegurl') || directUrl.includes('m3u8') ? 'HLS' : 'MP4'
            };
        } catch (error) {
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (attempt &lt; retries &amp;&amp; (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT')) {</span>
<span class="cstat-no" title="statement not covered" >                await new Promise(<span class="fstat-no" title="function not covered" >re</span>solve =&gt; <span class="cstat-no" title="statement not covered" >setTimeout(resolve, 1000 * (attempt + 1)))</span>;</span>
<span class="cstat-no" title="statement not covered" >                continue;</span>
            }
<span class="cstat-no" title="statement not covered" >            return null; </span>// Return null for failed verification
        }
    }
<span class="cstat-no" title="statement not covered" >    return null; </span>// Return null after all retries
}
&nbsp;
async function <span class="fstat-no" title="function not covered" >getDirectVideoLink(</span>inputUrl, config, cookies) {
    const fetchMethods = <span class="cstat-no" title="statement not covered" >[</span>
        { name: 'PassMd5', fn: fetchVidplyStream },
        { name: 'VOE', fn: fetchVoeStream },
        { name: 'Generic', fn: fetchGenericStream },
        { name: 'Uqload', fn: fetchUqloadStream },
        { name: 'Streamtape', fn: fetchStreamtapeStream }
    ];
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (const method of fetchMethods) {</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
             // logger placeholders, replaced with console.log (adapt as needed)
            const directUrl = <span class="cstat-no" title="statement not covered" >await method.fn(inputUrl, config, cookies, DEBUG_MODE, console.log, console.error);</span>
<span class="cstat-no" title="statement not covered" >            if (directUrl) {</span>
                const verified = <span class="cstat-no" title="statement not covered" >await verifyDirectUrl(directUrl, inputUrl, cookies.cookieHeader, config.headers);</span>
<span class="cstat-no" title="statement not covered" >                if (verified) {</span>
<span class="cstat-no" title="statement not covered" >                    return { ...verified, method: method.name };</span>
                }
            }
        } catch (error) {
<span class="cstat-no" title="statement not covered" >            console.error(`Method ${method.name} failed for ${inputUrl}`, error);</span>
        }
    }
<span class="cstat-no" title="statement not covered" >    return null;</span>
}
&nbsp;
&nbsp;
async function <span class="fstat-no" title="function not covered" >fetchSourceStreamUrl(</span>streamingUrl, provider) {
    const config = <span class="cstat-no" title="statement not covered" >PROVIDER_CONFIG[provider] || { baseUrl: `https://${streamingUrl.split('/')[2]}`, headers: { 'Referer': `https://${streamingUrl.split('/')[2]}/` } };</span>
<span class="cstat-no" title="statement not covered" >    config.provider = provider;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    try {</span>
        const eResponse = <span class="cstat-no" title="statement not covered" >await fetchWithSSLBypass(streamingUrl, {</span>
            headers: { 'User-Agent': 'Mozilla/5.0', ...config.headers },
        });
&nbsp;
&nbsp;
        const setCookieHeader = <span class="cstat-no" title="statement not covered" >eResponse.headers.get('set-cookie');</span>
        const cookies = <span class="cstat-no" title="statement not covered" >{</span>
            eResponse: {
                data: await eResponse.text(), // Get the response body as text
                request: { res: { responseUrl: eResponse.url } }
            },
            cookieHeader: setCookieHeader ? setCookieHeader.split(';')[0] : 'None'
        };
&nbsp;
&nbsp;
        const result = <span class="cstat-no" title="statement not covered" >await getDirectVideoLink(streamingUrl, config, cookies);</span>
<span class="cstat-no" title="statement not covered" >        return result; </span>// Return the result directly
    } catch (error) {
<span class="cstat-no" title="statement not covered" >        console.error(`Failed to fetch embed page for ${streamingUrl}`, error);</span>
<span class="cstat-no" title="statement not covered" >        return null; </span>// Return null on error
    }
}
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >module.exports = { fetchSourceStreamUrl };</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T16:31:40.786Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    