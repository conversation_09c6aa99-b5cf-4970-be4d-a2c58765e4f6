'use client'
import { useState, useEffect } from 'react'
import { useQuery } from '@apollo/client'
import Carousel from '@/components/ui/Carousel'

// Enhanced genre lists with more variety
const COMMON_GENRES = {
  movies: [
    'Action', 'Adventure', 'Animation', 'Comedy', 'Crime', 'Documentary',
    'Drama', 'Family', 'Fantasy', 'History', 'Horror', 'Music', 'Mystery',
    'Romance', 'Science Fiction', 'Thriller', 'War', 'Western'
  ],
  series: [
    'Action & Adventure', 'Animation', 'Comedy', 'Crime', 'Documentary',
    'Drama', 'Family', 'Kids', 'Mystery', 'News', 'Reality', 'Sci-Fi & Fantasy',
    'Soap', 'Talk', 'War & Politics', 'Western'
  ],
  anime: [
    'Action', 'Adventure', 'Comedy', 'Drama', 'Ecchi', 'Fantasy', 'Horror',
    'Mahou Shoujo', 'Mecha', 'Music', 'Mystery', 'Psychological', 'Romance',
    'Sci-Fi', 'Slice of Life', 'Sports', 'Supernatural', 'Thriller'
  ]
}

// GraphQL queries for each type by genre
const GET_MOVIES_BY_GENRE = `
  query GetMoviesByGenre($genre: String!, $page: Int, $limit: Int) {
    moviesByGenre(genre: $genre, page: $page, limit: $limit) {
      id
      title
      displayTitle
      thumbnail
      image
      __typename
      metadata {
        year
        synopsis
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
    }
  }
`

const GET_SERIES_BY_GENRE = `
  query GetSeriesByGenre($genre: String!, $page: Int, $limit: Int) {
    seriesByGenre(genre: $genre, page: $page, limit: $limit) {
      id
      title
      displayTitle
      thumbnail
      image
      __typename
      season
      episodes {
        episodeNumber
        streamingUrls {
          language
        }
      }
      metadata {
        year
        synopsis
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
    }
  }
`

const GET_ANIME_BY_GENRE = `
  query GetAnimeByGenre($genre: String!, $page: Int, $limit: Int) {
    animeByGenre(genre: $genre, page: $page, limit: $limit) {
      id
      title
      displayTitle
      thumbnail
      image
      __typename
      season
      animeLanguage
      episodes {
        episodeNumber
      }
      streamingUrls {
        id
      }
      metadata {
        year
        synopsis
      }
      jikan {
        mal_id
        title {
          default
          english
          japanese
        }
        synopsis
        images {
          jpg {
            image_url
            small_image_url
            large_image_url
          }
        }
        score
        scored_by
        status
        aired {
          from
          to
        }
        episodes
        duration
        rating
        genres {
          mal_id
          name
        }
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
    }
  }
`

export default function GenreCarousels({ type }) {
  const [genres] = useState(COMMON_GENRES[type] || [])

  if (!genres.length) {
    return null
  }

  return (
    <div className="space-y-8">
      {genres.map((genre) => (
        <GenreCarousel
          key={`${type}-${genre}`}
          type={type}
          genre={genre}
        />
      ))}
    </div>
  )
}

function GenreCarousel({ type, genre }) {
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchGenreItems = async () => {
      setLoading(true)
      setError(null)

      try {
        console.log(`🔄 GenreCarousel: Fetching ${genre} ${type}...`)

        // Use our new API route
        const response = await fetch(`/api/genres/${type}?genre=${encodeURIComponent(genre)}`)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        console.log(`📦 GenreCarousel: API result for ${genre} ${type}:`, result)

        if (result.success && result.data) {
          console.log(`✅ GenreCarousel: Setting ${result.data.length} ${genre} ${type} items`)
          setItems(result.data)
        } else {
          console.error(`❌ GenreCarousel: API error for ${genre} ${type}:`, result.error)
          setError(result.error || 'Unknown API error')
        }

      } catch (err) {
        console.error(`💥 GenreCarousel: Fetch error for ${genre} ${type}:`, err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchGenreItems()
  }, [type, genre])

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-2xl font-bold text-white flex items-center">
            <span className="w-1 h-6 bg-blue-500 mr-3 rounded"></span>
            {genre} {type.charAt(0).toUpperCase() + type.slice(1)}
          </h3>
        </div>
        <div className="flex justify-center py-8">
          <i className="fas fa-spinner fa-spin text-xl text-blue-500"></i>
        </div>
      </div>
    )
  }

  if (error || !items.length) {
    return null
  }

  return (
    <Carousel
      title={`${genre} ${type.charAt(0).toUpperCase() + type.slice(1)}`}
      items={items}
      type={type}
    />
  )
}
