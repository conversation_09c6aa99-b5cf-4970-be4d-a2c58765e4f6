'use client'
import { useQuery, useApolloClient } from '@apollo/client'
import { GET_MEDIA_DETAIL, GET_STREAM_URL } from '@/lib/queries'
import { getImageUrl, getTitleWithState, formatRating, getYear } from '@/utils/helpers'
import WishlistButton from '@/components/features/WishlistButton'
import { usePlayer } from '@/context/PlayerContext'
import EpisodeCard from '@/components/common/EpisodeCard'
import React from 'react'

export default function MediaDetailPage({ id, type }) {
  const { openPlayer } = usePlayer()
  const client = useApolloClient();
  const [sourceStates, setSourceStates] = React.useState({});
  const { data, loading, error } = useQuery(GET_MEDIA_DETAIL, {
    variables: {
      id,
      type: type.toUpperCase() // Convert to enum value
    },
    errorPolicy: 'all'
  })

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <i className="fas fa-spinner fa-spin text-4xl text-blue-500"></i>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-500 mb-4">Error</h1>
          <p className="text-gray-400">{error.message}</p>
        </div>
      </div>
    )
  }

  if (!data || !data.item) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-400 mb-4">Not Found</h1>
          <p className="text-gray-500">Media not found</p>
        </div>
      </div>
    )
  }

  const media = data.item
  const imageUrl = getImageUrl(media)
  const title = getTitleWithState(media, type)
  const rating = formatRating(media)
  const year = getYear(media)

  // Get synopsis from various sources
  const synopsis = media.metadata?.synopsis || 
                  media.tmdb?.overview || 
                  media.jikan?.synopsis || 
                  'No synopsis available.'

  // Handle play button click for main media
  const handlePlayClick = () => {
    // For movies and livetv, show source selection if multiple URLs exist
    if ((type === 'movie' || type === 'livetv') && media.streamingUrls && media.streamingUrls.length > 0) {
      if (media.streamingUrls.length === 1) {
        // If only one source, play directly
        openPlayer({
          id: media.id,
          type: type,
          title: title,
          streamId: media.streamingUrls[0].id
        })
      } else {
        // Show source selection modal
        const modal = document.createElement('div')
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'
        modal.innerHTML = `
          <div class="bg-gray-900 rounded-lg p-6 max-w-md w-full">
            <h3 class="text-xl font-semibold text-white mb-4">Select Source</h3>
            <div class="space-y-2">
              ${media.streamingUrls.map((stream, index) => `
                <button 
                  class="w-full text-left px-4 py-2 rounded bg-gray-800 hover:bg-gray-700 text-white transition-colors"
                  onclick="document.querySelector('#source-${index}').click()"
                >
                  ${stream.language || 'Unknown'} - ${stream.quality || 'Auto'}
                </button>
              `).join('')}
            </div>
            <button 
              class="mt-4 w-full px-4 py-2 rounded bg-red-600 hover:bg-red-700 text-white transition-colors"
              onclick="this.closest('.fixed').remove()"
            >
              Cancel
            </button>
          </div>
        `
        document.body.appendChild(modal)
        
        // Add hidden buttons for each source
        media.streamingUrls.forEach((stream, index) => {
          const button = document.createElement('button')
          button.id = `source-${index}`
          button.style.display = 'none'
          button.onclick = () => {
            openPlayer({
              id: media.id,
              type: type,
              title: title,
              streamId: stream.id,
              language: stream.language
            })
            modal.remove()
          }
          document.body.appendChild(button)
        })
      }
    }
    // For series and anime with episodes, play the first episode
    else if ((type === 'series' || type === 'anime') && media.episodes && media.episodes.length > 0) {
      const firstEpisode = media.episodes[0]
      handleEpisodePlay(firstEpisode)
    }
  }

  // Handle play button click for specific episode
  const handleEpisodePlay = (episode) => {
    if (episode.streamingUrls && episode.streamingUrls.length > 0) {
      if (episode.streamingUrls.length === 1) {
        // If only one source, play directly
        openPlayer({
          id: media.id,
          type: type,
          title: `${title} - Episode ${episode.episodeNumber || 'Unknown'}`,
          episodeNumber: episode.episodeNumber,
          streamId: episode.streamingUrls[0].id
        })
      } else {
        // Show source selection modal
        const modal = document.createElement('div')
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'
        modal.innerHTML = `
          <div class="bg-gray-900 rounded-lg p-6 max-w-md w-full">
            <h3 class="text-xl font-semibold text-white mb-4">Select Source for Episode ${episode.episodeNumber || 'Unknown'}</h3>
            <div class="space-y-2">
              ${episode.streamingUrls.map((stream, index) => `
                <button 
                  class="w-full text-left px-4 py-2 rounded bg-gray-800 hover:bg-gray-700 text-white transition-colors"
                  onclick="document.querySelector('#episode-source-${index}').click()"
                >
                  ${stream.language || 'Unknown'} - ${stream.quality || 'Auto'}
                </button>
              `).join('')}
            </div>
            <button 
              class="mt-4 w-full px-4 py-2 rounded bg-red-600 hover:bg-red-700 text-white transition-colors"
              onclick="this.closest('.fixed').remove()"
            >
              Cancel
            </button>
          </div>
        `
        document.body.appendChild(modal)
        
        // Add hidden buttons for each source
        episode.streamingUrls.forEach((stream, index) => {
          const button = document.createElement('button')
          button.id = `episode-source-${index}`
          button.style.display = 'none'
          button.onclick = () => {
            openPlayer({
              id: media.id,
              type: type,
              title: `${title} - Episode ${episode.episodeNumber || 'Unknown'}`,
              episodeNumber: episode.episodeNumber,
              streamId: stream.id,
              language: stream.language
            })
            modal.remove()
          }
          document.body.appendChild(button)
        })
      }
    }
  }

  // Handle play button click for specific streaming URL
  const handleStreamPlay = (stream) => {
    openPlayer({
      id: media.id,
      type: type,
      title: title,
      streamId: stream.id,
      language: stream.language,
      streamMethod: 'iframe',
      videoUrl: stream.url
    })
  }

  // Fetch direct source URL for a movie stream
  const fetchSourceUrl = async (stream) => {
    setSourceStates((prev) => ({ ...prev, [stream.id]: { loading: true, error: null } }));
    try {
      const { data } = await client.query({
        query: GET_STREAM_URL,
        variables: {
          itemId: media.id,
          type: type.toUpperCase(),
          streamId: stream.id,
        },
        fetchPolicy: 'network-only',
      });
      const url = data?.stream?.sourceStreamUrl;
      const method = data?.stream?.method;
      const size = data?.stream?.size;
      const streamType = data?.stream?.type;
      if (url) {
        setSourceStates((prev) => ({
          ...prev,
          [stream.id]: {
            url,
            method,
            size,
            type: streamType,
            loading: false,
            error: null,
            lastFetched: new Date().toISOString(),
          },
        }));
      } else {
        setSourceStates((prev) => ({
          ...prev,
          [stream.id]: {
            loading: false,
            error: 'No direct source found. Try again later.',
            lastFetched: new Date().toISOString(),
          },
        }));
      }
    } catch (err) {
      setSourceStates((prev) => ({
        ...prev,
        [stream.id]: {
          loading: false,
          error: err.message || 'Error fetching source. Try again.',
          lastFetched: new Date().toISOString(),
        },
      }));
    }
  };

  // Play direct source for a movie stream
  const playSource = (stream) => {
    const state = sourceStates[stream.id];
    if (state && state.url) {
      openPlayer({
        id: media.id,
        type: type,
        title: title,
        streamId: stream.id,
        language: stream.language,
        directSourceUrl: state.url,
        method: state.method,
        size: state.size,
        type: state.type,
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <div className="relative h-96 bg-gradient-to-t from-gray-900 to-transparent">
        <img
          src={imageUrl}
          alt={title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-60"></div>
        
        <div className="absolute bottom-0 left-0 right-0 p-8">
          <div className="container mx-auto">
            <div className="flex items-end space-x-6">
              <img
                src={imageUrl}
                alt={title}
                className="w-48 h-72 object-cover rounded-lg shadow-lg"
              />
              
              <div className="flex-1">
                <h1 
                  className="text-4xl font-bold text-white mb-4"
                  dangerouslySetInnerHTML={{ __html: title }}
                />
                
                <div className="flex items-center space-x-4 mb-4">
                  <span className="text-gray-300">{year}</span>
                  {rating !== 'N/A' && (
                    <div className="flex items-center text-yellow-400">
                      <i className="fas fa-star mr-1"></i>
                      <span>{rating}</span>
                    </div>
                  )}
                  <span className="text-gray-400 capitalize">{type}</span>
                </div>
                
                <p className="text-gray-300 text-lg mb-6 max-w-3xl">
                  {synopsis}
                </p>
                
                <div className="flex items-center space-x-4">
                  <button 
                    onClick={handlePlayClick}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                  >
                    <i className="fas fa-play mr-2"></i>
                    Play
                  </button>
                  
                  <WishlistButton 
                    item={media} 
                    type={type} 
                    className="w-12 h-12 text-lg"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="container mx-auto px-4 py-8">
        {/* Episodes/Seasons for Series/Anime */}
        {(type === 'series' || type === 'anime') && media.episodes && media.episodes.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-white mb-4">Episodes</h2>
            <div className="grid gap-4">
              {media.episodes.slice(0, 10).map((episode, index) => (
                <EpisodeCard
                  key={episode.episodeNumber || index}
                  episode={episode}
                  tmdbSeasons={media.tmdbSeasons}
                  openPlayer={openPlayer}
                  mediaId={media.id}
                  mediaType={type}
                  title={title}
                />
              ))}
            </div>
          </div>
        )}

        {/* Streaming URLs */}
        {media.streamingUrls && media.streamingUrls.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-white mb-4">Watch Options</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {media.streamingUrls.map((stream, index) => {
                const state = sourceStates[stream.id] || {};
                return (
                  <div key={stream.id || index} className="bg-gray-800 rounded-lg p-4">
                    <h3 className="text-white font-semibold mb-2">
                      {stream.provider || `Source ${index + 1}`}
                    </h3>
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-sm text-gray-400">
                        {stream.language && <span>{stream.language}</span>}
                        {stream.type && <span> • {stream.type}</span>}
                      </div>
                      <button 
                        onClick={() => handleStreamPlay(stream)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
                      >
                        Watch
                      </button>
                    </div>
                    {/* Fetch Source Button and State (only for movies) */}
                    {type === 'movie' && (
                      <div className="mt-2">
                        <button
                          onClick={() => fetchSourceUrl(stream)}
                          className="bg-cyan-700 hover:bg-cyan-800 text-white px-3 py-1 rounded text-sm mr-2"
                          disabled={state.loading}
                        >
                          {state.loading ? 'Fetching...' : 'Fetch Direct Source'}
                        </button>
                        {state.url && (
                          <button
                            onClick={() => playSource(stream)}
                            className="bg-green-700 hover:bg-green-800 text-white px-3 py-1 rounded text-sm"
                          >
                            Play Direct
                          </button>
                        )}
                        {state.error && (
                          <span className="text-red-400 ml-2 text-xs">{state.error}</span>
                        )}
                        {state.url && (
                          <div className="text-xs text-gray-400 mt-1 break-all">
                            <span className="font-mono">{state.url}</span>
                            <span className="ml-2">[{state.type || ''}{state.method ? `, ${state.method}` : ''}{state.size ? `, ${state.size}` : ''}]</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
