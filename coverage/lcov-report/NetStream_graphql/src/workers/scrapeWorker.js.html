
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for NetStream_graphql/src/workers/scrapeWorker.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">NetStream_graphql/src/workers</a> scrapeWorker.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/95</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/22</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/16</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/91</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// src/workers/scrapeWorker.js - MODIFIED FOR PROTOTYPE TO CLOSE BROWSER
const Bull = <span class="cstat-no" title="statement not covered" >require('bull');</span>
const axios = <span class="cstat-no" title="statement not covered" >require('axios');</span>
const { scrapeAll, SCRAPE_MODE } = <span class="cstat-no" title="statement not covered" >require('../scrapers/services/scrapeService');</span> // Adjust path if needed
const { SCRAPE_INTERVAL } = <span class="cstat-no" title="statement not covered" >require('../config/constants');</span> // Adjust path
const { tmdbApiKey } = <span class="cstat-no" title="statement not covered" >require('../config/env');</span> // Adjust path
const logger = <span class="cstat-no" title="statement not covered" >require('../utils/logger');</span> // Adjust path
const TrendingItem = <span class="cstat-no" title="statement not covered" >require('../db/models/TrendingItem');</span> // Adjust path
const { closeBrowser } = <span class="cstat-no" title="statement not covered" >require('../utils/browserUtils');</span> // &lt;&lt; IMPORT BROWSER UTILS
&nbsp;
// ... (Keep Redis config, queue setup) ...
const redisHost = <span class="cstat-no" title="statement not covered" >process.env.REDIS_HOST || 'localhost';</span>
const redisPort = <span class="cstat-no" title="statement not covered" >process.env.REDIS_PORT || 6379;</span>
const redisPassword = <span class="cstat-no" title="statement not covered" >process.env.REDIS_PASSWORD || undefined;</span>
&nbsp;
const redisConfig = <span class="cstat-no" title="statement not covered" >{</span>
    host: redisHost,
    port: redisPort,
    ...(redisPassword &amp;&amp; { password: redisPassword })
};
&nbsp;
const scrapeQueue = <span class="cstat-no" title="statement not covered" >new Bull('scrape-queue', { redis: redisConfig });</span>
const trendingQueue = <span class="cstat-no" title="statement not covered" >new Bull('trending-updates', { redis: redisConfig });</span>
&nbsp;
&nbsp;
// --- Regular Scrape Job Processing ---
async function <span class="fstat-no" title="function not covered" >processScrapeJob(</span>job) {
    // Scheduled scrapes default to LATEST mode
    const modeToRun = <span class="cstat-no" title="statement not covered" >SCRAPE_MODE.LATEST;</span>
<span class="cstat-no" title="statement not covered" >    logger.info(`Scrape worker processing REGULAR job in '${modeToRun}' mode...`, { jobId: job.id });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
        // scrapeAll in the prototype might call the scripts directly
        // Ensure scrapeAll (or the scripts it calls) handle Puppeteer internally now
<span class="cstat-no" title="statement not covered" >        await scrapeAll(modeToRun); </span>// Make sure this function calls the updated scripts
<span class="cstat-no" title="statement not covered" >        logger.info(`Scrape worker finished REGULAR job successfully.`, { jobId: job.id });</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >         logger.error(`Scrape worker REGULAR job failed: ${error.message}`, { jobId: job.id, stack: error.stack });</span>
<span class="cstat-no" title="statement not covered" >         throw error;</span>
    } finally {
        // Attempt to close browser after each scrape job in case it was left open
<span class="cstat-no" title="statement not covered" >        await closeBrowser().catch(<span class="fstat-no" title="function not covered" >er</span>r =&gt; <span class="cstat-no" title="statement not covered" >logger.error('[Scrape Worker] Error closing browser after job:', err))</span>;</span>
    }
}
&nbsp;
// --- Trending Data Update Job Processing ---
async function <span class="fstat-no" title="function not covered" >updateTrendingData(</span>) {
    // ... (Keep existing trending update logic) ...
<span class="cstat-no" title="statement not covered" >     logger.info('Starting TMDb trending data update job...');</span>
<span class="cstat-no" title="statement not covered" >    if (!tmdbApiKey) {</span>
<span class="cstat-no" title="statement not covered" >        logger.error('TMDb API Key missing, cannot update trending data.');</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
    }
    const mediaTypes = <span class="cstat-no" title="statement not covered" >['movie', 'tv'];</span>
    const timeWindow = <span class="cstat-no" title="statement not covered" >'day';</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
        const deleteResult = <span class="cstat-no" title="statement not covered" >await TrendingItem.deleteMany({ source: 'tmdb' });</span>
<span class="cstat-no" title="statement not covered" >        logger.info(`Cleared ${deleteResult.deletedCount} previous TMDB trending data entries.`);</span>
        let totalAdded = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >        for (const mediaType of mediaTypes) {</span>
            const url = <span class="cstat-no" title="statement not covered" >`https://api.themoviedb.org/3/trending/${mediaType}/${timeWindow}?api_key=${tmdbApiKey}&amp;language=fr-FR`;</span>
<span class="cstat-no" title="statement not covered" >            try {</span>
                const response = <span class="cstat-no" title="statement not covered" >await axios.get(url, { timeout: 15000 });</span>
                const results = <span class="cstat-no" title="statement not covered" >response.data?.results || [];</span>
<span class="cstat-no" title="statement not covered" >                if (results.length &gt; 0) {</span>
                    const itemsToInsert = <span class="cstat-no" title="statement not covered" >results</span>
                        .filter(<span class="fstat-no" title="function not covered" >it</span>em =&gt; <span class="cstat-no" title="statement not covered" >item.id &amp;&amp; typeof item.id === 'number')</span>
                        .map(<span class="fstat-no" title="function not covered" >(i</span>tem, index) =&gt; (<span class="cstat-no" title="statement not covered" >{</span>
                            tmdbId: item.id,
                            mediaType: mediaType,
                            rank: index,
                            source: 'tmdb',
                            fetchedAt: new Date()
                        }));
<span class="cstat-no" title="statement not covered" >                    if (itemsToInsert.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                         await TrendingItem.insertMany(itemsToInsert, { ordered: false });</span>
<span class="cstat-no" title="statement not covered" >                         logger.info(`Inserted ${itemsToInsert.length} trending ${mediaType} items.`);</span>
<span class="cstat-no" title="statement not covered" >                         totalAdded += itemsToInsert.length;</span>
                    }
                } else {
<span class="cstat-no" title="statement not covered" >                    logger.warn(`No trending results received for ${mediaType} from TMDb.`);</span>
                }
<span class="cstat-no" title="statement not covered" >                 await new Promise(<span class="fstat-no" title="function not covered" >re</span>solve =&gt; <span class="cstat-no" title="statement not covered" >setTimeout(resolve, 250))</span>;</span>
<span class="cstat-no" title="statement not covered" >                 await new Promise(<span class="fstat-no" title="function not covered" >re</span>solve =&gt; <span class="cstat-no" title="statement not covered" >setTimeout(resolve, 500))</span>;</span>
            } catch (fetchError) {
<span class="cstat-no" title="statement not covered" >                 logger.error(`Error fetching trending ${mediaType} from TMDb: ${fetchError.message}`, { status: fetchError.response?.status });</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        logger.info(`Finished TMDb trending data update job. Total items added: ${totalAdded}`);</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >        logger.error(`General error during trending data update: ${error.message}`, { stack: error.stack });</span>
<span class="cstat-no" title="statement not covered" >        throw error;</span>
    }
}
&nbsp;
// --- Worker Setup and Scheduling ---
async function <span class="fstat-no" title="function not covered" >setupWorkers(</span>) {
<span class="cstat-no" title="statement not covered" >  try {</span>
    // Connect DB logic might be elsewhere in prototype (e.g., server.js)
    // Ensure DB is connected before processing starts
&nbsp;
    // 1. Setup Regular Scrape Worker
<span class="cstat-no" title="statement not covered" >    scrapeQueue.process(processScrapeJob);</span>
    // ... (Keep repeatable job setup logic from prototype's worker) ...
    const scrapeRepeatableJobs = <span class="cstat-no" title="statement not covered" >await scrapeQueue.getRepeatableJobs();</span>
<span class="cstat-no" title="statement not covered" >    for (const job of scrapeRepeatableJobs) { /* ... remove old ... */ }</span>
<span class="cstat-no" title="statement not covered" >    await scrapeQueue.add({}, { repeat: { every: SCRAPE_INTERVAL } }); </span>// Use SCRAPE_INTERVAL from constants
<span class="cstat-no" title="statement not covered" >    logger.info(`Regular scrape worker processor set up.`);</span>
&nbsp;
    // 2. Setup Trending Update Worker
<span class="cstat-no" title="statement not covered" >    trendingQueue.process(updateTrendingData);</span>
    // ... (Keep repeatable job setup logic from prototype's worker) ...
     const trendingRepeatableJobs = <span class="cstat-no" title="statement not covered" >await trendingQueue.getRepeatableJobs();</span>
<span class="cstat-no" title="statement not covered" >     for (const job of trendingRepeatableJobs) { /* ... remove old ... */ }</span>
<span class="cstat-no" title="statement not covered" >     await trendingQueue.add({}, { repeat: { every: 3 * 60 * 60 * 1000 } }); </span>// Example: 3 hours
<span class="cstat-no" title="statement not covered" >    logger.info(`Trending update worker processor set up.`);</span>
&nbsp;
    // Optional initial trending update trigger
<span class="cstat-no" title="statement not covered" >    setTimeout(<span class="fstat-no" title="function not covered" >()</span> =&gt; { <span class="cstat-no" title="statement not covered" >trendingQueue.add({}); </span>}, 15000);</span>
&nbsp;
    // Event listeners
<span class="cstat-no" title="statement not covered" >    [scrapeQueue, trendingQueue].forEach(<span class="fstat-no" title="function not covered" >qu</span>eue =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        queue.on('completed', <span class="fstat-no" title="function not covered" >(j</span>ob) =&gt; { /* ... log ... */ });</span>
<span class="cstat-no" title="statement not covered" >        queue.on('failed', <span class="fstat-no" title="function not covered" >(j</span>ob, err) =&gt; { /* ... log ... */ });</span>
<span class="cstat-no" title="statement not covered" >        queue.on('error', <span class="fstat-no" title="function not covered" >(e</span>rror) =&gt; { /* ... log ... */ });</span>
    });
&nbsp;
  } catch (error) {
<span class="cstat-no" title="statement not covered" >    logger.error(`Error setting up Bull workers: ${error.message}`, { stack: error.stack });</span>
  }
}
&nbsp;
// Export function to start workers if needed by prototype's main process
<span class="cstat-no" title="statement not covered" >module.exports = {</span>
  startWorkers: setupWorkers,
  scrapeQueue,
  trendingQueue,
  updateTrendingData // Export the updateTrendingData function
};
&nbsp;
// Add graceful shutdown for the worker process if run standalone
async function <span class="fstat-no" title="function not covered" >gracefulShutdown(</span>) {
<span class="cstat-no" title="statement not covered" >    logger.info('Shutting down prototype scrape worker...');</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >        await Promise.all([</span>
            scrapeQueue.close(),
            trendingQueue.close(),
            closeBrowser() // &lt;&lt;&lt; CLOSE BROWSER
        ]);
        // Disconnect DB if connection managed here
<span class="cstat-no" title="statement not covered" >         if (require('mongoose').connection.readyState === 1) {</span>
<span class="cstat-no" title="statement not covered" >           await require('mongoose').disconnect();</span>
<span class="cstat-no" title="statement not covered" >           logger.info('MongoDB disconnected via worker shutdown.');</span>
         }
<span class="cstat-no" title="statement not covered" >        logger.info('Prototype worker shut down gracefully.');</span>
<span class="cstat-no" title="statement not covered" >        process.exit(0);</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >        logger.error('Error during prototype worker shutdown:', error);</span>
<span class="cstat-no" title="statement not covered" >        process.exit(1);</span>
    }
}
&nbsp;
// Listen for shutdown signals if this file can be run directly
<span class="cstat-no" title="statement not covered" >if (require.main === module) {</span>
     // Ensure DB connection first if run standalone
<span class="cstat-no" title="statement not covered" >     require('dotenv').config();</span>
     const mongoose = <span class="cstat-no" title="statement not covered" >require('mongoose');</span>
     const { mongoUri } = <span class="cstat-no" title="statement not covered" >require('../config/env');</span> // Corrected path
<span class="cstat-no" title="statement not covered" >     mongoose.connect(mongoUri).then(<span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >         logger.info('Standalone worker connected to DB.');</span>
<span class="cstat-no" title="statement not covered" >         setupWorkers();</span>
     }).catch(<span class="fstat-no" title="function not covered" >er</span>r =&gt; {
<span class="cstat-no" title="statement not covered" >          logger.error('Standalone worker DB connection failed:', err);</span>
<span class="cstat-no" title="statement not covered" >          process.exit(1);</span>
     });
&nbsp;
<span class="cstat-no" title="statement not covered" >    process.on('SIGTERM', gracefulShutdown);</span>
<span class="cstat-no" title="statement not covered" >    process.on('SIGINT', gracefulShutdown);</span>
}</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T16:31:40.526Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    