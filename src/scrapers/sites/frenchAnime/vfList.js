// File: src/scrapers/sites/frenchAnime/vfList.js
const axios = require('axios');
const cheerio = require('cheerio');
const logger = require('../../../utils/logger');
const Config = require('../../../db/models/Config');
const { FRENCH_ANIME_BASE } = require('../../../config/env');

async function scrapeFrenchAnimeVfList(totalPages = 1) {
  const animes = [];
  const maxPages = totalPages < 1 ? 1 : totalPages; // Trust the passed value, no re-detection
  
  // Validate and sanitize totalPages
  if (typeof maxPages !== 'number' || isNaN(maxPages)) {
    logger.error('Invalid page limit provided to scrapeFrenchAnimeVfList');
    return [];
  }

  for (let page = 1; page <= maxPages; page++) {
    try {
      // Get the latest FRENCH_ANIME_BASE from the database
      const frenchAnimeBase = await Config.getValue('FRENCH_ANIME_BASE', FRENCH_ANIME_BASE);

      const url = `https://${frenchAnimeBase}/animes-vf/page/${page}/`;
      logger.info(`Scraping French Anime VF list from: ${url}`);
      const { data } = await axios.get(url, { timeout: 10000 });
      const $ = cheerio.load(data);

      $('.mov.clearfix').each((i, el) => {
        const title = $(el).find('.mov-t.nowrap').text().trim();
        const detailUrl = $(el).find('.mov-mask').attr('data-link');
        const image = $(el).find('.mov-i img').attr('src');
        const season = $(el).find('.block-sai').text().match(/Saison\s*(\d+)/)?.[1] || '1';
        const episode = $(el).find('.block-ep').text().match(/Episode\s*(\d+)/)?.[1] || '1';
        const animeLanguage = 'VF';

        if (title && detailUrl) {
          const absoluteDetailUrl = detailUrl.startsWith('http') ? detailUrl : `https://${frenchAnimeBase}${detailUrl}`;
          // Create a sanitized anime object
          const animeObj = {
            title: String(title),
            detailUrl: absoluteDetailUrl,
            image: image?.startsWith('http') ? image : `https://${frenchAnimeBase}${image || '/default-image.jpg'}`,
            season: String(season),
            episodes: [{ episodeNumber: String(episode) }],
            animeLanguage: String(animeLanguage)
          };

          // Validate the object before pushing
          if (animeObj.title && animeObj.detailUrl) {
            animes.push(animeObj);
          } else {
            logger.warn(`Skipping invalid anime object: ${JSON.stringify(animeObj)}`);
          }
        }
      });

      // Log the results in a safe way
      const newItems = animes.length - (page - 1) * 10;
      logger.info(`Scraped French Anime VF page ${page} - Items: ${newItems}`);
      
      if (animes.length === 0 && page > 1) {
        logger.info('No more items found, stopping pagination');
        break;
      }
    } catch (error) {
      if (error.response?.status === 404) {
        logger.info('Reached last page (404), stopping pagination');
        break;
      }
      
      // Create a custom error with safe information
      const err = new Error(`French Anime VF scrape error on page ${page}: ${error.message}`);
      err.animes = animes; // Attach the animes array for debug logging
      throw err;
    }
  }
  logger.info(`Total VF animes scraped: ${animes.length}`);
  return animes;
}

module.exports = { scrapeFrenchAnimeVfList };