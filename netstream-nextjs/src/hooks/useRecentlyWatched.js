'use client'
import { useState, useEffect, useCallback } from 'react'

export function useRecentlyWatched() {
  const [recentlyWatched, setRecentlyWatched] = useState([])
  const [groupedRecentlyWatched, setGroupedRecentlyWatched] = useState({
    movies: [],
    series: [],
    anime: []
  })

  // Load recently watched from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('recentlyWatched')
      if (saved) {
        try {
          const items = JSON.parse(saved)
          setRecentlyWatched(items)
          groupItems(items)
        } catch (error) {
          console.error('Error loading recently watched:', error)
        }
      }
    }
  }, [])

  const groupItems = useCallback((items) => {
    const grouped = {
      movies: items.filter(item => item.type === 'movie'),
      series: items.filter(item => item.type === 'series'),
      anime: items.filter(item => item.type === 'anime')
    }
    setGroupedRecentlyWatched(grouped)
  }, [])

  const saveToStorage = useCallback((items) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('recentlyWatched', JSON.stringify(items))
    }
  }, [])

  const addToRecentlyWatched = useCallback((item) => {
    const newItem = {
      id: item.id,
      title: item.title || item.displayTitle,
      thumbnail: item.thumbnail || item.image,
      type: item.type,
      year: item.year || item.metadata?.year,
      watchedAt: new Date().toISOString(),
      progress: item.progress || 0,
      duration: item.duration || 0
    }

    setRecentlyWatched(prev => {
      // Remove existing entry if it exists
      const filtered = prev.filter(existing => 
        !(existing.id === newItem.id && existing.type === newItem.type)
      )
      
      // Add to beginning and limit to 50 items
      const updated = [newItem, ...filtered].slice(0, 50)
      saveToStorage(updated)
      groupItems(updated)
      return updated
    })
  }, [saveToStorage, groupItems])

  const updateProgress = useCallback((id, type, progress, duration) => {
    setRecentlyWatched(prev => {
      const updated = prev.map(item => {
        if (item.id === id && item.type === type) {
          return {
            ...item,
            progress,
            duration,
            watchedAt: new Date().toISOString()
          }
        }
        return item
      })
      saveToStorage(updated)
      groupItems(updated)
      return updated
    })
  }, [saveToStorage, groupItems])

  const removeFromRecentlyWatched = useCallback((id, type) => {
    setRecentlyWatched(prev => {
      const updated = prev.filter(item => !(item.id === id && item.type === type))
      saveToStorage(updated)
      groupItems(updated)
      return updated
    })
  }, [saveToStorage, groupItems])

  const clearRecentlyWatched = useCallback(() => {
    setRecentlyWatched([])
    setGroupedRecentlyWatched({ movies: [], series: [], anime: [] })
    if (typeof window !== 'undefined') {
      localStorage.removeItem('recentlyWatched')
    }
  }, [])

  return {
    recentlyWatched,
    groupedRecentlyWatched,
    addToRecentlyWatched,
    updateProgress,
    removeFromRecentlyWatched,
    clearRecentlyWatched
  }
}
