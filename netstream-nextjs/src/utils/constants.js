export const MEDIA_TYPES = {
  MOVIE: 'movie',
  SERIES: 'series', 
  ANIME: 'anime',
  LIVETV: 'livetv'
}

export const SORT_OPTIONS = {
  LATEST: 'LATEST',
  TRENDING: 'TRENDING',
  ALPHABETICAL: 'ALPHABETICAL',
  RATING: 'RATING',
  YEAR: 'YEAR'
}

export const QUALITY_LEVELS = {
  AUTO: 'auto',
  SD: '480p',
  HD: '720p',
  FHD: '1080p',
  UHD: '4K'
}

export const LANGUAGES = {
  ENGLISH: 'en',
  FRENCH: 'fr',
  JAPANESE: 'ja',
  UNKNOWN: 'unknown'
}

export const PLAYER_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  PLAYING: 'playing',
  PAUSED: 'paused',
  ENDED: 'ended',
  ERROR: 'error'
}

export const API_ENDPOINTS = {
  GRAPHQL: '/graphql',
  PROXY_IMAGE: '/proxy-image',
  PROXY_VIDEO: '/proxy-video',
  PROXY_SUBTITLE: '/proxy-subtitle'
}
