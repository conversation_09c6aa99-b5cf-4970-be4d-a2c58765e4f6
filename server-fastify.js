// File: server-fastify.js
// NetStream Fastify Server - High Performance Migration
// Phase 1: Core Framework Setup

require("dotenv").config();
const fastify = require('fastify')({
  logger: {
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    transport: process.env.NODE_ENV !== 'production' ? {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname'
      }
    } : undefined
  },
  trustProxy: true,
  bodyLimit: 1048576 * 10, // 10MB
  keepAliveTimeout: 30000,
  connectionTimeout: 30000,
  requestTimeout: 30000,
  disableRequestLogging: process.env.NODE_ENV === 'production'
});

const path = require('path');
const { MongoClient } = require('mongodb');
const fs = require('fs');

// Import configuration
const { mongoUri, port } = require('./src/config/env');

// Import services
const FastifyCache = require('./src/cache/fastifyCache');
const FastifyDbService = require('./src/db/fastifyDbService');
const FastifyDataLoaders = require('./src/graphql/dataLoaders');

// Import workers and scraping services with error handling
let startWorkers, scrapeService, SCRAPE_MODE, logger;

try {
  const workerModule = require('./src/workers/scrapeWorker');
  startWorkers = workerModule.startWorkers;
} catch (error) {
  console.warn('Scrape worker not available:', error.message);
  startWorkers = async () => { console.log('Scrape workers disabled - dependencies not available'); };
}

try {
  scrapeService = require('./src/scrapers/services/scrapeService');
} catch (error) {
  console.warn('Scrape service not available:', error.message);
  scrapeService = null;
}

try {
  const constants = require('./src/config/constants');
  SCRAPE_MODE = constants.SCRAPE_MODE;
} catch (error) {
  console.warn('Constants not available:', error.message);
  SCRAPE_MODE = { LATEST: 'latest', FULL: 'full' };
}

try {
  logger = require('./src/utils/logger');
} catch (error) {
  console.warn('Logger not available:', error.message);
  logger = console;
}

// Global variables for database and services
let mongoClient;
let db;
let cacheService;
let dbService;
let dataLoaders;

// Graceful shutdown handler
const gracefulShutdown = async (signal) => {
  fastify.log.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    // Close Fastify server
    await fastify.close();
    
    // Close MongoDB connection
    if (mongoClient) {
      await mongoClient.close();
      fastify.log.info('MongoDB connection closed');
    }

    // Close cache service
    if (cacheService) {
      await cacheService.disconnect();
      fastify.log.info('Cache service disconnected');
    }
    
    fastify.log.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    fastify.log.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Register shutdown handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Database connection function
async function connectDatabase() {
  try {
    mongoClient = new MongoClient(mongoUri, {
      maxPoolSize: 50,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 10000,
      retryWrites: true,
      retryReads: true
    });

    await mongoClient.connect();
    db = mongoClient.db();
    
    fastify.log.info('Connected to MongoDB successfully');

    // Test the connection
    await db.admin().ping();
    fastify.log.info('MongoDB ping successful');

    // Initialize services
    dbService = new FastifyDbService(db);
    dataLoaders = new FastifyDataLoaders(db);

    // Ensure database indexes for performance
    await dbService.ensureIndexes();
    fastify.log.info('Database indexes ensured');

    return { mongoClient, db };
  } catch (error) {
    fastify.log.error('Failed to connect to MongoDB:', error);
    throw error;
  }
}

// Initialize cache service
async function initializeCacheService() {
  try {
    cacheService = new FastifyCache();

    // Test cache connection
    const healthCheck = await cacheService.healthCheck();
    if (healthCheck.status === 'healthy') {
      fastify.log.info('Cache service initialized successfully');
      return cacheService;
    } else {
      fastify.log.warn('Cache service health check failed, continuing without cache');
      cacheService = null;
      return null;
    }
  } catch (error) {
    fastify.log.error('Failed to initialize cache service:', error);
    // Continue without cache in case of Redis connection issues
    cacheService = null;
    return null;
  }
}

// Register essential plugins
async function registerPlugins() {
  // Initialize cache service first
  await initializeCacheService();

  // Redis plugin (if cache service is available)
  if (cacheService) {
    // Determine Redis host based on environment
    let redisHost = process.env.REDIS_HOST;
    if (!redisHost) {
      if (process.env.NODE_ENV === 'production') {
        // In production, try to use the Redis service URL if available
        redisHost = process.env.REDIS_URL || process.env.REDIS_SERVICE_URL || 'localhost';
      } else {
        redisHost = 'localhost';
      }
    }

    await fastify.register(require('@fastify/redis'), {
      host: redisHost,
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      family: 4,
      keepAlive: true,
      lazyConnect: true,
      connectTimeout: 10000, // 10 second connection timeout
      commandTimeout: 5000, // 5 second command timeout
      maxRetriesPerRequest: 2 // Reduced retries
    });
  }

  // CORS support
  await fastify.register(require('@fastify/cors'), {
    origin: (origin, callback) => {
      const allowedOrigins = [
        'http://localhost:3000',
        'http://localhost:3001',
        process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
        process.env.PRODUCTION_URL
      ].filter(Boolean);

      // Allow requests with no origin (mobile apps, curl, etc.)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }
      
      // Allow all origins in development
      if (process.env.NODE_ENV !== 'production') {
        return callback(null, true);
      }
      
      return callback(new Error('Not allowed by CORS'), false);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
  });

  // Compression
  await fastify.register(require('@fastify/compress'), {
    global: true,
    threshold: 1024,
    encodings: ['gzip', 'deflate']
  });

  // Static files
  await fastify.register(require('@fastify/static'), {
    root: path.join(__dirname, 'public'),
    prefix: '/',
    decorateReply: false
  });

  // Request context for tracking
  await fastify.register(require('@fastify/request-context'));

  // Rate limiting
  await fastify.register(require('@fastify/rate-limit'), {
    max: 1000, // requests
    timeWindow: '1 minute',
    skipOnError: true,
    keyGenerator: (request) => {
      return request.ip || 'anonymous';
    }
  });

  // WebSocket support for subscriptions and real-time features
  await fastify.register(require('@fastify/websocket'));

  // WebSocket logger integration
  fastify.register(async function (fastify) {
    // WebSocket endpoint for real-time logs
    fastify.get('/ws/logs/:logId', { websocket: true }, (connection, request) => {
      const { logId } = request.params;

      fastify.log.info(`WebSocket client connected to logs: ${logId}`);

      connection.socket.on('message', (message) => {
        try {
          const data = JSON.parse(message);

          if (data.type === 'subscribe') {
            // Subscribe to log stream
            fastify.log.info(`Client subscribed to logs: ${logId}`);

            // Send initial connection message
            connection.socket.send(JSON.stringify({
              type: 'connected',
              logId,
              timestamp: new Date().toISOString(),
              message: 'Connected to log stream'
            }));

            // Send periodic updates (mock implementation)
            const interval = setInterval(() => {
              if (connection.socket.readyState === 1) { // OPEN
                connection.socket.send(JSON.stringify({
                  type: 'log',
                  logId,
                  timestamp: new Date().toISOString(),
                  level: 'info',
                  message: `Sample log message for ${logId}`,
                  data: { progress: Math.floor(Math.random() * 100) }
                }));
              } else {
                clearInterval(interval);
              }
            }, 5000);

            connection.socket.on('close', () => {
              clearInterval(interval);
            });
          }
        } catch (error) {
          fastify.log.error('WebSocket message error:', error);
        }
      });

      connection.socket.on('close', () => {
        fastify.log.info(`WebSocket client disconnected from logs: ${logId}`);
      });

      connection.socket.on('error', (error) => {
        fastify.log.error('WebSocket error:', error);
      });
    });

    // WebSocket endpoint for performance monitoring
    fastify.get('/ws/performance', { websocket: true }, (connection, request) => {
      fastify.log.info('WebSocket client connected to performance monitoring');

      // Send performance data every 5 seconds
      const interval = setInterval(() => {
        if (connection.socket.readyState === 1) { // OPEN
          const memoryUsage = process.memoryUsage();
          const performanceData = {
            type: 'performance',
            timestamp: new Date().toISOString(),
            memory: {
              heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
              heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
              rss: Math.round(memoryUsage.rss / 1024 / 1024)
            },
            uptime: process.uptime(),
            cpu: process.cpuUsage()
          };

          connection.socket.send(JSON.stringify(performanceData));
        } else {
          clearInterval(interval);
        }
      }, 5000);

      connection.socket.on('close', () => {
        clearInterval(interval);
        fastify.log.info('WebSocket client disconnected from performance monitoring');
      });

      connection.socket.on('error', (error) => {
        fastify.log.error('WebSocket performance error:', error);
        clearInterval(interval);
      });
    });
  });

  // Simple metrics endpoint (without fastify-metrics due to compatibility issues)
  fastify.get('/metrics', async (request, reply) => {
    reply.type('text/plain');
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();

    return `# HELP nodejs_memory_usage_bytes Memory usage in bytes
# TYPE nodejs_memory_usage_bytes gauge
nodejs_memory_usage_bytes{type="rss"} ${memUsage.rss}
nodejs_memory_usage_bytes{type="heapTotal"} ${memUsage.heapTotal}
nodejs_memory_usage_bytes{type="heapUsed"} ${memUsage.heapUsed}
nodejs_memory_usage_bytes{type="external"} ${memUsage.external}

# HELP nodejs_uptime_seconds Process uptime in seconds
# TYPE nodejs_uptime_seconds gauge
nodejs_uptime_seconds ${uptime}

# HELP netstream_server_info Server information
# TYPE netstream_server_info gauge
netstream_server_info{version="2.0.0",framework="fastify"} 1
`;
  });

  fastify.log.info('Simple metrics endpoint enabled');

  fastify.log.info('Essential plugins registered successfully');
}

// Register GraphQL
async function registerGraphQL() {
  try {
    fastify.log.info('Loading GraphQL schema and resolvers...');

    // Load the complete schema from schema.graphql
    const fs = require('fs');
    const path = require('path');
    const schemaPath = path.join(__dirname, 'schema.graphql');
    const fullSchema = fs.readFileSync(schemaPath, 'utf8');

    // Load the Fastify-compatible resolvers with enhanced stream functionality
    const resolvers = require('./src/graphql/fastifyCompatibleResolvers');

    fastify.log.info('Using working test schema...');

    fastify.log.info('Registering Mercurius...');

    // Start with basic configuration
    const mercuriusConfig = {
      schema: fullSchema,
      resolvers: resolvers,
      graphiql: process.env.NODE_ENV !== 'production',

      // Context function
      context: (request, reply) => {
        return {
          request,
          reply,
          db: request.db,
          mongoClient: request.mongoClient,
          dbService: request.dbService,
          dataLoaders: request.dataLoaders,
          cacheService: request.cacheService,
          logger: fastify.log,
          user: request.user || null,
          isAdmin: request.isAdmin || false,
          requestId: request.id,
          startTime: request.startTime
        };
      }
    };

    await fastify.register(require('mercurius'), mercuriusConfig);

    fastify.log.info('GraphQL (Mercurius) registered successfully');

  } catch (error) {
    console.error('Failed to register GraphQL - Full Error:', error);
    fastify.log.error('Failed to register GraphQL:', error.message);
    fastify.log.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code
    });
    throw error;
  }
}

// Add services to request context
fastify.addHook('onRequest', async (request, reply) => {
  request.db = db;
  request.mongoClient = mongoClient;
  request.dbService = dbService;
  request.dataLoaders = dataLoaders;
  request.cacheService = cacheService;
  request.startTime = Date.now();
});

// Add response time header
fastify.addHook('onSend', async (request, reply, payload) => {
  const responseTime = Date.now() - request.startTime;
  reply.header('X-Response-Time', `${responseTime}ms`);
  
  // Log slow requests
  if (responseTime > 1000) {
    fastify.log.warn(`Slow request detected: ${request.method} ${request.url} - ${responseTime}ms`);
  }
  
  return payload;
});

// Health check endpoint
fastify.get('/health', async (request, reply) => {
  try {
    // Check database connection
    await db.admin().ping();

    // Check cache connection
    let cacheStatus = 'disabled';
    if (cacheService) {
      const cacheHealth = await cacheService.healthCheck();
      cacheStatus = cacheHealth.status;
    }

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: 'connected',
      cache: cacheStatus
    };

    return healthStatus;
  } catch (error) {
    reply.code(503);
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: 'disconnected',
      cache: 'unknown'
    };
  }
});

// Cache statistics endpoint
fastify.get('/cache/stats', async (request, reply) => {
  if (!cacheService) {
    return {
      error: 'Cache service not available',
      stats: null
    };
  }

  return {
    stats: cacheService.getStats(),
    health: await cacheService.healthCheck()
  };
});

// Cache clear endpoint (admin only)
fastify.post('/cache/clear', async (request, reply) => {
  const { pattern, adminToken } = request.body || {};

  // Validate admin token (simple check for demo)
  if (!adminToken || adminToken !== process.env.ADMIN_KEY) {
    reply.code(401);
    return { error: 'Unauthorized' };
  }

  if (!cacheService) {
    return {
      error: 'Cache service not available',
      cleared: 0
    };
  }

  const cleared = await cacheService.clear(pattern);
  return {
    message: `Cleared ${cleared} cache entries`,
    cleared,
    pattern: pattern || 'netstream:*'
  };
});

// Basic API info endpoint
fastify.get('/api', async (request, reply) => {
  return {
    name: 'NetStream API',
    version: '2.0.0',
    framework: 'Fastify',
    graphql: '/graphql',
    health: '/health',
    timestamp: new Date().toISOString()
  };
});

// Error handler
fastify.setErrorHandler(async (error, request, reply) => {
  fastify.log.error({
    error: error.message,
    stack: error.stack,
    url: request.url,
    method: request.method,
    ip: request.ip
  }, 'Request error');

  // Don't leak error details in production
  if (process.env.NODE_ENV === 'production') {
    reply.code(error.statusCode || 500);
    return {
      error: 'Internal Server Error',
      statusCode: error.statusCode || 500,
      timestamp: new Date().toISOString()
    };
  }

  reply.code(error.statusCode || 500);
  return {
    error: error.message,
    statusCode: error.statusCode || 500,
    stack: error.stack,
    timestamp: new Date().toISOString()
  };
});

// 404 handler
fastify.setNotFoundHandler(async (request, reply) => {
  reply.code(404);
  return {
    error: 'Not Found',
    statusCode: 404,
    message: `Route ${request.method} ${request.url} not found`,
    timestamp: new Date().toISOString()
  };
});

// Server startup function
async function startServer() {
  try {
    fastify.log.info('Starting NetStream Fastify server...');

    // Connect to database
    await connectDatabase();

    // Initialize configuration for Docker environment
    if (process.env.NODE_ENV === 'production' || process.env.DOCKER_ENV) {
      try {
        const { initializeConfig } = require('./scripts/docker-startup');
        await initializeConfig();
      } catch (error) {
        fastify.log.warn('Config initialization warning:', error.message);
        // Continue startup even if config init fails
      }
    }

    // Register plugins
    await registerPlugins();

    // Register GraphQL
    await registerGraphQL();

    // Register API routes
    await fastify.register(require('./src/routes/fastifyRoutes'));
    
    // Start server
    const serverPort = process.env.FASTIFY_PORT || 3001;
    const host = process.env.NODE_ENV === 'production' ? '0.0.0.0' : 'localhost';
    
    await fastify.listen({ 
      port: serverPort, 
      host: host 
    });
    
    fastify.log.info(`NetStream Fastify server listening on ${host}:${serverPort}`);
    fastify.log.info(`Health check available at: http://${host}:${serverPort}/health`);
    fastify.log.info(`API info available at: http://${host}:${serverPort}/api`);
    fastify.log.info(`GraphQL endpoint available at: http://${host}:${serverPort}/graphql`);
    fastify.log.info(`Performance metrics available at: http://${host}:${serverPort}/metrics`);
    fastify.log.info(`Cache stats available at: http://${host}:${serverPort}/cache/stats`);

    if (process.env.NODE_ENV !== 'production') {
      fastify.log.info(`GraphiQL available at: http://${host}:${serverPort}/graphiql`);
    }

    // Start background workers
    try {
      fastify.log.info('Starting background workers...');

      // Start trending worker (if available)
      try {
        require('./src/workers/trendingWorker');
        fastify.log.info('Trending worker started successfully');
      } catch (error) {
        fastify.log.warn('Trending worker not available:', error.message);
      }

      // Start scraping workers
      try {
        await startWorkers();
        fastify.log.info('Scraping workers started successfully');
      } catch (error) {
        fastify.log.warn('Scraping workers not available:', error.message);
      }

    } catch (error) {
      fastify.log.warn('Some workers failed to start:', error.message);
    }

    // Memory optimization for production
    if (process.env.NODE_ENV === 'production') {
      // Force garbage collection every 30 seconds
      setInterval(() => {
        if (global.gc) {
          global.gc();
        }
      }, 30000);

      // Monitor memory usage
      setInterval(() => {
        const memUsage = process.memoryUsage();
        const memUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);

        if (memUsedMB > 500) { // Alert if using more than 500MB
          fastify.log.warn(`High memory usage detected: ${memUsedMB}MB`);
        }
      }, 60000);
    }
    
  } catch (error) {
    console.error('Failed to start server - Full Error:', error);
    fastify.log.error('Failed to start server:', error.message);
    fastify.log.error('Error stack:', error.stack);
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = { fastify, startServer, connectDatabase };
