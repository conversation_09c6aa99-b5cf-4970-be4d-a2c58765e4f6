// File: src/enrichment/config/enrichmentConfig.js
/**
 * Configuration for the enrichment process
 *
 * This file contains configuration options for the enrichment process.
 * These options can be overridden by environment variables.
 *
 * To enable advanced enrichment with Gemini AI, set USE_ADVANCED_ENRICHMENT=true in your .env file.
 * This will use Gemini AI for better title matching and verification.
 *
 * Example .env configuration:
 * ```
 * GEMINI_API_KEY=your_api_key_here
 * USE_ADVANCED_ENRICHMENT=true
 * ```
 */

// Default configuration
const defaultConfig = {
  // Whether to use Gemini AI for advanced enrichment
  USE_GEMINI: process.env.USE_GEMINI === 'true' || false,

  // Whether to fetch seasons data during enrichment
  FETCH_SEASONS: process.env.FETCH_SEASONS !== 'false', // Default to true unless explicitly set to false

  // Whether to use advanced title matching
  USE_ADVANCED_MATCHING: process.env.USE_ADVANCED_MATCHING === 'true' || false,

  // Whether to enable caching
  ENABLE_CACHING: process.env.ENABLE_CACHING !== 'false', // Default to true unless explicitly set to false

  // Similarity threshold for title matching (0.0 to 1.0)
  TITLE_SIMILARITY_THRESHOLD: parseFloat(process.env.TITLE_SIMILARITY_THRESHOLD) || 0.5,

  // Rate limits for API calls (in milliseconds)
  // Convert from requests per minute to milliseconds between requests
  GEMINI_RATE_LIMIT_MS: parseInt(process.env.GEMINI_RATE_LIMIT) ? Math.ceil(60000 / parseInt(process.env.GEMINI_RATE_LIMIT)) : 2000, // Default: 30 req/min = 2000ms
  TMDB_RATE_LIMIT_MS: parseInt(process.env.TMDB_RATE_LIMIT) ? Math.ceil(60000 / parseInt(process.env.TMDB_RATE_LIMIT)) : 1500, // Default: 40 req/min = 1500ms
  JIKAN_RATE_LIMIT_MS: parseInt(process.env.JIKAN_RATE_LIMIT) ? Math.ceil(60000 / parseInt(process.env.JIKAN_RATE_LIMIT)) : 1000, // Default: 60 req/min = 1000ms

  // Backward compatibility
  API_RATE_LIMIT_MS: parseInt(process.env.API_RATE_LIMIT_MS) || 2000, // Default to Gemini rate limit

  // Maximum number of concurrent enrichment operations
  MAX_CONCURRENT_ENRICHMENTS: parseInt(process.env.MAX_CONCURRENT_ENRICHMENTS) || 40
};

/**
 * Get the current enrichment configuration
 * @returns {Object} The current configuration
 */
function getConfig() {
  return { ...defaultConfig };
}

/**
 * Update the enrichment configuration
 * @param {Object} newConfig - The new configuration options
 * @returns {Object} The updated configuration
 */
function updateConfig(newConfig) {
  Object.assign(defaultConfig, newConfig);
  return { ...defaultConfig };
}

module.exports = {
  getConfig,
  updateConfig
};
