// File: src/utils/fetchWithRetry.js
// Utility for making HTTP requests with retry logic and rate limiting

const logger = require('./logger');

/**
 * Fetch with retry logic and exponential backoff
 * @param {string} url - URL to fetch
 * @param {object} options - Fetch options
 * @param {number} retries - Number of retries (default: 3)
 * @param {number} baseDelay - Base delay in milliseconds (default: 1000)
 * @returns {Promise<Response>} - Fetch response
 */
async function fetchWithRetry(url, options = {}, retries = 3, baseDelay = 1000) {
  for (let i = 0; i < retries; i++) {
    try {
      // Import node-fetch dynamically
      const fetch = (await import('node-fetch')).default;

      // Handle timeout with AbortController
      const { timeout, ...fetchOptions } = options;
      let controller, timeoutId;

      if (timeout) {
        controller = new AbortController();
        timeoutId = setTimeout(() => controller.abort(), timeout);
        fetchOptions.signal = controller.signal;
      }

      const response = await fetch(url, fetchOptions);

      // Clear timeout if successful
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      if (response.status === 429) {
        const retryAfter = response.headers.get("Retry-After") || (baseDelay / 1000) * (i + 1);
        logger.warn(`Rate limited (429), retrying after ${retryAfter}s`, { url });
        await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));
        continue;
      }
      
      // Only treat 4xx and 5xx as errors, allow 2xx and 3xx (redirects)
      if (response.status >= 400) {
        const text = await response.text();
        throw new Error(
          `Fetch failed: ${response.status} ${response.statusText} - ${text.slice(0, 200)}`
        );
      }
      
      return response;
    } catch (err) {
      // Clear timeout on error
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Handle timeout errors
      if (err.name === 'AbortError') {
        const timeoutError = new Error(`Request timeout after ${timeout || 'default'}ms`);
        timeoutError.name = 'TimeoutError';
        err = timeoutError;
      }

      if (i === retries - 1) throw err;

      logger.warn(`Attempt ${i + 1} failed: ${err.message}, retrying...`, { url });
      await new Promise((resolve) => setTimeout(resolve, baseDelay * (i + 1)));
    }
  }
  
  throw new Error(`fetchWithRetry failed for ${url} after all retries.`);
}

/**
 * Fetch with timeout
 * @param {string} url - URL to fetch
 * @param {object} options - Fetch options
 * @param {number} timeout - Timeout in milliseconds (default: 10000)
 * @returns {Promise<Response>} - Fetch response
 */
async function fetchWithTimeout(url, options = {}, timeout = 10000) {
  const fetch = (await import('node-fetch')).default;
  
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error(`Request timeout after ${timeout}ms`);
    }
    throw error;
  }
}

/**
 * Fetch JSON with retry and error handling
 * @param {string} url - URL to fetch
 * @param {object} options - Fetch options
 * @param {number} retries - Number of retries
 * @returns {Promise<object>} - Parsed JSON response
 */
async function fetchJsonWithRetry(url, options = {}, retries = 3) {
  const response = await fetchWithRetry(url, options, retries);
  
  const contentType = response.headers.get('content-type');
  if (!contentType || !contentType.includes('application/json')) {
    const text = await response.text();
    throw new Error(`Expected JSON response, got ${contentType}: ${text.slice(0, 200)}`);
  }
  
  return await response.json();
}

/**
 * Fetch with custom headers for different providers
 * @param {string} url - URL to fetch
 * @param {string} provider - Provider name (wiflix, witv, etc.)
 * @param {object} customOptions - Custom fetch options
 * @returns {Promise<Response>} - Fetch response
 */
async function fetchWithProviderHeaders(url, provider = 'default', customOptions = {}) {
  const providerHeaders = {
    default: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9'
    },
    wiflix: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5'
    },
    witv: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Referer': 'https://witv.skin/',
      'Origin': 'https://witv.skin',
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Connection': 'keep-alive',
      'Range': 'bytes=0-',
      'Accept-Encoding': 'identity;q=1, *;q=0'
    },
    addic7ed: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Referer': 'https://www.addic7ed.com/',
      'Connection': 'keep-alive'
    }
  };

  const headers = {
    ...providerHeaders[provider] || providerHeaders.default,
    ...customOptions.headers
  };

  const options = {
    ...customOptions,
    headers
  };

  return await fetchWithRetry(url, options);
}

module.exports = {
  fetchWithRetry,
  fetchWithTimeout,
  fetchJsonWithRetry,
  fetchWithProviderHeaders
};
