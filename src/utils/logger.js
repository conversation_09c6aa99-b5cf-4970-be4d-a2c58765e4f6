const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => `${timestamp} [${level}]: ${message}`)
  ),
  transports: [
    //new winston.transports.File({ filename: 'logs/app.log' }),
    new winston.transports.Console()
  ]
});

module.exports = logger;