
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for NetStream_graphql/src/scrapers/services/scrapeService.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">NetStream_graphql/src/scrapers/services</a> scrapeService.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/162</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/39</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/24</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/153</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
&nbsp;
// File: /home/<USER>/NetStream/src/scrapers/services/scrapeService.js - REVISED STRUCTURE
const mongoose = <span class="cstat-no" title="statement not covered" >require('mongoose');</span>
const logger = <span class="cstat-no" title="statement not covered" >require('../../utils/logger');</span>
const { mongoUri, SCRAPE_INTERVAL, SCRAPING_ORDER, SCRAPE_MODE, scrapeMode } = <span class="cstat-no" title="statement not covered" >require('../../config/env');</span> // Get constants from env
const { scrapeFrenchAnime } = <span class="cstat-no" title="statement not covered" >require('../../../scripts/scrapeFrenchAnime');</span> // Corrected path
const { scrapeWitv } = <span class="cstat-no" title="statement not covered" >require('../../../scripts/scrapeWitv');</span> // Corrected path
const { scrapeWiflixMovies } = <span class="cstat-no" title="statement not covered" >require('../../../scripts/scrapeWiflixMovies');</span>
const { scrapeWiflixSeries } = <span class="cstat-no" title="statement not covered" >require('../../../scripts/scrapeWiflixSeries');</span>
const { saveToDB } = <span class="cstat-no" title="statement not covered" >require('../../../scripts/saveToDB');</span>
const { closeBrowser } = <span class="cstat-no" title="statement not covered" >require('../../utils/browserUtils');</span> // Keep import for closeBrowser
&nbsp;
// Page limits (keep the function as is)
// Update page limits to properly handle film-ancien and film-en-streaming
const DEFAULT_PAGE_LIMITS = <span class="cstat-no" title="statement not covered" >{ witv: -1, movies: -1, series: -1, anime: -1 };</span>
// Modify to include film-ancien and film-en-streaming
const LATEST_PAGE_LIMITS = <span class="cstat-no" title="statement not covered" >{ movies: 2, series: 2, anime: 2, witv: 4 };</span>
const SCRAPER_TIMEOUTS = <span class="cstat-no" title="statement not covered" >{</span>
    movies: 90 * 60 * 1000, // Increased to 90 minutes
    series: 60 * 60 * 1000,
    anime: 30 * 60 * 1000,
    witv: 15 * 60 * 1000
};
&nbsp;
function <span class="fstat-no" title="function not covered" >getPageLimits(</span>mode) {
    const isLatestMode = <span class="cstat-no" title="statement not covered" >mode === SCRAPE_MODE.LATEST;</span>
    const baseLimits = <span class="cstat-no" title="statement not covered" >isLatestMode ? LATEST_PAGE_LIMITS : DEFAULT_PAGE_LIMITS;</span>
    const limits = <span class="cstat-no" title="statement not covered" >{ ...baseLimits };</span>
&nbsp;
    // Allow overriding via environment variables
<span class="cstat-no" title="statement not covered" >    for (const key in limits) {</span>
        const envVar = <span class="cstat-no" title="statement not covered" >`PAGE_LIMIT_${key.toUpperCase()}`;</span>
<span class="cstat-no" title="statement not covered" >        if (process.env[envVar]) {</span>
            const parsedValue = <span class="cstat-no" title="statement not covered" >parseInt(process.env[envVar], 10);</span>
<span class="cstat-no" title="statement not covered" >            if (!isNaN(parsedValue)) {</span>
<span class="cstat-no" title="statement not covered" >                limits[key] = parsedValue;</span>
            } else {
<span class="cstat-no" title="statement not covered" >                logger.warn(`Invalid value for environment variable ${envVar}: ${process.env[envVar]}`);</span>
            }
        }
    }
&nbsp;
    // Allow overriding via command line arguments (less common now)
    const args = <span class="cstat-no" title="statement not covered" >process.argv.slice(2);</span>
<span class="cstat-no" title="statement not covered" >    args.forEach(<span class="fstat-no" title="function not covered" >ar</span>g =&gt; {</span>
        const [key, value] = <span class="cstat-no" title="statement not covered" >arg.split('=');</span>
<span class="cstat-no" title="statement not covered" >        if (limits.hasOwnProperty(key)) {</span>
            const parsedValue = <span class="cstat-no" title="statement not covered" >parseInt(value, 10);</span>
<span class="cstat-no" title="statement not covered" >            limits[key] = isNaN(parsedValue) ? limits[key] : parsedValue;</span>
        }
    });
<span class="cstat-no" title="statement not covered" >    logger.info(`Page limits parsed for ${mode} mode: ${JSON.stringify(limits)}`);</span>
<span class="cstat-no" title="statement not covered" >    return limits;</span>
}
&nbsp;
async function <span class="fstat-no" title="function not covered" >connectDB(</span>) {
<span class="cstat-no" title="statement not covered" >    if (mongoose.connection.readyState !== 1) {</span>
<span class="cstat-no" title="statement not covered" >        logger.info('Attempting to connect to MongoDB in scrapeService...');</span>
<span class="cstat-no" title="statement not covered" >        await mongoose.connect(mongoUri);</span>
<span class="cstat-no" title="statement not covered" >        logger.info('Connected to MongoDB in scrapeService');</span>
    } else {
<span class="cstat-no" title="statement not covered" >        logger.info('Using existing MongoDB connection in scrapeService');</span>
    }
}
&nbsp;
async function <span class="fstat-no" title="function not covered" >timeout(</span>ms) {
<span class="cstat-no" title="statement not covered" >  return new Promise(<span class="fstat-no" title="function not covered" >re</span>solve =&gt; <span class="cstat-no" title="statement not covered" >setTimeout(resolve, ms))</span>;</span>
}
&nbsp;
// Add a delay between scraping tasks to prevent overwhelming the browser
async function <span class="fstat-no" title="function not covered" >runScraperWithTimeout(</span>taskFn, timeoutDuration, taskName) {
     // Add retry mechanism for protocol errors
     let retries = <span class="cstat-no" title="statement not covered" >0;</span>
     const maxRetries = <span class="cstat-no" title="statement not covered" >3;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >     while (retries &lt;= maxRetries) {</span>
<span class="cstat-no" title="statement not covered" >         try {</span>
<span class="cstat-no" title="statement not covered" >             return await Promise.race([</span>
                 taskFn(),
                 new Promise(<span class="fstat-no" title="function not covered" >(_</span>, reject) =&gt;
<span class="cstat-no" title="statement not covered" >                     setTimeout(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >reject(new Error(`${taskName} scraping timeout after ${timeoutDuration / 1000}s`)),</span> timeoutDuration)</span>
                 )
             ]);
         } catch (err) {
<span class="cstat-no" title="statement not covered" >             if (err.message.includes('Protocol timeout') || err.name === 'ProtocolError') {</span>
<span class="cstat-no" title="statement not covered" >                 retries++;</span>
<span class="cstat-no" title="statement not covered" >                 if (retries &lt;= maxRetries) {</span>
<span class="cstat-no" title="statement not covered" >                     logger.warn(`Protocol error in ${taskName}, retry ${retries}/${maxRetries} after 10s delay...`);</span>
<span class="cstat-no" title="statement not covered" >                     await timeout(10000); </span>// 10 second delay before retry
<span class="cstat-no" title="statement not covered" >                     continue;</span>
                 }
             }
&nbsp;
<span class="cstat-no" title="statement not covered" >             logger.error(`Error or Timeout in ${taskName} scraper: ${err.message}`);</span>
<span class="cstat-no" title="statement not covered" >             return []; </span>// Return empty array on error/timeout to allow others to proceed
         }
     }
}
&nbsp;
&nbsp;
async function <span class="fstat-no" title="function not covered" >scrapeAll(</span>mode = <span class="branch-0 cbranch-no" title="branch not covered" >scrapeMode)</span> {
  const startTime = <span class="cstat-no" title="statement not covered" >Date.now();</span>
<span class="cstat-no" title="statement not covered" >  logger.info(`Starting scrapeAll function in ${mode} mode`);</span>
<span class="cstat-no" title="statement not covered" >  try {</span>
<span class="cstat-no" title="statement not covered" >    await connectDB();</span>
&nbsp;
    const pageLimits = <span class="cstat-no" title="statement not covered" >getPageLimits(mode);</span>
<span class="cstat-no" title="statement not covered" >    logger.info(`Using page limits for ${mode} mode: ${JSON.stringify(pageLimits)}`);</span>
&nbsp;
    let totalCounts = <span class="cstat-no" title="statement not covered" >{ movies: 0, series: 0, anime: 0, witv: 0 };</span>
    let taskResults = <span class="cstat-no" title="statement not covered" >{ movies: [], series: [], anime: [], witv: [] };</span>
&nbsp;
    // Define scraper functions clearly
    const tasks = <span class="cstat-no" title="statement not covered" >{</span>
      anime: <span class="fstat-no" title="function not covered" >as</span>ync () =&gt; {
<span class="cstat-no" title="statement not covered" >          if (pageLimits.anime === 0) {</span>
<span class="cstat-no" title="statement not covered" >              logger.info('Skipping anime (limit 0)');</span>
<span class="cstat-no" title="statement not covered" >              return [];</span>
          }
<span class="cstat-no" title="statement not covered" >          logger.info(`Starting anime scraping with limit ${pageLimits.anime}`);</span>
          const results = <span class="cstat-no" title="statement not covered" >await runScraperWithTimeout(</span>
<span class="fstat-no" title="function not covered" >              ()</span> =&gt; <span class="cstat-no" title="statement not covered" >scrapeFrenchAnime(pageLimits.anime, saveToDB, mode === SCRAPE_MODE.LATEST),</span>
              SCRAPER_TIMEOUTS.anime,
              'Anime'
          );
<span class="cstat-no" title="statement not covered" >          logger.info(`Anime scraping completed, got ${results.length} results`);</span>
<span class="cstat-no" title="statement not covered" >          taskResults.anime = results;</span>
<span class="cstat-no" title="statement not covered" >          return results;</span>
      },
      wiflix_movies: <span class="fstat-no" title="function not covered" >as</span>ync () =&gt; {
<span class="cstat-no" title="statement not covered" >          if (pageLimits.movies === 0) {</span>
<span class="cstat-no" title="statement not covered" >              logger.info('Skipping movies (limit 0)');</span>
<span class="cstat-no" title="statement not covered" >              return [];</span>
          }
<span class="cstat-no" title="statement not covered" >          logger.info(`Starting movies scraping with limit ${pageLimits.movies}`);</span>
          const results = <span class="cstat-no" title="statement not covered" >await runScraperWithTimeout(</span>
<span class="fstat-no" title="function not covered" >              ()</span> =&gt; <span class="cstat-no" title="statement not covered" >scrapeWiflixMovies(pageLimits.movies, saveToDB, mode === SCRAPE_MODE.LATEST),</span>
              SCRAPER_TIMEOUTS.movies,
              'Wiflix Movies'
          );
<span class="cstat-no" title="statement not covered" >          logger.info(`Movies scraping completed, got ${results.length} results`);</span>
<span class="cstat-no" title="statement not covered" >          taskResults.movies = results;</span>
<span class="cstat-no" title="statement not covered" >          return results;</span>
      },
      wiflix_series: <span class="fstat-no" title="function not covered" >as</span>ync () =&gt; {
<span class="cstat-no" title="statement not covered" >          if (pageLimits.series === 0) {</span>
<span class="cstat-no" title="statement not covered" >              logger.info('Skipping series (limit 0)');</span>
<span class="cstat-no" title="statement not covered" >              return [];</span>
          }
<span class="cstat-no" title="statement not covered" >          logger.info(`Starting series scraping with limit ${pageLimits.series}`);</span>
          const results = <span class="cstat-no" title="statement not covered" >await runScraperWithTimeout(</span>
<span class="fstat-no" title="function not covered" >              ()</span> =&gt; <span class="cstat-no" title="statement not covered" >scrapeWiflixSeries(pageLimits.series, saveToDB, mode === SCRAPE_MODE.LATEST),</span>
              SCRAPER_TIMEOUTS.series,
              'Wiflix Series'
          );
<span class="cstat-no" title="statement not covered" >          logger.info(`Series scraping completed, got ${results.length} results`);</span>
<span class="cstat-no" title="statement not covered" >          taskResults.series = results;</span>
<span class="cstat-no" title="statement not covered" >          return results;</span>
      },
      witv: <span class="fstat-no" title="function not covered" >as</span>ync () =&gt; {
<span class="cstat-no" title="statement not covered" >          if (pageLimits.witv === 0) {</span>
<span class="cstat-no" title="statement not covered" >              logger.info('Skipping WiTV (limit 0)');</span>
<span class="cstat-no" title="statement not covered" >              return [];</span>
          }
<span class="cstat-no" title="statement not covered" >          logger.info(`Starting WiTV scraping with limit ${pageLimits.witv}`);</span>
          const results = <span class="cstat-no" title="statement not covered" >await runScraperWithTimeout(</span>
<span class="fstat-no" title="function not covered" >              ()</span> =&gt; <span class="cstat-no" title="statement not covered" >scrapeWitv(pageLimits.witv, saveToDB),</span>
              SCRAPER_TIMEOUTS.witv,
              'WiTV'
          );
<span class="cstat-no" title="statement not covered" >          logger.info(`WiTV scraping completed, got ${results.length} results`);</span>
<span class="cstat-no" title="statement not covered" >          taskResults.witv = results;</span>
<span class="cstat-no" title="statement not covered" >          return results;</span>
      }
    };
&nbsp;
<span class="cstat-no" title="statement not covered" >    logger.info(`SCRAPING_ORDER: ${SCRAPING_ORDER.join(', ')}`);</span>
&nbsp;
    // Separate tasks
    const puppeteerTaskNames = <span class="cstat-no" title="statement not covered" >SCRAPING_ORDER.filter(<span class="fstat-no" title="function not covered" >ta</span>sk =&gt; <span class="cstat-no" title="statement not covered" >task.startsWith('wiflix_'))</span>;</span>
    const axiosTaskNames = <span class="cstat-no" title="statement not covered" >SCRAPING_ORDER.filter(<span class="fstat-no" title="function not covered" >ta</span>sk =&gt; <span class="cstat-no" title="statement not covered" >!task.startsWith('wiflix_'))</span>;</span>
&nbsp;
    // Get concurrency settings from environment variables or use defaults
    const MAX_CONCURRENT_PUPPETEER_TASKS = <span class="cstat-no" title="statement not covered" >process.env.MAX_CONCURRENT_PUPPETEER_TASKS ? parseInt(process.env.MAX_CONCURRENT_PUPPETEER_TASKS) : 1;</span>
    const MAX_CONCURRENT_AXIOS_TASKS = <span class="cstat-no" title="statement not covered" >process.env.MAX_CONCURRENT_AXIOS_TASKS ? parseInt(process.env.MAX_CONCURRENT_AXIOS_TASKS) : 2;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    logger.info(`Scrape concurrency settings: MAX_CONCURRENT_PUPPETEER_TASKS=${MAX_CONCURRENT_PUPPETEER_TASKS}, MAX_CONCURRENT_AXIOS_TASKS=${MAX_CONCURRENT_AXIOS_TASKS}`);</span>
&nbsp;
    let allResults = <span class="cstat-no" title="statement not covered" >[];</span>
&nbsp;
    // Run Axios tasks with controlled concurrency
<span class="cstat-no" title="statement not covered" >    if (axiosTaskNames.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        logger.info(`Starting Axios tasks with concurrency ${MAX_CONCURRENT_AXIOS_TASKS}: ${axiosTaskNames.join(', ')}`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        try {</span>
            // Process Axios tasks in batches to control concurrency
<span class="cstat-no" title="statement not covered" >            for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; axiosTaskNames.length; i += MAX_CONCURRENT_AXIOS_TASKS) {</span>
                const batch = <span class="cstat-no" title="statement not covered" >axiosTaskNames.slice(i, i + MAX_CONCURRENT_AXIOS_TASKS);</span>
<span class="cstat-no" title="statement not covered" >                logger.info(`Processing Axios batch ${Math.floor(i/MAX_CONCURRENT_AXIOS_TASKS) + 1}/${Math.ceil(axiosTaskNames.length/MAX_CONCURRENT_AXIOS_TASKS)}: ${batch.join(', ')}`);</span>
&nbsp;
                const batchPromises = <span class="cstat-no" title="statement not covered" >batch.map(<span class="fstat-no" title="function not covered" >ta</span>skName =&gt; <span class="cstat-no" title="statement not covered" >tasks[taskName]())</span>;</span>
                const batchResults = <span class="cstat-no" title="statement not covered" >await Promise.all(batchPromises);</span>
&nbsp;
                // Flatten and add to allResults
                const flattenedResults = <span class="cstat-no" title="statement not covered" >batchResults.flat();</span>
<span class="cstat-no" title="statement not covered" >                allResults.push(...flattenedResults);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                logger.info(`Axios batch completed. Batch results: ${flattenedResults.length}`);</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            logger.info(`All Axios tasks completed. Total results: ${allResults.length}`);</span>
        } catch (axiosError) {
<span class="cstat-no" title="statement not covered" >            logger.error(`Error during Axios tasks: ${axiosError.message}`, { stack: axiosError.stack });</span>
        }
    }
&nbsp;
    // Run Puppeteer tasks with controlled concurrency
<span class="cstat-no" title="statement not covered" >    if (puppeteerTaskNames.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        logger.info(`Starting Puppeteer tasks with concurrency ${MAX_CONCURRENT_PUPPETEER_TASKS}: ${puppeteerTaskNames.join(', ')}`);</span>
&nbsp;
        // Process Puppeteer tasks in batches to control concurrency
<span class="cstat-no" title="statement not covered" >        for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; puppeteerTaskNames.length; i += MAX_CONCURRENT_PUPPETEER_TASKS) {</span>
            const batch = <span class="cstat-no" title="statement not covered" >puppeteerTaskNames.slice(i, i + MAX_CONCURRENT_PUPPETEER_TASKS);</span>
<span class="cstat-no" title="statement not covered" >            logger.info(`Processing Puppeteer batch ${Math.floor(i/MAX_CONCURRENT_PUPPETEER_TASKS) + 1}/${Math.ceil(puppeteerTaskNames.length/MAX_CONCURRENT_PUPPETEER_TASKS)}: ${batch.join(', ')}`);</span>
&nbsp;
            // Run tasks in the current batch concurrently
            const batchPromises = <span class="cstat-no" title="statement not covered" >batch.map(<span class="fstat-no" title="function not covered" >ta</span>skName =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                return new Promise(<span class="fstat-no" title="function not covered" >as</span>ync (resolve) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    try {</span>
<span class="cstat-no" title="statement not covered" >                        logger.info(`Starting Puppeteer task: ${taskName}`);</span>
                        const result = <span class="cstat-no" title="statement not covered" >await tasks[taskName]();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                        if (Array.isArray(result)) {</span>
<span class="cstat-no" title="statement not covered" >                            logger.info(`Puppeteer task ${taskName} completed with ${result.length} results`);</span>
<span class="cstat-no" title="statement not covered" >                            resolve(result);</span>
                        } else {
<span class="cstat-no" title="statement not covered" >                            logger.warn(`Puppeteer task ${taskName} did not return an array: ${typeof result}`);</span>
<span class="cstat-no" title="statement not covered" >                            resolve([]);</span>
                        }
                    } catch (puppeteerError) {
<span class="cstat-no" title="statement not covered" >                        logger.error(`Error during Puppeteer task ${taskName}: ${puppeteerError.message}`, { stack: puppeteerError.stack });</span>
<span class="cstat-no" title="statement not covered" >                        resolve([]);</span>
                    }
                });
            });
&nbsp;
            const batchResults = <span class="cstat-no" title="statement not covered" >await Promise.all(batchPromises);</span>
            const flattenedResults = <span class="cstat-no" title="statement not covered" >batchResults.flat();</span>
<span class="cstat-no" title="statement not covered" >            allResults.push(...flattenedResults);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            logger.info(`Puppeteer batch completed. Batch results: ${flattenedResults.length}`);</span>
&nbsp;
            // Add a delay between batches to allow resources to be freed
<span class="cstat-no" title="statement not covered" >            if (i + MAX_CONCURRENT_PUPPETEER_TASKS &lt; puppeteerTaskNames.length) {</span>
                const batchDelay = <span class="cstat-no" title="statement not covered" >10000;</span> // 10 seconds between batches
<span class="cstat-no" title="statement not covered" >                logger.info(`Waiting ${batchDelay}ms between Puppeteer batches...`);</span>
<span class="cstat-no" title="statement not covered" >                await timeout(batchDelay);</span>
            }
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        logger.info(`All Puppeteer tasks completed. Total results: ${allResults.filter(<span class="fstat-no" title="function not covered" >r </span>=&gt; <span class="cstat-no" title="statement not covered" >r)</span>.length}`);</span>
    }
&nbsp;
    // Log detailed results by type
<span class="cstat-no" title="statement not covered" >    for (const type in taskResults) {</span>
<span class="cstat-no" title="statement not covered" >        totalCounts[type] = taskResults[type].length;</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    logger.info(`Scraping results by type: ${JSON.stringify(totalCounts)}`);</span>
&nbsp;
    const duration = <span class="cstat-no" title="statement not covered" >((Date.now() - startTime) / 1000).toFixed(1);</span>
<span class="cstat-no" title="statement not covered" >    logger.info(`Full scrape completed in ${mode} mode in ${duration}s.`);</span>
<span class="cstat-no" title="statement not covered" >    logger.info(`Total items potentially saved across all executed tasks: ${allResults.length}`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
        totalItems: allResults.length,
        itemsByType: totalCounts,
        duration: parseFloat(duration)
    };
&nbsp;
  } catch (err) {
    // Catch errors from connectDB or other setup issues
<span class="cstat-no" title="statement not covered" >    logger.error(`ScrapeAll main process error in ${mode} mode: ${err.message}`, { stack: err.stack });</span>
<span class="cstat-no" title="statement not covered" >    throw err; </span>// Re-throw to allow proper handling by caller
  } finally {
    // Centralized browser closing
<span class="cstat-no" title="statement not covered" >    logger.info('ScrapeAll function finished or errored. Attempting to close browser...');</span>
<span class="cstat-no" title="statement not covered" >    await closeBrowser().catch(<span class="fstat-no" title="function not covered" >er</span>r =&gt; <span class="cstat-no" title="statement not covered" >logger.error('[ScrapeAll Finally] Error closing browser:', err))</span>;</span>
    // Optionally disconnect DB if needed, but likely handled by server shutdown
    // if (mongoose.connection.readyState === 1) {
    //     await mongoose.disconnect();
    //     logger.info('MongoDB disconnected via scrapeAll finally block.');
    // }
  }
}
&nbsp;
<span class="cstat-no" title="statement not covered" >module.exports = { scrapeAll, SCRAPE_MODE };</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T16:31:40.786Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    