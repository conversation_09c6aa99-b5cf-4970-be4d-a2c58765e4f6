// src/scrapers/sites/wiflix/detail.js - FINAL: Added Path Extraction
const cheerio = require('cheerio');
const logger = require('../../../utils/logger');
const { WIFLIX_BASE } = require('../../../config/constants');
const { fetchPageWithPuppeteer } = require('../../../utils/browserUtils');
const fs = require('fs');
const path = require('path');
const Config = require('../../../db/models/Config');

// Will be set dynamically in the scrapeWiflixDetail function
let WIFLIX_BASE_URL;
// Debug HTML generation removed

// Helper function to safely extract path from URL
function extractPathFromUrl(fullUrl, logContext) {
    if (!fullUrl || typeof fullUrl !== 'string' || !fullUrl.startsWith('http')) {
         // Handle potential relative URLs passed from thumbnail logic specifically
         if (fullUrl && typeof fullUrl === 'string' && fullUrl.startsWith('/')) {
             return fullUrl; // Assume it's already a valid path
         }
        return '';
    }
    try {
        const urlObject = new URL(fullUrl);
        return urlObject.pathname + urlObject.search + urlObject.hash;
    } catch (e) {
        logger.warn(`[${logContext}] Could not parse path from URL "${fullUrl}": ${e.message}`);
        return ''; // Return empty string on error
    }
}

function containsRealContent(html) {
    if (!html || typeof html !== 'string') return false;
    const hasMetadata = html.includes('<ul class="mov-list">');
    // Check for either the movie link structure OR the series episode structure
    const hasPlayerHost = html.includes('<div class="tabsbox filmlinks">') || html.includes('<div class="epblocks">');
    if (!hasMetadata) logger.warn('[Wiflix Detail Final] Detail HTML missing metadata block (<ul class="mov-list">)');
    if (!hasPlayerHost) logger.warn('[Wiflix Detail Final] Detail HTML missing player/host/episode block.');
    return hasMetadata || hasPlayerHost; // Require at least one key section
}

// Helper Function: Extracts provider from URL (as fallback or primary if needed)
function getProviderFromUrl(urlString) {
    if (typeof urlString !== 'string' || !urlString.startsWith('http')) return 'unknown';
    try {
        const hostname = new URL(urlString).hostname.toLowerCase().replace(/^www\./, '');
        const providerMap = {
            'do7go.com': 'Do7go',
            'magasavor.net': 'Magasavor',
            'alejandrocenturyoil.com': 'Magasavor',
            'tipfly.xyz': 'Tipfly',
            'vidply.com': 'Vidply',
            'uqload.net': 'Uqload', 'uqload.to': 'Uqload', 'uqloada.ws': 'Uqload',
            'voe.sx': 'Voe',
            'streamtape.com': 'Streamtape',
            'vidmoly.to': 'Vidmoly', 'vidmoly.me': 'Vidmoly',
            'dood.re': 'Doodstream', 'dood.wf': 'Doodstream', 'dood.pro': 'Doodstream', 'dood.sh': 'Doodstream', 'dooood.com': 'Doodstream', 'dooodster.com': 'Doodstream', 'd0000d.com': 'Doodstream', 'd000d.com': 'Doodstream', 'd0o0d.com': 'Doodstream',
            'mixdrop.co': 'Mixdrop', 'mixdrop.ps': 'Mixdrop',
            'vudeo.ws': 'Vudeo', 'vudeo.nlt': 'Vudeo',
            'upstream.to': 'Upstream', 'upstream.co': 'Upstream',
            'sbface.com': 'StreamSB', 'sbanh.com': 'StreamSB', 'sbchill.com': 'StreamSB', 'sbrity.com': 'StreamSB', 'sbbrisk.com': 'StreamSB', 'sblanh.com': 'StreamSB', 'sbhight.com': 'StreamSB', 'sbspeed.com': 'StreamSB', 'ssblongvu.com': 'StreamSB',
            'filemoon.sx': 'Filemoon',
            'luluvdo.com': 'Luluvdo', // Added from HTML
            'waaw1.tv': 'Waaw', // Base name
            'waaw.tv': 'Waaw',
            'waaw.to': 'Waaw',
            'smoothpre.com': 'Filelions', // Added from HTML
            'cybervynx.com': 'Swish',     // Added from HTML
             // Add more mappings as discovered
        };
        if (providerMap[hostname]) return providerMap[hostname];
        for (const domainKey in providerMap) {
            if (hostname.includes(domainKey.split('.')[0])) return providerMap[domainKey];
        }
        const domainParts = hostname.split('.');
        const mainDomain = domainParts.length > 1 ? domainParts[domainParts.length - 2] : domainParts[0];
        return mainDomain ? mainDomain.charAt(0).toUpperCase() + mainDomain.slice(1) : 'unknown';
    } catch (err) {
        logger.warn(`[Wiflix Detail Final] Could not parse provider from URL ${urlString}: ${err.message}`);
        return 'unknown';
    }
}
// --- End Helper ---

async function scrapeWiflixDetail(detailUrl) {
    logger.info(`[Wiflix Detail Final] Scraping detail page using Puppeteer: ${detailUrl.substring(0, 100)}...`);

    // Get the latest WIFLIX_BASE from the database
    const wiflixBase = await Config.getValue('WIFLIX_BASE', WIFLIX_BASE);
    WIFLIX_BASE_URL = `https://${wiflixBase}`;
    logger.info(`[Wiflix Detail Final] Using Wiflix base URL: ${WIFLIX_BASE_URL}`);

    let html;
    let timeout = 180000; // Increased timeout to 3 minutes
    let retryCount = 0;
    const maxRetries = 3; // Increased max retries

    // Check if this is a known problematic URL
    const isProblematicUrl = detailUrl.includes('le-renard-rouge') ||
                             detailUrl.includes('red-fox') ||
                             detailUrl.includes('renard-rouge');

    if (isProblematicUrl) {
        logger.info(`[Wiflix Detail Final] Special handling for problematic URL: ${detailUrl}`);
        timeout = 120000; // Increase timeout for problematic URLs
    }

    async function attemptScrape() {
        try {
            logger.info(`[Wiflix Detail Final] Attempt ${retryCount + 1} with timeout ${timeout}ms`);
            html = await fetchPageWithPuppeteer(detailUrl, timeout, retryCount);

            if (!html) {
                throw new Error("Puppeteer returned null HTML for detail page.");
            }

            if (!containsRealContent(html)) {
                logger.warn(`[Wiflix Detail Final] Puppeteer HTML for ${detailUrl} missing key content markers.`);

                // For problematic URLs, add detailed diagnostics
                if (isProblematicUrl) {
                    const diagnosticHtml = html.substring(0, 1000) + "..."; // First 1000 chars
                    logger.debug(`[Wiflix Detail Final] HTML snippet for diagnostics: ${diagnosticHtml}`);
                }

                // Return empty object with path field included
                return { streamingUrls: [], metadata: {}, episodes: [], thumbnail: undefined, thumbnailPath: undefined };
            }
        } catch (error) {
            // Special handling for concurrency errors
            if (error.message.includes('concurrency limit') || error.message.includes('Max retry attempts')) {
                logger.error(`[Wiflix Detail Final] Attempt ${retryCount + 1} failed due to concurrency limits: ${error.message}`);

                // Add a longer delay for concurrency issues to allow other requests to complete
                const concurrencyDelay = 5000 + (retryCount * 2000);
                logger.info(`[Wiflix Detail Final] Waiting ${concurrencyDelay}ms before retry due to concurrency issues...`);
                await new Promise(resolve => setTimeout(resolve, concurrencyDelay));
            } else {
                logger.error(`[Wiflix Detail Final] Attempt ${retryCount + 1} failed: ${error.message}`);
            }

            if (retryCount < maxRetries) {
                retryCount++;
                timeout += 30000; // Increase timeout for each retry
                return await attemptScrape();
            }
            throw error;
        }
        return html;
    }

    try {
        html = await attemptScrape();

        if (!html) { throw new Error("All scrape attempts failed for detail page."); }

        const $ = cheerio.load(html);
        const streamingUrls = []; // For movies
        const episodes = [];      // For series

        // --- Metadata Extraction (Keep robust version) ---
        const metadata = {};
        $('ul.mov-list li').each((_i, li) => {
            const element = $(li);
            const labelElement = element.find('strong').first();
            let label = labelElement.text().trim().toUpperCase().replace(':', '');
             if (!label) {
                const liText = element.clone().children().remove().end().text();
                const parts = liText.split(':');
                if (parts.length > 1) { label = parts[0].trim().toUpperCase(); }
            }
            const valueElement = element.find('.mov-desc');
            let valueText = valueElement.text().trim();
            if (!valueText && label) {
                valueText = element.text().substring(label.length + 1).trim();
            } else if (!valueText && !label) {
                valueText = element.text().trim();
            }

            // ... (keep all the metadata extraction logic: SYNOPSIS, ACTEURS, etc.)
             if (label.includes('SYNOPSIS')) {
                let synopsisRaw = valueElement.find('span[itemprop="description"]').text().trim() || valueText;
                const synopsisPrefixRegex = /^(Résumé|Synopsis)\s*(du film|de la série)?\s*.*?(- Saison \d+)?\s*(en Streaming Complet)?:?\s*/i;
                metadata.synopsis = synopsisRaw.replace(synopsisPrefixRegex, '').trim();
             } else if (label.includes('ACTEURS')) {
                 metadata.actors = valueElement.find('span[itemprop="actor"]').map((_j, actor) => $(actor).text().trim()).get();
                 if (!metadata.actors || metadata.actors.length === 0) {
                     metadata.actors = valueText.split(',').map(a => a.trim()).filter(Boolean);
                 }
             } else if (label.includes('DATE DE SORTIE')) {
                 metadata.year = valueText.match(/\d{4}/)?.[0] || '';
             } else if (label.includes('GENRE')) {
                 metadata.genre = valueElement.find('span[itemprop="genre"]').map((_j, genre) => $(genre).text().trim()).get().join(', ');
                  if (!metadata.genre) { metadata.genre = valueText; }
             } else if (label.includes('ORIGINE')) {
                 metadata.origin = valueText;
             } else if (label.includes('RÉALISATEUR')) {
                 metadata.creator = valueElement.find('span[itemprop="name"]').text().trim() || valueText;
             } else if (label.includes('DURÉE')) {
                 const directorName = $('li:contains("RÉALISATEUR") .mov-desc span[itemprop="name"]').text().trim();
                 let durationText = valueText;
                 if (directorName && durationText.includes(directorName)) { durationText = durationText.replace(directorName, '').trim(); }
                 metadata.duration = durationText || '';
             }
        });
        // --- End Metadata ---

        // --- Thumbnail Extraction ---
        let thumbnail = $('#posterimg[itemprop="thumbnailUrl"]').attr('src') || $('.mov-i img[itemprop="image"]').attr('src') || '';
        let thumbnailPath = ''; // Initialize path
        if (thumbnail) {
            // Ensure thumbnail URL is absolute before extracting path
            if (!thumbnail.startsWith('http')) {
                thumbnail = `${WIFLIX_BASE_URL}${thumbnail.startsWith('/') ? '' : '/'}${thumbnail}`;
            }
            // Extract path from the absolute URL
            thumbnailPath = extractPathFromUrl(thumbnail, 'Wiflix Detail Final');
        }
        // --- End Thumbnail ---


        // --- Stream/Episode Extraction (Enhanced Logic for Both) ---
        const episodeBlocks = $('.epblocks .blocvostfr, .epblocks .blocfr');

        // Log all episode blocks for debugging problematic URLs
        if (isProblematicUrl) {
            logger.info(`[Wiflix Detail Final] Detailed episode block analysis for problematic URL`);
            logger.info(`[Wiflix Detail Final] Episode blocks count: ${episodeBlocks.length}`);
            logger.info(`[Wiflix Detail Final] All episode blocks: ${$('.epblocks').html()?.substring(0, 200)}...`);

            // Log all rel attributes
            const relAttributes = [];
            $('li.clicbtn').each((i, el) => {
                relAttributes.push($(el).attr('rel'));
            });
            logger.info(`[Wiflix Detail Final] Rel attributes: ${relAttributes.join(', ')}`);

            // Check if host blocks exist for each rel attribute
            relAttributes.forEach(rel => {
                logger.info(`[Wiflix Detail Final] Host block for ${rel} exists: ${$(`.hostsblock .${rel}`).length > 0}`);
                if ($(`.hostsblock .${rel}`).length > 0) {
                    logger.info(`[Wiflix Detail Final] Links in ${rel}: ${$(`.hostsblock .${rel} a`).length}`);
                }
            });
        }

        if (episodeBlocks.length > 0) {
            // Series Logic
            logger.info(`[Wiflix Detail Final] Found ${episodeBlocks.length} language blocks. Processing as Series.`);
            const seasonMatch = detailUrl.match(/saison-(\d+)/i);
            const season = seasonMatch ? seasonMatch[1] : '1';

            episodeBlocks.each((_, langBlock) => {
                const isVostfr = $(langBlock).hasClass('blocvostfr');
                const language = isVostfr ? 'VOSTFR' : 'VF';

                logger.info(`[Wiflix Detail Final] Processing ${language} block`);

                $(langBlock).find('ul.eplist li.clicbtn').each((_i, epTab) => {
                    const epText = $(epTab).text().trim();
                    const epMatch = epText.match(/(\d+)/);
                    let episodeNumber = epMatch ? epMatch[1] : null;
                    const relAttr = $(epTab).attr('rel');

                    logger.info(`[Wiflix Detail Final] Found episode tab: "${epText}" with rel="${relAttr}"`);

                    if (!episodeNumber && relAttr) {
                         const relMatch = relAttr.match(/ep(\d+)/i);
                         if(relMatch) episodeNumber = relMatch[1];
                    }

                    // Additional fallback for episode number extraction
                    if (!episodeNumber && epText) {
                        // Try to extract any number from the text as a last resort
                        const anyNumberMatch = epText.match(/\d+/);
                        if (anyNumberMatch) {
                            episodeNumber = anyNumberMatch[0];
                            logger.info(`[Wiflix Detail Final] Extracted episode number ${episodeNumber} from text as fallback`);
                        }
                    }

                    if (!episodeNumber || !relAttr) {
                        logger.warn(`[Wiflix Detail Final] Could not extract episode number or rel attribute from tab: "${epText}"`);
                        return;
                    }

                    // Try multiple selector patterns for the links container
                    let linksContainer = $(`.hostsblock .${relAttr}`);

                    // If not found, try alternative selectors
                    if (linksContainer.length === 0) {
                        logger.warn(`[Wiflix Detail Final] Could not find links container with selector .hostsblock .${relAttr}`);

                        // Try alternative selectors
                        const alternativeSelectors = [
                            `.hostsblock div[class*="${relAttr}"]`,
                            `.hostsblock div[id*="${relAttr}"]`,
                            `.hostsblock div[class*="${episodeNumber}"]`,
                            `.hostsblock div[id*="${episodeNumber}"]`
                        ];

                        for (const selector of alternativeSelectors) {
                            const altContainer = $(selector);
                            if (altContainer.length > 0) {
                                logger.info(`[Wiflix Detail Final] Found alternative links container with selector: ${selector}`);
                                linksContainer = altContainer;
                                break;
                            }
                        }

                        // If still not found, try a more aggressive approach for problematic URLs
                        if (linksContainer.length === 0 && isProblematicUrl) {
                            logger.info(`[Wiflix Detail Final] Using aggressive link extraction for problematic URL`);
                            // For problematic URLs, just get all links in the hostsblock
                            linksContainer = $('.hostsblock');
                        }
                    }

                    const episodeStreamingUrls = [];

                    // Enhanced streaming URL extraction with better error handling
                    linksContainer.find('a[onclick*="loadVideo"]').each((_j, link) => {
                        try {
                            const onclickAttr = $(link).attr('onclick') || '';
                            const urlMatch = onclickAttr.match(/loadVideo\(['"]([^'"]+)['"]\)/);
                            const hostText = $(link).find('span.clichost').text().trim(); // Get "Lecteur X" or actual name

                            // Log for problematic URLs
                            if (isProblematicUrl) {
                                logger.info(`[Wiflix Detail Final] Processing link: onclick="${onclickAttr}", hostText="${hostText}"`);
                            }

                            if (urlMatch && urlMatch[1]) {
                                const streamUrl = urlMatch[1].trim();
                                let providerName = hostText.toLowerCase();
                                // If provider is generic, try getting from URL
                                if (providerName.startsWith('lecteur') || providerName === 'unknown' || !providerName) {
                                    providerName = getProviderFromUrl(streamUrl);
                                } else {
                                    // Attempt to capitalize if it's not generic (like "DDSTREAM" -> "Ddstream")
                                    providerName = providerName.charAt(0).toUpperCase() + providerName.slice(1).toLowerCase();
                                }

                                // Validate URL before adding
                                if (streamUrl && streamUrl.startsWith('http')) {
                                    episodeStreamingUrls.push({
                                        url: streamUrl,
                                        provider: providerName, // Use determined name
                                        language: language,
                                    });

                                    if (isProblematicUrl) {
                                        logger.info(`[Wiflix Detail Final] Added streaming URL: ${streamUrl} (${providerName})`);
                                    }
                                } else {
                                    logger.warn(`[Wiflix Detail Final] Invalid streaming URL: "${streamUrl}"`);
                                }
                            } else if (isProblematicUrl) {
                                // For problematic URLs, try a more aggressive approach
                                // Look for any URL in the onclick attribute
                                const anyUrlMatch = onclickAttr.match(/https?:\/\/[^'"]+/);
                                if (anyUrlMatch && anyUrlMatch[0]) {
                                    const streamUrl = anyUrlMatch[0].trim();
                                    const providerName = getProviderFromUrl(streamUrl);

                                    logger.info(`[Wiflix Detail Final] Found URL with aggressive matching: ${streamUrl}`);

                                    episodeStreamingUrls.push({
                                        url: streamUrl,
                                        provider: providerName,
                                        language: language,
                                    });
                                } else {
                                    logger.warn(`[Wiflix Detail Final] Could not extract URL from onclick: "${onclickAttr}"`);
                                }
                            }
                        } catch (error) {
                            logger.error(`[Wiflix Detail Final] Error processing streaming link: ${error.message}`);
                        }
                    });

                    if (episodeStreamingUrls.length > 0) {
                         let existingEp = episodes.find(e => String(e.episodeNumber) === String(episodeNumber) && String(e.season) === String(season));
                         if (existingEp) {
                             existingEp.streamingUrls.push(...episodeStreamingUrls);
                              if (existingEp.language === 'unknown' && language !== 'unknown') {
                                 existingEp.language = language;
                             } else if (existingEp.language !== language && existingEp.language !== 'Multi') {
                                 existingEp.language = 'Multi';
                             }
                         } else {
                             episodes.push({
                                episodeNumber: String(episodeNumber),
                                season: String(season),
                                language: language,
                                streamingUrls: episodeStreamingUrls
                             });
                         }
                    } else {
                         if(linksContainer.length > 0) {
                            logger.warn(`[Wiflix Detail Final] No streaming URLs found in container .${relAttr} for Ep ${episodeNumber} (${language}) on ${detailUrl}`);
                        } else {
                             logger.warn(`[Wiflix Detail Final] Could not find links container .${relAttr} for Ep ${episodeNumber} (${language}) on ${detailUrl}`);
                        }
                    }
                });
            });

             episodes.sort((a, b) => {
                const numA = parseInt(a.episodeNumber, 10);
                const numB = parseInt(b.episodeNumber, 10);
                if (isNaN(numA) || isNaN(numB)) return String(a.episodeNumber).localeCompare(String(b.episodeNumber));
                return numA - numB;
             });

        } else {
            // Movie Logic - Look inside `.tabsbox .linkstab` with more flexible selectors
            logger.debug(`[Wiflix Detail Final] No episode blocks found, processing as Movie.`);

            // Try multiple selectors for movie streaming links
            const movieLinkSelectors = [
                '.tabsbox .linkstab a[onclick*="loadVideo"]',
                '.tabsbox a[onclick*="loadVideo"]',
                '.linkstab a[onclick*="loadVideo"]',
                'a[onclick*="loadVideo"]',
                '.player-option',
                '.server-item',
                '.streaming-server',
                '.play-btn',
                'a[data-link]',
                'a[data-url]',
                'a[href*="embed"]',
                'a[href*="player"]',
                'a[href*="stream"]',
                'a[href*="watch"]'
            ];

            let movieServerLinks = $();

            // Try each selector until we find links
            for (const selector of movieLinkSelectors) {
                const links = $(selector);
                logger.info(`[Wiflix Detail Final] Selector '${selector}' found ${links.length} movie server links`);

                if (links.length > 0) {
                    movieServerLinks = links;
                    break;
                }
            }

            // If we found links, process them
            if (movieServerLinks.length > 0) {
                movieServerLinks.each((_i, link) => {
                    try {
                        const element = $(link);
                        const onclickAttr = element.attr('onclick') || '';
                        const dataLink = element.attr('data-link') || element.attr('data-url') || '';
                        const href = element.attr('href') || '';

                        // Try to extract URL from onclick attribute
                        let streamUrl = null;
                        let urlMatch = onclickAttr.match(/loadVideo\(['"]([^'"]+)['"]\)/);

                        if (urlMatch && urlMatch[1]) {
                            streamUrl = urlMatch[1].trim();
                        } else if (dataLink && dataLink.startsWith('http')) {
                            // Try data-link or data-url attribute
                            streamUrl = dataLink;
                        } else if (href && href.startsWith('http') && !href.includes('javascript:')) {
                            // Try href attribute
                            streamUrl = href;
                        } else if (onclickAttr) {
                            // Try to find any URL in the onclick attribute
                            const anyUrlMatch = onclickAttr.match(/https?:\/\/[^'"]+/);
                            if (anyUrlMatch && anyUrlMatch[0]) {
                                streamUrl = anyUrlMatch[0].trim();
                            }
                        }

                        // If we found a URL, add it to streamingUrls
                        if (streamUrl) {
                            const hostText = element.find('span').first().text().trim() || element.text().trim(); // Get text like "DdStream", "Voe"
                            const linkText = element.text().toLowerCase(); // For language detection
                            const langMatch = linkText.includes('vostfr') ? 'VOSTFR' : (linkText.includes('vf') || linkText.includes('french') || linkText.includes('truefrench') ? 'VF' : 'unknown'); // Improved language check

                            let providerName = hostText; // Trust the text first for movies
                            if (!providerName || providerName.toLowerCase().startsWith('lecteur')) {
                                providerName = getProviderFromUrl(streamUrl); // Fallback to URL parsing
                            } else {
                                // Basic capitalization
                                providerName = providerName.charAt(0).toUpperCase() + providerName.slice(1).toLowerCase();
                            }

                            streamingUrls.push({
                                url: streamUrl,
                                provider: providerName, // Use extracted/parsed name
                                language: langMatch,
                            });

                            logger.info(`[Wiflix Detail Final] Added streaming URL: ${streamUrl} (${providerName})`);
                        } else {
                            logger.warn(`[Wiflix Detail Final] Could not extract URL from movie link: ${element.html()}`);
                        }
                    } catch (error) {
                        logger.error(`[Wiflix Detail Final] Error processing movie link: ${error.message}`);
                    }
                });
            } else {
                // If no links found with selectors, try a more aggressive approach
                logger.warn(`[Wiflix Detail Final] No movie server links found with standard selectors, trying more aggressive approach`);

                // Try to find any links that might be streaming links
                const allLinks = $('a[href^="http"]');
                logger.info(`[Wiflix Detail Final] Found ${allLinks.length} links that might be streaming links`);

                if (allLinks.length > 0) {
                    // Filter links that look like streaming links
                    allLinks.each((_i, link) => {
                        try {
                            const element = $(link);
                            const href = element.attr('href');

                            if (href && (
                                href.includes('embed') ||
                                href.includes('player') ||
                                href.includes('stream') ||
                                href.includes('watch') ||
                                href.includes('video') ||
                                href.includes('play')
                            )) {
                                const streamUrl = href;
                                const providerName = getProviderFromUrl(streamUrl);

                                streamingUrls.push({
                                    url: streamUrl,
                                    provider: providerName,
                                    language: 'unknown',
                                });

                                logger.info(`[Wiflix Detail Final] Added streaming URL (aggressive): ${streamUrl} (${providerName})`);
                            }
                        } catch (error) {
                            logger.error(`[Wiflix Detail Final] Error processing link in aggressive approach: ${error.message}`);
                        }
                    });
                }

                if (streamingUrls.length === 0) {
                    logger.warn(`[Wiflix Detail Final] No streaming URLs found for ${detailUrl} using any method`);
                }
            }
        }
        // --- END Logic ---

        // Enhanced result logging
        logger.info(`[Wiflix Detail Final] Scraped: ${detailUrl.substring(0,100)}... | Episodes: ${episodes.length}, Movie Streams: ${streamingUrls.length}`);

        // For problematic URLs, log more details about the results
        if (isProblematicUrl) {
            logger.info(`[Wiflix Detail Final] Detailed results for problematic URL:`);

            if (episodes.length > 0) {
                const episodeSummary = episodes.map(ep =>
                    `S${ep.season}:E${ep.episodeNumber} (${ep.streamingUrls.length} streams)`
                ).join(', ');
                logger.info(`[Wiflix Detail Final] Episodes found: ${episodeSummary}`);
            } else if (streamingUrls.length > 0) {
                const streamSummary = streamingUrls.map(s => s.provider).join(', ');
                logger.info(`[Wiflix Detail Final] Stream providers: ${streamSummary}`);
            } else {
                logger.warn(`[Wiflix Detail Final] No episodes or streams found for problematic URL`);
            }

            // Log metadata
            const metadataKeys = Object.keys(metadata);
            if (metadataKeys.length > 0) {
                logger.info(`[Wiflix Detail Final] Metadata keys found: ${metadataKeys.join(', ')}`);
            } else {
                logger.warn(`[Wiflix Detail Final] No metadata found for problematic URL`);
            }
        }

        const result = {
            streamingUrls: streamingUrls, // Structured for movies
            metadata,
            episodes: episodes, // Structured for series
            thumbnail: thumbnail || undefined, // Keep full URL
            thumbnailPath: thumbnailPath || undefined, // Add path
        };

        return result;

    } catch (error) {
        logger.error(`[Wiflix Detail Final] Failed Puppeteer scrape for ${detailUrl}: ${error.message}`, { stack: error.stack });

        // For problematic URLs, provide more detailed error information
        if (isProblematicUrl) {
            logger.error(`[Wiflix Detail Final] Detailed error for problematic URL: ${error.stack}`);

            // Try one last approach for problematic URLs - use a different scraping strategy
            try {
                logger.info(`[Wiflix Detail Final] Attempting alternative scraping approach for problematic URL`);

                // This could be implemented with a different scraping strategy
                // For now, we'll just return empty results
                logger.info(`[Wiflix Detail Final] Alternative approach not implemented yet`);
            } catch (altError) {
                logger.error(`[Wiflix Detail Final] Alternative approach failed: ${altError.message}`);
            }
        }

        // Return empty object with path field included
        return { streamingUrls: [], metadata: {}, episodes: [], thumbnail: undefined, thumbnailPath: undefined };
    }
}

module.exports = { scrapeWiflixDetail };