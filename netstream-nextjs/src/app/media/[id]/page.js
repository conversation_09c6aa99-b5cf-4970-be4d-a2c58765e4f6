const handleWatchClick = (provider) => {
  if (!provider?.url) {
    console.error('No provider URL found:', provider);
    return;
  }
  
  // For movies, use the provider's streaming URL directly
  if (media?.type === 'MOVIE') {
    window.open(provider.url, '_blank');
    return;
  }
  
  // For series/anime, use the provider's streaming URL
  if (media?.type === 'SERIES' || media?.type === 'ANIME') {
    window.open(provider.url, '_blank');
    return;
  }
}; 