import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/mongodb';

export async function POST() {
  try {
    const db = await connectToDatabase();
    const optimizationResults = {
      operations: [],
      statistics: {},
      timestamp: new Date().toISOString()
    };

    // Get database statistics before optimization
    const collections = ['movies', 'series', 'animes', 'livetv'];
    const beforeStats = {};
    
    for (const collection of collections) {
      try {
        const stats = await db.collection(collection).stats();
        beforeStats[collection] = {
          count: stats.count,
          size: stats.size,
          avgObjSize: stats.avgObjSize,
          indexes: stats.nindexes
        };
      } catch (error) {
        console.warn(`Could not get stats for ${collection}:`, error.message);
      }
    }

    // 1. Ensure indexes exist for better performance
    console.log('Creating/updating database indexes...');
    
    try {
      // Movies collection indexes
      await db.collection('movies').createIndex({ title: 1 });
      await db.collection('movies').createIndex({ 'tmdb.id': 1 });
      await db.collection('movies').createIndex({ updatedAt: -1 });
      optimizationResults.operations.push('Created/updated movies indexes');
    } catch (error) {
      console.warn('Error creating movies indexes:', error.message);
    }

    try {
      // Series collection indexes
      await db.collection('series').createIndex({ title: 1 });
      await db.collection('series').createIndex({ 'tmdb.id': 1 });
      await db.collection('series').createIndex({ updatedAt: -1 });
      optimizationResults.operations.push('Created/updated series indexes');
    } catch (error) {
      console.warn('Error creating series indexes:', error.message);
    }

    try {
      // Anime collection indexes
      await db.collection('animes').createIndex({ title: 1 });
      await db.collection('animes').createIndex({ 'jikan.mal_id': 1 });
      await db.collection('animes').createIndex({ updatedAt: -1 });
      optimizationResults.operations.push('Created/updated anime indexes');
    } catch (error) {
      console.warn('Error creating anime indexes:', error.message);
    }

    try {
      // LiveTV collection indexes
      await db.collection('livetv').createIndex({ title: 1 });
      await db.collection('livetv').createIndex({ updatedAt: -1 });
      optimizationResults.operations.push('Created/updated livetv indexes');
    } catch (error) {
      console.warn('Error creating livetv indexes:', error.message);
    }

    // 2. Update collection statistics
    console.log('Updating collection statistics...');
    for (const collection of collections) {
      try {
        await db.collection(collection).aggregate([
          { $collStats: { latencyStats: { histograms: true } } }
        ]).toArray();
        optimizationResults.operations.push(`Updated statistics for ${collection}`);
      } catch (error) {
        console.warn(`Error updating statistics for ${collection}:`, error.message);
      }
    }

    // 3. Clean up orphaned documents (simplified)
    console.log('Cleaning up orphaned documents...');
    for (const collection of collections) {
      try {
        // Remove documents without titles (basic cleanup)
        const result = await db.collection(collection).deleteMany({
          title: { $exists: false }
        });
        if (result.deletedCount > 0) {
          optimizationResults.operations.push(`Cleaned up ${result.deletedCount} orphaned documents from ${collection}`);
        }
      } catch (error) {
        console.warn(`Error cleaning up ${collection}:`, error.message);
      }
    }

    // 4. Get final statistics
    const afterStats = {};
    for (const collection of collections) {
      try {
        const stats = await db.collection(collection).stats();
        afterStats[collection] = {
          count: stats.count,
          size: stats.size,
          avgObjSize: stats.avgObjSize,
          indexes: stats.nindexes
        };
      } catch (error) {
        console.warn(`Could not get final stats for ${collection}:`, error.message);
      }
    }

    // Calculate optimization results
    let totalSizeReduction = 0;
    let totalDocumentsRemoved = 0;
    
    for (const collection of collections) {
      if (beforeStats[collection] && afterStats[collection]) {
        const sizeReduction = beforeStats[collection].size - afterStats[collection].size;
        const documentsRemoved = beforeStats[collection].count - afterStats[collection].count;
        
        totalSizeReduction += sizeReduction;
        totalDocumentsRemoved += documentsRemoved;
      }
    }

    optimizationResults.statistics = {
      before: beforeStats,
      after: afterStats,
      improvements: {
        sizeReduction: `${Math.round(totalSizeReduction / 1024 / 1024 * 100) / 100} MB`,
        documentsRemoved: totalDocumentsRemoved,
        operationsPerformed: optimizationResults.operations.length
      }
    };

    console.log('Database optimization completed successfully');

    return NextResponse.json({
      success: true,
      message: 'Database optimization completed successfully',
      data: optimizationResults
    });

  } catch (error) {
    console.error('Database optimization error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to optimize database: ${error.message}`
    }, { status: 500 });
  }
} 