'use client'
import { useEffect, useRef } from 'react'

export default function DirectPlayerFix() {
  const observerRef = useRef(null)
  const fixAppliedRef = useRef(false)

  useEffect(() => {
    const fixPlayer = () => {
      // Check if we're using the modern player implementation
      const modernPlayerContainer = document.querySelector('.player-container')
      if (modernPlayerContainer) {
        console.log('Direct player fix: Modern player detected, skipping fix')
        return
      }

      const playerContainer = document.getElementById('player-container')
      if (!playerContainer) {
        console.log('Direct player fix: Player container not found')
        return
      }

      // Check if fix has already been applied
      if (fixAppliedRef.current) {
        console.log('Direct player fix: Fix already applied, skipping')
        return
      }

      let playerWrapper = document.getElementById('player-wrapper')

      if (!playerWrapper) {
        console.log('Direct player fix: Creating player wrapper')

        const player = document.getElementById('player')
        const playerIframe = document.getElementById('player-iframe')
        const closeButton = document.getElementById('close-player')

        if (!player && !playerIframe) {
          console.log('Direct player fix: Neither player nor iframe element found')
          return
        }

        playerWrapper = document.createElement('div')
        playerWrapper.id = 'player-wrapper'

        const playerLogo = document.createElement('div')
        playerLogo.id = 'player-logo'
        playerLogo.textContent = 'NetStream'

        const playerControls = document.createElement('div')
        playerControls.id = 'player-controls'
        playerControls.innerHTML = `
          <div id="player-title-bar">
            <div id="player-title">Now Playing</div>
          </div>

          <div id="player-progress-container">
            <div id="player-progress-buffer"></div>
            <div id="player-progress-bar"></div>
            <div id="player-time-tooltip">00:00</div>
          </div>

          <div id="player-buttons">
            <div class="player-button-group">
              <button id="player-play-pause" class="player-button" aria-label="Play/Pause">
                <i class="fas fa-play"></i>
              </button>

              <div id="player-volume-container">
                <button id="player-volume-toggle" class="player-button" aria-label="Mute/Unmute">
                  <i class="fas fa-volume-up"></i>
                </button>
                <div id="player-volume-slider">
                  <div id="player-volume-level"></div>
                </div>
              </div>

              <div id="player-time-display">
                <span id="player-current-time">00:00</span> / <span id="player-duration">00:00</span>
              </div>
            </div>

            <div class="player-button-group">
              <button id="player-settings" class="player-button" aria-label="Settings">
                <i class="fas fa-cog"></i>
              </button>
              <button id="player-fullscreen" class="player-button" aria-label="Fullscreen">
                <i class="fas fa-expand"></i>
              </button>
            </div>
          </div>
        `

        const settingsMenu = document.createElement('div')
        settingsMenu.id = 'player-settings-menu'
        settingsMenu.innerHTML = `
          <div class="player-settings-item" data-setting="quality">
            <span>Quality</span>
            <span id="player-quality-value">Auto</span>
          </div>
          <div class="player-settings-item" data-setting="speed">
            <span>Speed</span>
            <span id="player-speed-value">Normal</span>
          </div>
        `

        if (player) {
          playerContainer.removeChild(player)
          playerWrapper.appendChild(player)
        }
        
        if (playerIframe) {
          playerContainer.removeChild(playerIframe)
          playerWrapper.appendChild(playerIframe)
        }
        
        playerWrapper.appendChild(playerLogo)
        playerWrapper.appendChild(playerControls)
        playerWrapper.appendChild(settingsMenu)

        if (closeButton) {
          playerContainer.insertBefore(playerWrapper, closeButton)
        } else {
          playerContainer.appendChild(playerWrapper)
        }

        console.log('Direct player fix: Player wrapper created successfully')
        fixAppliedRef.current = true
      }

      if (window.modernPlayer && typeof window.modernPlayer.connectVideoElement === 'function') {
        if (window.modernPlayer.isConnecting) {
          console.log('Direct player fix: Already connecting video element, skipping')
          return
        }

        console.log('Direct player fix: Connecting video element to modern player')

        if (window.modernPlayer.refreshElements) {
          console.log('Direct player fix: Refreshing player elements')
          window.modernPlayer.refreshElements()
        }

        window.modernPlayer.connectVideoElement()
      }
    }

    // Delay the fix to ensure the DOM is fully loaded
    const timeoutId = setTimeout(fixPlayer, 500)

    observerRef.current = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const playerContainer = document.getElementById('player-container')
          if (playerContainer && !playerContainer.classList.contains('hidden')) {
            console.log('Direct player fix: Player container shown, applying fix')
            fixPlayer()
          }
        }
      })
    })

    const playerContainer = document.getElementById('player-container')
    if (playerContainer) {
      observerRef.current.observe(playerContainer, { attributes: true })
      console.log('Direct player fix: MutationObserver started')
    }

    return () => {
      clearTimeout(timeoutId)
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  return null
}
