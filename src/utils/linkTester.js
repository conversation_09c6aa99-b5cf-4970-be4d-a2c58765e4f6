const axios = require('axios');
const pLimit = require('p-limit');
const logger = require('./logger');
const limit = pLimit(5);

async function testLink(url) {
  try {
    const response = await limit(() => axios.head(url, { timeout: 5000 }));
    return response.status === 200;
  } catch (error) {
    logger.warn(`Link test failed for ${url}: ${error.message}`);
    return false;
  }
}

module.exports = { testLink };