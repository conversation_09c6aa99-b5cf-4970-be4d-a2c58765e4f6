/** @type {import('next').NextConfig} */
const nextConfig = {
  // This enables the standalone output mode, which creates a
  // minimal server for production deployment in Docker.
  output: 'standalone',
  experimental: {
    outputFileTracingRoot: undefined,
  },
  // Disable telemetry
  telemetry: false,
  // Optimize images
  images: {
    unoptimized: true, // For static export compatibility
  },
};

export default nextConfig;
