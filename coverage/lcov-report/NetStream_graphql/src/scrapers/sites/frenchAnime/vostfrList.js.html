
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for NetStream_graphql/src/scrapers/sites/frenchAnime/vostfrList.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">NetStream_graphql/src/scrapers/sites/frenchAnime</a> vostfrList.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/36</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/23</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/2</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/33</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// File: src/scrapers/sites/frenchAnime/vostfrList.js
const axios = <span class="cstat-no" title="statement not covered" >require('axios');</span>
const cheerio = <span class="cstat-no" title="statement not covered" >require('cheerio');</span>
const logger = <span class="cstat-no" title="statement not covered" >require('../../../utils/logger');</span>
const Config = <span class="cstat-no" title="statement not covered" >require('../../../db/models/Config');</span>
const { FRENCH_ANIME_BASE } = <span class="cstat-no" title="statement not covered" >require('../../../config/env');</span>
&nbsp;
async function <span class="fstat-no" title="function not covered" >scrapeFrenchAnimeVostfrList(</span>totalPages = <span class="branch-0 cbranch-no" title="branch not covered" >1)</span> {
  const animes = <span class="cstat-no" title="statement not covered" >[];</span>
  const maxPages = <span class="cstat-no" title="statement not covered" >totalPages &lt; 1 ? 1 : totalPages;</span> // Trust the passed value, no re-detection
&nbsp;
<span class="cstat-no" title="statement not covered" >  for (let page = <span class="cstat-no" title="statement not covered" >1;</span> page &lt;= maxPages; page++) {</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Get the latest FRENCH_ANIME_BASE from the database
      const frenchAnimeBase = <span class="cstat-no" title="statement not covered" >await Config.getValue('FRENCH_ANIME_BASE', FRENCH_ANIME_BASE);</span>
&nbsp;
      const url = <span class="cstat-no" title="statement not covered" >`https://${frenchAnimeBase}/animes-vostfr/page/${page}/`;</span>
<span class="cstat-no" title="statement not covered" >      logger.info(`Scraping French Anime VOSTFR list from: ${url}`);</span>
      const { data } = <span class="cstat-no" title="statement not covered" >await axios.get(url, { timeout: 10000 });</span>
      const $ = <span class="cstat-no" title="statement not covered" >cheerio.load(data);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      $('.mov.clearfix').each(<span class="fstat-no" title="function not covered" >(i</span>, el) =&gt; {</span>
        const title = <span class="cstat-no" title="statement not covered" >$(el).find('.mov-t.nowrap').text().trim();</span>
        const detailUrl = <span class="cstat-no" title="statement not covered" >$(el).find('.mov-mask').attr('data-link');</span>
        const image = <span class="cstat-no" title="statement not covered" >$(el).find('.mov-i img').attr('src');</span>
        const season = <span class="cstat-no" title="statement not covered" >$(el).find('.block-sai').text().match(/Saison\s*(\d+)/)?.[1] || '1';</span>
        const episode = <span class="cstat-no" title="statement not covered" >$(el).find('.block-ep').text().match(/Episode\s*(\d+)/)?.[1] || '1';</span>
        const animeLanguage = <span class="cstat-no" title="statement not covered" >'VOSTFR';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (title &amp;&amp; detailUrl) {</span>
          const absoluteDetailUrl = <span class="cstat-no" title="statement not covered" >detailUrl.startsWith('http') ? detailUrl : `https://${frenchAnimeBase}${detailUrl}`;</span>
<span class="cstat-no" title="statement not covered" >          animes.push({</span>
            title,
            detailUrl: absoluteDetailUrl,
            image: image?.startsWith('http') ? image : `https://${frenchAnimeBase}${image || '/default-image.jpg'}`,
            season,
            episodes: [{ episodeNumber: episode }],
            animeLanguage
          });
        } else {
<span class="cstat-no" title="statement not covered" >          logger.warn(`Skipping item on page ${page} due to missing title or detailUrl: title=${title}, detailUrl=${detailUrl}`);</span>
        }
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      logger.info(`Scraped French Anime VOSTFR page ${page} - Items: ${animes.length - (page - 1) * 10}`);</span>
<span class="cstat-no" title="statement not covered" >      if (animes.length === 0 &amp;&amp; page &gt; 1) <span class="cstat-no" title="statement not covered" >break; </span></span>// Stop if no items found on subsequent pages
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (error.response?.status === 404) <span class="cstat-no" title="statement not covered" >break; </span></span>// Exit on 404 (page not found)
<span class="cstat-no" title="statement not covered" >      logger.error(`French Anime VOSTFR scrape error on page ${page}: ${error.message}`);</span>
<span class="cstat-no" title="statement not covered" >      break;</span>
    }
  }
&nbsp;
<span class="cstat-no" title="statement not covered" >  logger.info(`Total VOSTFR animes scraped: ${animes.length}`);</span>
<span class="cstat-no" title="statement not covered" >  return animes;</span>
}
&nbsp;
<span class="cstat-no" title="statement not covered" >module.exports = { scrapeFrenchAnimeVostfrList };</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T16:31:40.526Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    