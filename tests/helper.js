// File: tests/helper.js
// Test helper for Fastify migration tests
// Provides utilities for testing the migrated server

const { MongoMemoryServer } = require('mongodb-memory-server');
const { MongoClient } = require('mongodb');
const fastify = require('fastify');

// Test configuration
const testConfig = {
  logger: false, // Disable logging during tests
  trustProxy: true,
  bodyLimit: 1048576 * 10,
  keepAliveTimeout: 30000,
  connectionTimeout: 30000
};

// Build test server
async function build(opts = {}) {
  const app = fastify({
    ...testConfig,
    ...opts
  });

  // Setup test database
  const mongoServer = new MongoMemoryServer();
  await mongoServer.start();
  const mongoUri = mongoServer.getUri();
  
  // Connect to test database
  const mongoClient = new MongoClient(mongoUri);
  await mongoClient.connect();
  const db = mongoClient.db();

  // Import services
  const FastifyDbService = require('../src/db/fastifyDbService');
  const FastifyDataLoaders = require('../src/graphql/dataLoaders');
  const FastifyCache = require('../src/cache/fastifyCache');

  // Initialize services
  const dbService = new FastifyDbService(db);
  const dataLoaders = new FastifyDataLoaders(db);
  
  // Mock cache service for tests
  const cacheService = {
    get: async () => null,
    set: async () => true,
    del: async () => true,
    getStats: () => ({ hits: 0, misses: 0, hitRate: '0%' }),
    healthCheck: async () => ({ status: 'healthy' })
  };

  // Register essential plugins
  await app.register(require('@fastify/cors'), {
    origin: true,
    credentials: true
  });

  await app.register(require('@fastify/compress'), {
    global: true,
    threshold: 1024
  });

  // Register rate limiting
  await app.register(require('@fastify/rate-limit'), {
    max: 1000,
    timeWindow: '1 minute'
  });

  // Mock metrics plugin
  try {
    await app.register(require('fastify-metrics'), {
      endpoint: '/metrics',
      defaultMetrics: { enabled: true }
    });
  } catch (error) {
    // Mock metrics endpoint if plugin not available
    app.get('/metrics', async () => {
      return '# Mock metrics for testing\ntest_metric 1\n';
    });
  }

  // Add services to request context
  app.addHook('onRequest', async (request, reply) => {
    request.db = db;
    request.mongoClient = mongoClient;
    request.dbService = dbService;
    request.dataLoaders = dataLoaders;
    request.cacheService = cacheService;
    request.startTime = Date.now();
  });

  // Add response time header
  app.addHook('onSend', async (request, reply, payload) => {
    const responseTime = Date.now() - request.startTime;
    reply.header('X-Response-Time', `${responseTime}ms`);
    return payload;
  });

  // Register GraphQL with minimal resolvers
  const minimalResolvers = require('../src/graphql/minimalResolvers');
  const { typeDefs } = require('../src/graphql/fastifySchema');

  await app.register(require('mercurius'), {
    schema: typeDefs,
    resolvers: minimalResolvers,
    graphiql: false,
    context: (request, reply) => ({
      request,
      reply,
      db: request.db,
      mongoClient: request.mongoClient,
      dbService: request.dbService,
      dataLoaders: request.dataLoaders,
      cacheService: request.cacheService
    })
  });

  // Register API routes
  await app.register(require('../src/routes/fastifyRoutes'));

  // Health check endpoint
  app.get('/health', async (request, reply) => {
    try {
      await db.admin().ping();
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        database: 'connected',
        cache: 'mocked'
      };
    } catch (error) {
      reply.code(503);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
        database: 'disconnected'
      };
    }
  });

  // API info endpoint
  app.get('/api', async () => ({
    name: 'NetStream API',
    version: '2.0.0',
    framework: 'Fastify',
    graphql: '/graphql',
    health: '/health',
    timestamp: new Date().toISOString()
  }));

  // Cache stats endpoint
  app.get('/cache/stats', async () => ({
    stats: cacheService.getStats(),
    health: await cacheService.healthCheck()
  }));

  // Cache clear endpoint
  app.post('/cache/clear', async (request, reply) => {
    const { adminToken } = request.body || {};
    
    if (!adminToken || adminToken !== 'test-admin-token') {
      reply.code(401);
      return { error: 'Unauthorized' };
    }
    
    return {
      message: 'Cache cleared (mocked)',
      cleared: 0
    };
  });

  // Performance endpoint
  app.get('/performance', async () => {
    const memoryUsage = process.memoryUsage();
    return {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024)
      },
      cache: cacheService.getStats()
    };
  });

  // Error handler
  app.setErrorHandler(async (error, request, reply) => {
    if (process.env.NODE_ENV === 'test') {
      reply.code(error.statusCode || 500);
      return {
        error: error.message,
        statusCode: error.statusCode || 500,
        timestamp: new Date().toISOString()
      };
    }
    throw error;
  });

  // 404 handler
  app.setNotFoundHandler(async (request, reply) => {
    reply.code(404);
    return {
      error: 'Not Found',
      statusCode: 404,
      message: `Route ${request.method} ${request.url} not found`,
      timestamp: new Date().toISOString()
    };
  });

  // Store test utilities
  app.testUtils = {
    mongoServer,
    mongoClient,
    db,
    dbService,
    dataLoaders,
    cacheService
  };

  // Cleanup function
  app.cleanup = async () => {
    await mongoClient.close();
    await mongoServer.stop();
  };

  return app;
}

// Seed test data
async function seedTestData(db) {
  // Add some test movies
  await db.collection('movies').insertMany([
    {
      title: 'Test Movie 1',
      cleanedTitle: 'test-movie-1',
      thumbnail: 'test-thumbnail-1.jpg',
      detailUrl: 'test-detail-url-1',
      streamingUrls: [
        { url: 'test-stream-1.m3u8', type: 'HLS', language: 'VF' }
      ],
      tmdb: { id: 12345, title: 'Test Movie 1', genres: ['Action', 'Drama'] },
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      title: 'Test Movie 2',
      cleanedTitle: 'test-movie-2',
      thumbnail: 'test-thumbnail-2.jpg',
      detailUrl: 'test-detail-url-2',
      streamingUrls: [
        { url: 'test-stream-2.m3u8', type: 'HLS', language: 'VOSTFR' }
      ],
      tmdb: { id: 12346, title: 'Test Movie 2', genres: ['Comedy'] },
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);

  // Add some test series
  await db.collection('series').insertMany([
    {
      title: 'Test Series 1',
      cleanedTitle: 'test-series-1',
      thumbnail: 'test-series-thumbnail-1.jpg',
      detailUrl: 'test-series-detail-url-1',
      episodes: [
        {
          episodeNumber: '1',
          title: 'Episode 1',
          streamingUrls: [
            { url: 'test-series-stream-1-1.m3u8', type: 'HLS', language: 'VF' }
          ]
        }
      ],
      tmdb: { id: 54321, title: 'Test Series 1', genres: ['Drama', 'Thriller'] },
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);

  // Add test config
  await db.collection('config').insertOne({
    tmdbApiKey: 'test-api-key',
    wiflixBase: 'test-wiflix.com',
    frenchAnimeBase: 'test-anime.com',
    witvBase: 'test-witv.com',
    createdAt: new Date(),
    updatedAt: new Date()
  });
}

module.exports = {
  build,
  seedTestData,
  testConfig
};
