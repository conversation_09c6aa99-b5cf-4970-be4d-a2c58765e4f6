'use client'
import { useWishlist } from '@/hooks/useWishlist'
import Carousel from '@/components/ui/Carousel'

export default function WishlistCarousels({ filterType = null }) {
  const { wishlistItems, groupedWishlist } = useWishlist()

  if (!wishlistItems || wishlistItems.length === 0) {
    return null
  }

  // If filterType is specified, only show that type
  if (filterType) {
    const items = groupedWishlist[filterType]
    if (!items || items.length === 0) {
      return null
    }

    return (
      <div className="mb-12">
        <Carousel
          title={`My Wishlist ${filterType.charAt(0).toUpperCase() + filterType.slice(1)}`}
          items={items}
          type={filterType}
        />
      </div>
    )
  }

  // Show all types (for home page)
  return (
    <div className="space-y-8 mb-12">
      <h2 className="text-2xl font-bold text-white">My Wishlist</h2>

      {groupedWishlist.movies && groupedWishlist.movies.length > 0 && (
        <Carousel
          title="Wishlist Movies"
          items={groupedWishlist.movies}
          type="movies"
        />
      )}

      {groupedWishlist.series && groupedWishlist.series.length > 0 && (
        <Carousel
          title="Wishlist Series"
          items={groupedWishlist.series}
          type="series"
        />
      )}

      {groupedWishlist.anime && groupedWishlist.anime.length > 0 && (
        <Carousel
          title="Wishlist Anime"
          items={groupedWishlist.anime}
          type="anime"
        />
      )}
    </div>
  )
}
