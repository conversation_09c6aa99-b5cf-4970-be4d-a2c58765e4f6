// src/services/configService.js
const Config = require('../db/models/Config');
const logger = require('../utils/logger');

class ConfigService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache
  }

  async getApiKey(keyName, fallbackValue = null) {
    try {
      const cacheKey = `api_key_${keyName}`;
      const cached = this.cache.get(cacheKey);
      
      // Check if cache is still valid
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        return cached.value;
      }

      // Get from database
      const value = await Config.getValue(keyName, fallbackValue);
      
      // Cache the result
      this.cache.set(cacheKey, {
        value,
        timestamp: Date.now()
      });

      return value;
    } catch (error) {
      logger.error(`Error getting API key ${keyName}: ${error.message}`);
      return fallbackValue;
    }
  }

  async getTmdbApiKey() {
    return await this.getApiKey('TMDB_API_KEY', process.env.TMDB_API_KEY);
  }

  async getGeminiApiKey() {
    return await this.getApiKey('GEMINI_API_KEY', process.env.GEMINI_API_KEY);
  }

  async getBaseUrl(keyName, fallbackValue = null) {
    try {
      const cacheKey = `base_url_${keyName}`;
      const cached = this.cache.get(cacheKey);
      
      // Check if cache is still valid
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        return cached.value;
      }

      // Get from database
      const value = await Config.getValue(keyName, fallbackValue);
      
      // Cache the result
      this.cache.set(cacheKey, {
        value,
        timestamp: Date.now()
      });

      return value;
    } catch (error) {
      logger.error(`Error getting base URL ${keyName}: ${error.message}`);
      return fallbackValue;
    }
  }

  // Clear cache for a specific key or all keys
  clearCache(keyName = null) {
    if (keyName) {
      this.cache.delete(`api_key_${keyName}`);
      this.cache.delete(`base_url_${keyName}`);
    } else {
      this.cache.clear();
    }
  }

  // Get all configuration values
  async getAllConfig() {
    try {
      const [tmdbApiKey, geminiApiKey, wiflixBase, frenchAnimeBase, witvBase] = await Promise.all([
        this.getTmdbApiKey(),
        this.getGeminiApiKey(),
        this.getBaseUrl('WIFLIX_BASE', process.env.WIFLIX_BASE || 'wiflix-max.cam'),
        this.getBaseUrl('FRENCH_ANIME_BASE', process.env.FRENCH_ANIME_BASE || 'french-anime.com'),
        this.getBaseUrl('WITV_BASE', process.env.WITV_BASE || 'witv.skin')
      ]);

      return {
        tmdbApiKey,
        geminiApiKey,
        wiflixBase,
        frenchAnimeBase,
        witvBase
      };
    } catch (error) {
      logger.error(`Error getting all config: ${error.message}`);
      return {
        tmdbApiKey: process.env.TMDB_API_KEY,
        geminiApiKey: process.env.GEMINI_API_KEY,
        wiflixBase: process.env.WIFLIX_BASE || 'wiflix-max.cam',
        frenchAnimeBase: process.env.FRENCH_ANIME_BASE || 'french-anime.com',
        witvBase: process.env.WITV_BASE || 'witv.skin'
      };
    }
  }
}

// Create a singleton instance
const configService = new ConfigService();

module.exports = configService;
