/**
 * Addic7ed.com Subtitle Scraper
 * Fetches subtitle download links from Addic7ed.com
 *
 * Website Structure:
 * - Main page: https://www.addic7ed.com/
 * - Show page: https://www.addic7ed.com/show/[show_id]
 * - Season page: https://www.addic7ed.com/show/[show_id]/[season]
 * - Episode page: https://www.addic7ed.com/show/[show_id]/[season]/[episode]
 * - Episode page (by name): https://www.addic7ed.com/serie/[show_name]/[season]/[episode]/[episode_name]
 * - Download link: https://www.addic7ed.com/original/[subtitle_id]/[version]
 *
 * Search functionality:
 * - Search URL: https://www.addic7ed.com/srch.php?search=[query]&Submit=Search
 *
 * Subtitle table structure:
 * - Each episode page contains tables with class "tabel95"
 * - Each table represents a version of subtitles
 * - Inside each table, rows with class "epeven" or "epodd" represent different languages
 * - Each row contains language, completion status, and download link
 */

const fetch = require('node-fetch');
const cheerio = require('cheerio');
const logger = require('../utils/logger');

/**
 * Normalize a string for comparison
 * @param {string} str - String to normalize
 * @returns {string} Normalized string
 */
function normalizeString(str) {
  return str.toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove special characters
    .replace(/\s+/g, ' ')    // Replace multiple spaces with a single space
    .trim();
}

/**
 * Search for a show on Addic7ed.com
 * @param {string} showName - Name of the show
 * @returns {Promise<{id: string, name: string}|null>} Show ID and name or null if not found
 */
async function searchShow(showName) {
  try {
    // Normalize the show name for URL
    const urlShowName = showName.replace(/\s+/g, '_');

    // Try direct URL first (faster)
    const directUrl = `https://www.addic7ed.com/serie/${urlShowName}`;
    let response = await fetch(directUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html',
        'Accept-Language': 'en-US,en;q=0.9'
      }
    });

    // If direct URL works, extract the show ID
    if (response.ok) {
      const html = await response.text();
      const $ = cheerio.load(html);
      const showIdMatch = html.match(/\/show\/(\d+)/);

      if (showIdMatch && showIdMatch[1]) {
        return {
          id: showIdMatch[1],
          name: $('title').text().split('-')[0].trim()
        };
      }
    }

    // If direct URL fails, try search
    const searchUrl = `https://www.addic7ed.com/srch.php?search=${encodeURIComponent(showName)}&Submit=Search`;
    response = await fetch(searchUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html',
        'Accept-Language': 'en-US,en;q=0.9'
      }
    });

    if (response.ok) {
      const html = await response.text();
      const $ = cheerio.load(html);

      // Find the first show link
      const showLink = $('a[href^="/show/"]').first();
      if (showLink.length) {
        const href = showLink.attr('href');
        const showIdMatch = href.match(/\/show\/(\d+)/);

        if (showIdMatch && showIdMatch[1]) {
          return {
            id: showIdMatch[1],
            name: showLink.text().trim()
          };
        }
      }
    }

    return null;
  } catch (error) {
    logger.error(`Error searching for show on Addic7ed: ${error.message}`);
    return null;
  }
}

/**
 * Get episode page URL
 * @param {string} showId - Show ID
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @returns {string} Episode page URL
 */
function getEpisodeUrl(showId, season, episode) {
  return `https://www.addic7ed.com/serie/${showId}/${season}/${episode}`;
}

/**
 * Get episode page URL by name
 * @param {string} showName - Show name
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @param {string} episodeTitle - Episode title (optional)
 * @returns {string} Episode page URL
 */
function getEpisodeUrlByName(showName, season, episode, episodeTitle = '') {
  const urlShowName = showName.replace(/\s+/g, '_');
  let url = `https://www.addic7ed.com/serie/${urlShowName}/${season}/${episode}`;

  if (episodeTitle) {
    const urlEpisodeTitle = episodeTitle.replace(/\s+/g, '_');
    url += `/${urlEpisodeTitle}`;
  }

  return url;
}

/**
 * Extract subtitle download links from episode page
 * @param {string} html - HTML content of the episode page
 * @param {string} language - Language to filter by (e.g., 'English', 'French')
 * @returns {Array<{language: string, version: string, download: string, hearing_impaired: boolean}>} Array of subtitle info
 */
function extractSubtitleLinks(html, language = '') {
  const $ = cheerio.load(html);
  const subtitles = [];

  // Find all subtitle tables
  $('table.tabel95').each((i, table) => {
    const $table = $(table);
    const version = $table.find('td.NewsTitle').text().match(/Version (.*?),/)?.[1] || '';

    // Find all language rows
    $table.find('tr.epeven, tr.epodd').each((j, row) => {
      const $row = $(row);
      const rowLanguage = $row.find('td:nth-child(4)').text().trim();

      // Skip if language doesn't match (if specified)
      if (language && normalizeString(rowLanguage) !== normalizeString(language)) {
        return;
      }

      // Check if completed
      const status = $row.find('td:nth-child(6)').text().trim();
      if (status !== 'Completed') {
        return;
      }

      // Get download link
      const downloadLink = $row.find('a.buttonDownload').attr('href');
      if (!downloadLink) {
        return;
      }

      // Check if hearing impaired
      const hearingImpaired = $row.find('img[title="Hearing Impaired"]').length > 0;

      subtitles.push({
        language: rowLanguage,
        version: version,
        download: `https://www.addic7ed.com${downloadLink}`,
        hearing_impaired: hearingImpaired
      });
    });
  });

  return subtitles;
}

/**
 * Get subtitles for a specific episode
 * @param {string} showName - Show name
 * @param {number} season - Season number
 * @param {number} episode - Episode number
 * @param {string} language - Language to filter by (optional)
 * @returns {Promise<Array>} Array of subtitle info
 */
async function getSubtitles(showName, season, episode, language = '') {
  try {
    logger.info(`Searching for subtitles: ${showName} S${season}E${episode} [${language || 'all languages'}]`);

    // Common headers for all requests
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'Accept-Language': 'en-US,en;q=0.9',
      'Referer': 'https://www.addic7ed.com/',
      'Connection': 'keep-alive',
      'Cache-Control': 'max-age=0'
    };

    // Try multiple approaches to find the episode

    // Approach 1: Try direct URL with show name
    const episodeUrl = getEpisodeUrlByName(showName, season, episode);
    logger.info(`Approach 1: Trying direct URL: ${episodeUrl}`);

    try {
      const response = await fetch(episodeUrl, { headers });

      if (response.ok) {
        const html = await response.text();

        // Check if the page contains subtitle tables
        if (html.includes('tabel95') && html.includes('buttonDownload')) {
          logger.info(`Found subtitles using direct URL approach`);
          return extractSubtitleLinks(html, language);
        }
      }
    } catch (error) {
      logger.warn(`Error with direct URL approach: ${error.message}`);
    }

    // Approach 2: Search for the show first
    logger.info(`Approach 2: Searching for show: ${showName}`);
    const show = await searchShow(showName);

    if (show) {
      logger.info(`Found show: ${show.name} (ID: ${show.id})`);

      // Get episode page with show ID
      const episodeUrlWithId = getEpisodeUrl(show.id, season, episode);
      logger.info(`Trying URL with show ID: ${episodeUrlWithId}`);

      try {
        const response = await fetch(episodeUrlWithId, { headers });

        if (response.ok) {
          const html = await response.text();

          // Check if the page contains subtitle tables
          if (html.includes('tabel95') && html.includes('buttonDownload')) {
            logger.info(`Found subtitles using show ID approach`);
            return extractSubtitleLinks(html, language);
          }
        }
      } catch (error) {
        logger.warn(`Error with show ID approach: ${error.message}`);
      }
    } else {
      logger.warn(`Show not found on Addic7ed: ${showName}`);
    }

    // Approach 3: Try alternative show name formats
    const alternativeNames = generateAlternativeNames(showName);
    logger.info(`Approach 3: Trying alternative show names: ${alternativeNames.join(', ')}`);

    for (const altName of alternativeNames) {
      if (altName === showName) continue; // Skip the original name

      logger.info(`Trying alternative name: ${altName}`);

      // Try direct URL with alternative name
      const altEpisodeUrl = getEpisodeUrlByName(altName, season, episode);

      try {
        const response = await fetch(altEpisodeUrl, { headers });

        if (response.ok) {
          const html = await response.text();

          // Check if the page contains subtitle tables
          if (html.includes('tabel95') && html.includes('buttonDownload')) {
            logger.info(`Found subtitles using alternative name: ${altName}`);
            return extractSubtitleLinks(html, language);
          }
        }
      } catch (error) {
        logger.warn(`Error with alternative name approach: ${error.message}`);
      }

      // Try searching for the alternative show name
      const altShow = await searchShow(altName);

      if (altShow) {
        logger.info(`Found show with alternative name: ${altShow.name} (ID: ${altShow.id})`);

        // Get episode page with show ID
        const altEpisodeUrlWithId = getEpisodeUrl(altShow.id, season, episode);

        try {
          const response = await fetch(altEpisodeUrlWithId, { headers });

          if (response.ok) {
            const html = await response.text();

            // Check if the page contains subtitle tables
            if (html.includes('tabel95') && html.includes('buttonDownload')) {
              logger.info(`Found subtitles using alternative name show ID approach`);
              return extractSubtitleLinks(html, language);
            }
          }
        } catch (error) {
          logger.warn(`Error with alternative name show ID approach: ${error.message}`);
        }
      }
    }

    // If all approaches fail, return empty array
    logger.warn(`No subtitles found for ${showName} S${season}E${episode} after trying all approaches`);
    return [];
  } catch (error) {
    logger.error(`Error fetching subtitles from Addic7ed: ${error.message}`);
    return [];
  }
}

/**
 * Generate alternative names for a show
 * @param {string} showName - Original show name
 * @returns {Array<string>} Array of alternative names
 */
function generateAlternativeNames(showName) {
  const alternatives = [showName];

  // Remove "The" from the beginning
  if (showName.toLowerCase().startsWith('the ')) {
    alternatives.push(showName.substring(4));
  }

  // Add "The" to the beginning
  if (!showName.toLowerCase().startsWith('the ')) {
    alternatives.push(`The ${showName}`);
  }

  // Replace spaces with dots
  alternatives.push(showName.replace(/\s+/g, '.'));

  // Replace spaces with hyphens
  alternatives.push(showName.replace(/\s+/g, '-'));

  // Remove special characters
  alternatives.push(showName.replace(/[^\w\s]/g, ''));

  // Convert to lowercase
  alternatives.push(showName.toLowerCase());

  // Remove duplicate alternatives
  return [...new Set(alternatives)];
}

module.exports = {
  getSubtitles,
  searchShow,
  normalizeString,
  generateAlternativeNames
};
