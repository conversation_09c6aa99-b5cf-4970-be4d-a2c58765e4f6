#!/usr/bin/env node

// Docker startup script for NetStream backend
// Ensures proper initialization of database and config service

const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Config = require('../src/db/models/Config');

async function initializeConfig() {
  try {
    logger.info('🚀 Initializing NetStream configuration for Docker environment...');
    
    // Wait for MongoDB connection
    if (mongoose.connection.readyState !== 1) {
      logger.info('⏳ Waiting for MongoDB connection...');
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('MongoDB connection timeout'));
        }, 30000); // 30 second timeout
        
        mongoose.connection.on('connected', () => {
          clearTimeout(timeout);
          resolve();
        });
        
        mongoose.connection.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });
    }
    
    logger.info('✅ MongoDB connected successfully');
    
    // Initialize default configuration values
    const defaultConfig = {
      'WIFLIX_BASE': process.env.WIFLIX_BASE || 'wiflix-max.cam',
      'FRENCH_ANIME_BASE': process.env.FRENCH_ANIME_BASE || 'french-anime.com',
      'WITV_BASE': process.env.WITV_BASE || 'witv.skin',
      'TMDB_API_KEY': process.env.TMDB_API_KEY || '',
      'GEMINI_API_KEY': process.env.GEMINI_API_KEY || '',
      'GRID_ITEMS_ENABLED': 'true'
    };
    
    logger.info('📝 Initializing default configuration values...');
    await Config.initializeDefaults(defaultConfig);
    
    // Verify configuration
    const configCount = await Config.countDocuments();
    logger.info(`✅ Configuration initialized with ${configCount} entries`);
    
    // Test config service
    const configService = require('../src/services/configService');
    const tmdbKey = await configService.getTmdbApiKey();
    const geminiKey = await configService.getGeminiApiKey();
    
    logger.info('🔑 API Key Status:');
    logger.info(`  - TMDB API Key: ${tmdbKey ? '✅ Configured' : '❌ Not configured'}`);
    logger.info(`  - Gemini API Key: ${geminiKey ? '✅ Configured' : '❌ Not configured'}`);
    
    logger.info('🎉 NetStream configuration initialization completed successfully!');
    
  } catch (error) {
    logger.error('❌ Configuration initialization failed:', error.message);
    throw error;
  }
}

// Export for use in other scripts
module.exports = { initializeConfig };

// Run if called directly
if (require.main === module) {
  initializeConfig()
    .then(() => {
      logger.info('✅ Docker startup initialization completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('❌ Docker startup initialization failed:', error);
      process.exit(1);
    });
}
