const Anime = require('../../../db/models/Anime');
const { getPaginatedData } = require('../../../utils/pagination');

const resolvers = {
    Query: {
        animes: async (_, { filter, sort, page, limit }) => {
            const query = {};

            if (filter) {
                if (filter.search) {
                    query.$or = [
                        { title: { $regex: filter.search, $options: 'i' } },
                        { 'jikan.title.english': { $regex: filter.search, $options: 'i' } },
                        { 'jikan.title.japanese': { $regex: filter.search, $options: 'i' } },
                        { 'jikan.title.synonyms': { $regex: filter.search, $options: 'i' } }
                    ];
                }

                if (filter.genre && filter.genre.length > 0) {
                    const genreQueries = filter.genre.map(genre => {
                        const lowerCaseGenre = genre.toLowerCase();
                        return {
                            $or: [
                                { 'jikan.genres': { $elemMatch: { name: { $regex: lowerCaseGenre, $options: 'i' } } } },
                                { 'metadata.genre': { $regex: lowerCaseGenre, $options: 'i' } },
                                { 'tmdb.genres': { $in: [lowerCaseGenre] } }
                            ]
                        };
                    });

                    if (query.$or) {
                        query.$and = genreQueries;
                    } else {
                        query.$or = genreQueries;
                    }
                    
                }

                if (filter.type) {
                    query['jikan.type'] = filter.type;
                }

                if (filter.year) {
                    query['jikan.year'] = filter.year;
                }
                if (filter.status) {
                  query['jikan.status'] = filter.status;
              }
                if (filter.score) {
                  query['jikan.score'] = {$gte: filter.score};
              }
            }
            return getPaginatedData(Anime, query, sort, page, limit);
        }
    }
};

module.exports = resolvers;