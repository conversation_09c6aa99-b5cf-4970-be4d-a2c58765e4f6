// File: src/jobs/fastifyJobQueue.js
// BullMQ Job Queue Implementation for Fastify
// High-performance job processing with improved concurrency

const { Queue, Worker, QueueScheduler } = require('bullmq');
const Redis = require('ioredis');

class FastifyJobQueue {
  constructor(redisConfig = {}) {
    // Determine Redis host based on environment
    let redisHost = process.env.REDIS_HOST;
    if (!redisHost) {
      if (process.env.NODE_ENV === 'production') {
        // In production, try to use the Redis service URL if available
        redisHost = process.env.REDIS_URL || process.env.REDIS_SERVICE_URL || 'localhost';
      } else {
        redisHost = 'localhost';
      }
    }

    this.redisConfig = {
      host: redisHost,
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: process.env.REDIS_JOB_DB || 1, // Use different DB for jobs
      maxRetriesPerRequest: 2, // Reduced retries
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      lazyConnect: true,
      connectTimeout: 10000, // 10 second connection timeout
      commandTimeout: 5000, // 5 second command timeout
      ...redisConfig
    };

    this.connection = new Redis(this.redisConfig);
    this.queues = new Map();
    this.workers = new Map();
    this.schedulers = new Map();
    
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    this.connection.on('connect', () => {
      console.log('Job queue Redis connected');
    });

    this.connection.on('error', (error) => {
      console.error('Job queue Redis error:', error.message);
    });

    this.connection.on('ready', () => {
      console.log('Job queue Redis ready');
    });

    this.connection.on('timeout', () => {
      console.error('Job queue Redis connection timeout');
    });
  }

  // Create or get a queue
  getQueue(name, options = {}) {
    if (this.queues.has(name)) {
      return this.queues.get(name);
    }

    const defaultOptions = {
      connection: this.redisConfig,
      defaultJobOptions: {
        removeOnComplete: 10,
        removeOnFail: 5,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        delay: 0,
        ...options.defaultJobOptions
      }
    };

    const queue = new Queue(name, { ...defaultOptions, ...options });
    this.queues.set(name, queue);

    // Create scheduler for this queue
    const scheduler = new QueueScheduler(name, { connection: this.redisConfig });
    this.schedulers.set(name, scheduler);

    return queue;
  }

  // Create a worker for a queue
  createWorker(queueName, processor, options = {}) {
    const defaultOptions = {
      connection: this.redisConfig,
      concurrency: 5,
      maxStalledCount: 1,
      stalledInterval: 30000,
      ...options
    };

    const worker = new Worker(queueName, processor, defaultOptions);
    this.workers.set(queueName, worker);

    // Setup worker event handlers
    worker.on('completed', (job) => {
      console.log(`Job ${job.id} completed in queue ${queueName}`);
    });

    worker.on('failed', (job, err) => {
      console.error(`Job ${job.id} failed in queue ${queueName}:`, err);
    });

    worker.on('stalled', (jobId) => {
      console.warn(`Job ${jobId} stalled in queue ${queueName}`);
    });

    worker.on('error', (err) => {
      console.error(`Worker error in queue ${queueName}:`, err);
    });

    return worker;
  }

  // Add a job to a queue
  async addJob(queueName, jobName, data, options = {}) {
    const queue = this.getQueue(queueName);
    
    const jobOptions = {
      priority: 0,
      delay: 0,
      ...options
    };

    try {
      const job = await queue.add(jobName, data, jobOptions);
      console.log(`Job ${job.id} added to queue ${queueName}`);
      return job;
    } catch (error) {
      console.error(`Failed to add job to queue ${queueName}:`, error);
      throw error;
    }
  }

  // Add a recurring job
  async addRecurringJob(queueName, jobName, data, cronPattern, options = {}) {
    const queue = this.getQueue(queueName);
    
    const jobOptions = {
      repeat: {
        pattern: cronPattern,
        ...options.repeat
      },
      ...options
    };

    try {
      const job = await queue.add(jobName, data, jobOptions);
      console.log(`Recurring job ${job.id} added to queue ${queueName} with pattern ${cronPattern}`);
      return job;
    } catch (error) {
      console.error(`Failed to add recurring job to queue ${queueName}:`, error);
      throw error;
    }
  }

  // Get job statistics
  async getQueueStats(queueName) {
    const queue = this.getQueue(queueName);
    
    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed()
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        total: waiting.length + active.length + completed.length + failed.length + delayed.length
      };
    } catch (error) {
      console.error(`Failed to get stats for queue ${queueName}:`, error);
      return null;
    }
  }

  // Clean old jobs
  async cleanQueue(queueName, grace = 24 * 60 * 60 * 1000) { // 24 hours default
    const queue = this.getQueue(queueName);
    
    try {
      await Promise.all([
        queue.clean(grace, 10, 'completed'),
        queue.clean(grace, 10, 'failed')
      ]);
      
      console.log(`Cleaned old jobs from queue ${queueName}`);
    } catch (error) {
      console.error(`Failed to clean queue ${queueName}:`, error);
    }
  }

  // Pause a queue
  async pauseQueue(queueName) {
    const queue = this.getQueue(queueName);
    await queue.pause();
    console.log(`Queue ${queueName} paused`);
  }

  // Resume a queue
  async resumeQueue(queueName) {
    const queue = this.getQueue(queueName);
    await queue.resume();
    console.log(`Queue ${queueName} resumed`);
  }

  // Get all queue names
  getQueueNames() {
    return Array.from(this.queues.keys());
  }

  // Get worker for a queue
  getWorker(queueName) {
    return this.workers.get(queueName);
  }

  // Close all connections
  async close() {
    console.log('Closing job queue connections...');
    
    // Close all workers
    const workerClosePromises = Array.from(this.workers.values()).map(worker => worker.close());
    await Promise.all(workerClosePromises);
    
    // Close all schedulers
    const schedulerClosePromises = Array.from(this.schedulers.values()).map(scheduler => scheduler.close());
    await Promise.all(schedulerClosePromises);
    
    // Close all queues
    const queueClosePromises = Array.from(this.queues.values()).map(queue => queue.close());
    await Promise.all(queueClosePromises);
    
    // Close Redis connection
    await this.connection.quit();
    
    console.log('Job queue connections closed');
  }

  // Health check
  async healthCheck() {
    try {
      await this.connection.ping();
      return {
        status: 'healthy',
        queues: this.getQueueNames().length,
        workers: this.workers.size,
        connection: 'connected'
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        connection: 'disconnected'
      };
    }
  }
}

module.exports = FastifyJobQueue;
