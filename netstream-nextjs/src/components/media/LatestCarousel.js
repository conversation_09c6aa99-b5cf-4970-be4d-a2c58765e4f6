'use client'
import { useState, useEffect } from 'react'
import Carousel from '@/components/ui/Carousel'

export default function LatestCarousel({ type }) {
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchLatestItems = async () => {
      try {
        console.log(`🔄 LatestCarousel: Fetching latest ${type}...`)
        setLoading(true)
        setError(null)

        // Fetch from our API route
        const response = await fetch(`/api/latest/${type}`)
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const result = await response.json()
        console.log(`📦 LatestCarousel: API result for ${type}:`, result)
        
        if (result.success && result.data) {
          console.log(`✅ LatestCarousel: Setting ${result.data.length} latest ${type} items`)
          setItems(result.data)
        } else {
          console.error(`❌ LatestCarousel: API error for ${type}:`, result.error)
          setError(result.error || 'Unknown API error')
        }
        
        setLoading(false)
        
      } catch (error) {
        console.error(`💥 LatestCarousel: Fetch error for ${type}:`, error)
        setError(error.message)
        setLoading(false)
      }
    }

    fetchLatestItems()
  }, [type])

  if (loading) {
    return (
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-white flex items-center">
            <span className="w-1 h-8 bg-green-500 mr-4 rounded"></span>
            Latest {type.charAt(0).toUpperCase() + type.slice(1)}
          </h2>
        </div>
        <div className="flex justify-center py-8">
          <div className="text-center">
            <i className="fas fa-spinner fa-spin text-2xl text-green-500 mb-2"></i>
            <p className="text-gray-400">Loading latest {type}...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    console.error(`LatestCarousel error for ${type}:`, error)
    return (
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-white flex items-center">
            <span className="w-1 h-8 bg-green-500 mr-4 rounded"></span>
            Latest {type.charAt(0).toUpperCase() + type.slice(1)}
          </h2>
        </div>
        <div className="flex justify-center py-8">
          <div className="text-center">
            <i className="fas fa-exclamation-triangle text-2xl text-red-500 mb-2"></i>
            <p className="text-red-400">Error loading latest {type}: {error}</p>
          </div>
        </div>
      </div>
    )
  }

  if (!items.length) {
    return (
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-white flex items-center">
            <span className="w-1 h-8 bg-green-500 mr-4 rounded"></span>
            Latest {type.charAt(0).toUpperCase() + type.slice(1)}
          </h2>
        </div>
        <div className="flex justify-center py-8">
          <div className="text-center">
            <i className="fas fa-film text-2xl text-gray-500 mb-2"></i>
            <p className="text-gray-400">No latest {type} available</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="mb-12">
      <Carousel
        title={`Latest ${type.charAt(0).toUpperCase() + type.slice(1)}`}
        items={items}
        type={type}
      />
    </div>
  )
}
