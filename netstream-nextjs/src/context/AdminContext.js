'use client'
import { createContext, useContext, useState, useEffect } from 'react'

const AdminContext = createContext()

export function AdminProvider({ children }) {
  const [isAdmin, setIsAdmin] = useState(false)
  const [adminToken, setAdminToken] = useState(null)
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false)
  const [isAdminPanelOpen, setIsAdminPanelOpen] = useState(false)

  useEffect(() => {
    // Check for existing admin token
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('adminToken')
      if (token) {
        setAdminToken(token)
        setIsAdmin(true)
      }
    }
  }, [])

  const login = (token) => {
    setAdminToken(token)
    setIsAdmin(true)
    if (typeof window !== 'undefined') {
      localStorage.setItem('adminToken', token)
    }
    setIsLoginModalOpen(false)
  }

  const logout = () => {
    setAdminToken(null)
    setIsAdmin(false)
    if (typeof window !== 'undefined') {
      localStorage.removeItem('adminToken')
    }
    setIsAdminPanelOpen(false)
  }

  const showLoginModal = () => {
    setIsLoginModalOpen(true)
  }

  const hideLoginModal = () => {
    setIsLoginModalOpen(false)
  }

  const showAdminPanel = () => {
    setIsAdminPanelOpen(true)
  }

  const hideAdminPanel = () => {
    setIsAdminPanelOpen(false)
  }

  const value = {
    isAdmin,
    user: adminToken ? { token: adminToken } : null,
    isLoginModalOpen,
    isAdminPanelOpen,
    login,
    logout,
    showLoginModal,
    hideLoginModal,
    showAdminPanel,
    hideAdminPanel
  }

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  )
}

export function useAdminContext() {
  const context = useContext(AdminContext)
  if (context === undefined) {
    throw new Error('useAdminContext must be used within an AdminProvider')
  }
  return context
}
