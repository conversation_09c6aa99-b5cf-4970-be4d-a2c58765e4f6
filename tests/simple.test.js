// Simple test to verify the new functionality works
const { build } = require('./helper');

describe('NetStream Fastify Migration - Simple Tests', () => {
  let app;

  beforeAll(async () => {
    app = await build();
  });

  afterAll(async () => {
    if (app) {
      await app.cleanup();
      await app.close();
    }
  });

  test('should start server successfully', async () => {
    expect(app).toBeDefined();
  });

  test('should respond to health check', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/health'
    });

    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload.status).toBe('healthy');
  });

  test('should handle enhanced performance API', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/api/performance'
    });

    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload.success).toBe(true);
    expect(payload.data).toBeDefined();
    expect(payload.data.memory).toBeDefined();
  });

  test('should handle system storage API', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/api/system/storage'
    });

    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload.success).toBe(true);
    expect(payload.data).toBeDefined();
  });

  test('should handle scraping API', async () => {
    const response = await app.inject({
      method: 'POST',
      url: '/api/scrape',
      payload: {
        mode: 'latest',
        type: 'movies',
        pages: 2
      }
    });

    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload.success).toBe(true);
    expect(payload.data.jobId).toBeDefined();
  });

  test('should handle Addic7ed subtitle search', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/api/addic7ed/subtitles?show=Breaking%20Bad&season=1&episode=1'
    });

    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload.success).toBe(true);
    expect(Array.isArray(payload.subtitles)).toBe(true);
  });

  test('should handle cache stats', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/api/cache/stats'
    });

    expect(response.statusCode).toBe(200);
  });

  test('should handle config endpoint', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/config'
    });

    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload.tmdbApiKey).toBeDefined();
  });

  test('should handle GraphQL queries', async () => {
    const response = await app.inject({
      method: 'POST',
      url: '/graphql',
      payload: {
        query: '{ databaseStats { totalItems } }'
      }
    });

    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload.data).toBeDefined();
    expect(payload.data.databaseStats).toBeDefined();
  });

  test('should validate required parameters for subtitle search', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/api/addic7ed/subtitles?show=Breaking%20Bad'
    });

    expect(response.statusCode).toBe(400);
  });

  test('should require admin token for cache clear', async () => {
    const response = await app.inject({
      method: 'POST',
      url: '/api/cache/clear',
      payload: {}
    });

    expect(response.statusCode).toBe(401);
  });
});
