'use client'
import { useEffect, useRef } from 'react'
import { useMediaData } from '@/hooks/useMediaData'
import GridItem from '@/components/ui/GridItem'
import { useIntersection } from 'react-use'

export default function MediaGrid({ type }) {
  const { data, loading, error, loadMore, hasMore } = useMediaData(type)
  const loadMoreRef = useRef(null)
  const intersection = useIntersection(loadMoreRef, {
    root: null,
    rootMargin: '100px',
    threshold: 0.1
  })

  useEffect(() => {
    if (intersection && intersection.isIntersecting && hasMore && !loading) {
      loadMore()
    }
  }, [intersection, hasMore, loading, loadMore])

  if (loading && (!data || data.length === 0)) {
    return (
      <div className="flex justify-center py-8">
        <i className="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        Error loading content: {error.message}
      </div>
    )
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-400">
        No items found
      </div>
    )
  }

  return (
    <div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {data.map((item) => (
          <GridItem
            key={item.id}
            item={item}
            type={type}
          />
        ))}
      </div>

      {/* Load More Trigger */}
      {hasMore && (
        <div ref={loadMoreRef} className="flex justify-center py-8">
          {loading ? (
            <i className="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
          ) : (
            <div className="text-gray-400">Loading more...</div>
          )}
        </div>
      )}
    </div>
  )
}
