const axios = require('axios');
const cheerio = require('cheerio');
const logger = require('../../../utils/logger');
const Anime = require('../../../db/models/Anime');
const Config = require('../../../db/models/Config');
const { FRENCH_ANIME_BASE } = require('../../../config/env');

async function isDuplicateAnime(detailUrl) { // Simplified to check only detailUrl
    if (!detailUrl) return false; // Avoid querying with null
    const existing = await Anime.findOne({ detailUrl });
    return !!existing;
}

async function scrapeFrenchAnimeDetail(detailUrl, season = '1', animeLanguage = 'unknown') {
    try {
        // Get the latest FRENCH_ANIME_BASE from the database
        const frenchAnimeBase = await Config.getValue('FRENCH_ANIME_BASE', FRENCH_ANIME_BASE);

        const url = detailUrl.startsWith('http') ? detailUrl : `https://${frenchAnimeBase}${detailUrl}`;
        logger.info(`Scraping French Anime detail from: ${url}`);
        const { data } = await axios.get(url);
        const $ = cheerio.load(data);

        const metadata = {
            synopsis: $('.mov-desc').last().text().trim() || '',
            actors: $('.mov-list [itemprop="actor"]').text().trim().split(',').map(a => a.trim()) || [],
            year: $('.mov-list li:contains("Date de sortie") .mov-desc').text().trim() || '',
            genre: $('.mov-list [itemprop="genre"]').text().trim() || '',
            origin: '',
            creator: '',
            duration: $('.mov-list li:contains("Durée") .mov-desc').text().trim() || ''
        };

        const episodes = [];
        const streamingUrls = [];

        $('.block-player').each((_, element) => {
            const blockSai = $(element).prevAll('.block-sai').first().text().trim().toLowerCase();
            const episodeLanguage = blockSai.includes('vostfr') ? 'VOSTFR' : blockSai.includes('vf') || blockSai.includes('french') ? 'VF' : 'unknown';
            const episodeText = $(element).prevAll('.block-ep').first().text().trim();
            const episodeNumber = episodeText.match(/Episode\s*(\d+)/i)?.[1] || 'Film';
            const seasonNumber = blockSai.match(/saison\s*(\d+)/i)?.[1] || (season === 'N/A' ? undefined : season);

            const episodeStreamingUrls = [];

            $(element).find('iframe').each((_, iframe) => {
                const src = $(iframe).attr('data-src') || $(iframe).attr('src');
                if (src) {
                    episodeStreamingUrls.push({
                        url: src.trim(),
                        provider: src.match(/vidmoly|magasavor|vidply|luluvdo/)?.[0] || 'unknown',
                        language: episodeLanguage,
                        lastChecked: new Date(),
                        isActive: true
                    });
                }
            });

            if (episodeStreamingUrls.length === 0) {
                episodeStreamingUrls.push({
                    url: 'N/A',
                    provider: 'None',
                    language: episodeLanguage,
                    lastChecked: new Date(),
                    isActive: false
                });
            }

            episodes.push({
                episodeNumber: String(episodeNumber),
                season: seasonNumber,
                language: episodeLanguage,
                streamingUrls: episodeStreamingUrls
            });
        });

        if (!episodes.length) {
            const epsText = $('.eps').text().trim();
            if (epsText) {
                const episodeLines = epsText.split('\n');
                episodeLines.forEach(line => {
                    const [epNum, urls] = line.split('!');
                    if (epNum && urls) {
                        const episodeNumber = String(parseInt(epNum, 10));
                        const urlList = urls.split(',').filter(url => url.trim());
                        const episodeUrls = urlList.map(url => ({
                            url: url.trim(),
                            provider: url.match(/vidmoly|magasavor|vidply|luluvdo/)?.[0] || 'unknown',
                            language: animeLanguage,
                            lastChecked: new Date(),
                            isActive: true
                        }));
                        episodes.push({
                            episodeNumber,
                            season: season === 'N/A' ? undefined : season,
                            language: animeLanguage,
                            streamingUrls: episodeUrls
                        });
                    }
                });
            }
        }

        if (!episodes.length) {
            episodes.push({
                episodeNumber: 'Film',
                season: season,
                language: animeLanguage,
                streamingUrls: [{
                    url: 'N/A',
                    provider: 'None',
                    language: animeLanguage,
                    lastChecked: new Date(),
                    isActive: false
                }]
            });
        }

        logger.info(`Scraped French Anime detail page: ${url} | Episodes: ${episodes.length}, Streams: ${episodes.reduce((sum, ep) => sum + ep.streamingUrls.length, 0)}`);
        return { episodes, metadata, animeLanguage, season };
    } catch (error) {
        logger.error(`French Anime detail scrape error for ${detailUrl}: ${error.message}`);
        return { episodes: [], metadata: {}, animeLanguage, season };
    }
}

module.exports = { scrapeFrenchAnimeDetail, isDuplicateAnime };