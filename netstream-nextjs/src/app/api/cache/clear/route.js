import { NextResponse } from 'next/server';

// In-memory cache storage (in production, you'd use Redis or similar)
let cacheStorage = new Map();
let cacheStats = {
  hits: 0,
  misses: 0,
  sets: 0,
  deletes: 0,
  lastCleared: null
};

// Export cache functions for use in other parts of the application
export function getCache(key) {
  const item = cacheStorage.get(key);
  if (item && item.expires > Date.now()) {
    cacheStats.hits++;
    return item.value;
  }
  if (item) {
    cacheStorage.delete(key);
  }
  cacheStats.misses++;
  return null;
}

export function setCache(key, value, ttl = 3600000) { // 1 hour default
  cacheStorage.set(key, {
    value,
    expires: Date.now() + ttl
  });
  cacheStats.sets++;
}

export function deleteCache(key) {
  const deleted = cacheStorage.delete(key);
  if (deleted) {
    cacheStats.deletes++;
  }
  return deleted;
}

export function getCacheStats() {
  return {
    ...cacheStats,
    size: cacheStorage.size,
    hitRate: cacheStats.hits + cacheStats.misses > 0 
      ? Math.round((cacheStats.hits / (cacheStats.hits + cacheStats.misses)) * 100)
      : 0
  };
}

export async function POST() {
  try {
    // Clear all cache entries
    const cacheSize = cacheStorage.size;
    cacheStorage.clear();
    
    // Update cache statistics
    cacheStats.lastCleared = new Date().toISOString();
    cacheStats.sets = 0;
    cacheStats.hits = 0;
    cacheStats.misses = 0;
    cacheStats.deletes = 0;

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    const clearData = {
      status: 'cleared',
      message: 'Cache cleared successfully',
      clearedEntries: cacheSize,
      timestamp: new Date().toISOString()
    };

    console.log(`Cache cleared: ${cacheSize} entries removed`);

    return NextResponse.json({
      success: true,
      message: 'Cache cleared successfully',
      data: clearData
    });

  } catch (error) {
    console.error('Cache clear error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to clear cache: ${error.message}`
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const stats = getCacheStats();
    
    return NextResponse.json({
      success: true,
      data: {
        ...stats,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Cache stats error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to get cache stats: ${error.message}`
    }, { status: 500 });
  }
} 