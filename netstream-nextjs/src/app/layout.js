import './globals.css'
import { ApolloWrapper } from '@/lib/apollo'
import { Toaster } from 'react-hot-toast'
import { UserProvider } from '@/context/UserContext'
import { PlayerProvider } from '@/context/PlayerContext'
import { AdminProvider } from '@/context/AdminContext'
import TopBar from '@/components/layout/TopBar'
import MobileSearchModal from '@/components/layout/MobileSearchModal'
import VideoPlayer from '@/components/player/VideoPlayer'
import DirectPlayerFix from '@/components/player/DirectPlayerFix'

export const metadata = {
  title: 'NetStream',
  description: 'Stream movies, series, anime, and live TV',
}

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
        <link rel="stylesheet" href="/css/admin-panel.css" />
      </head>
      <body className="bg-gray-900 text-white">
        <ApolloWrapper>
          <UserProvider>
            <PlayerProvider>
              <AdminProvider>
                <div className="min-h-screen flex flex-col">
                  <TopBar />
                  <main className="flex-1 overflow-auto">
                    {children}
                  </main>
                </div>
                <MobileSearchModal />
                <VideoPlayer />
                <DirectPlayerFix />
                <Toaster position="top-right" />
              </AdminProvider>
            </PlayerProvider>
          </UserProvider>
        </ApolloWrapper>
      </body>
    </html>
  )
}
