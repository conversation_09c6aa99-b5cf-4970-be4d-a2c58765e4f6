// NetStream_GraphQL/src/config/constants.js
const SCRAPE_MODE = {
    FULL: 'full',
    LATEST: 'latest'
  };
  
  const PROVIDER_CONFIG = {
    'magasavor.net': { baseUrl: 'https://magasavor.net', headers: { 'Referer': 'https://magasavor.net/', 'User-Agent': 'Mozilla/5.0' }, altDomains: ['magasavor', 'magasavor.nete', 'alejandrocenturyoil.com'] },
    'vidply.com': { baseUrl: 'https://vidply.com', headers: { 'Referer': 'https://vidply.com/' }, altDomains: ['vidply', 'vidply.come'] },
    'vidmoly.to': { baseUrl: 'https://vidmoly.to', headers: { 'Referer': 'https://vidmoly.to/' }, altDomains: ['vidmoly.me', 'vidmoly'] },
    'luluvdo.com': { baseUrl: 'https://luluvdo.com', headers: { 'Referer': 'https://luluvdo.com/' }, altDomains: ['luluvdo'] },
    'tipfly.xyz': { baseUrl: 'https://tipfly.xyz', headers: { 'Referer': 'https://tipfly.xyz/' } },
    'uqload.net': { baseUrl: 'https://uqload.net', headers: { 'Referer': 'https://uqload.net/', 'Origin': 'https://uqload.net', 'X-Requested-With': 'XMLHttpRequest' }, altDomains: ['uqload.to', 'uqloada.ws', 'uqload.'] },
    'oneupload.to': { baseUrl: 'https://oneupload.to', headers: { 'Referer': 'https://oneupload.to/' }, altDomains: ['oneupload.net'] },
    'waaw1.tv': { baseUrl: 'https://waaw1.tv', headers: { 'Referer': 'https://waaw1.tv/' }, altDomains: ['waaw.to', 'waaw.tv', 'waaw1.tve'] },
    'filegram.to': { baseUrl: 'https://filegram.to', headers: { 'Referer': 'https://filegram.to/' } },
    'dooodster.com': { baseUrl: 'https://dooodster.com', headers: { 'Referer': 'https://dooodster.com/' }, altDomains: ['dooood.com', 'dood.re', 'dood.wf', 'dood.pro', 'dood.sh'] },
    'voe.sx': { baseUrl: 'https://voe.sx', headers: { 'Referer': 'https://voe.sx/' } },
    'cybervynx.com': { baseUrl: 'https://cybervynx.com', headers: { 'Referer': 'https://cybervynx.com/' } },
    'sbface.com': { baseUrl: 'https://sbface.com', headers: { 'Referer': 'https://sbface.com/' }, altDomains: ['sbanh.com', 'sbchill.com', 'sbrity.com', 'sbbrisk.com', 'sblanh.com', 'sbhight.com', 'sbspeed.com'] },
    'lvturbo.com': { baseUrl: 'https://lvturbo.com', headers: { 'Referer': 'https://lvturbo.com/' } },
    'streamsilk.com': { baseUrl: 'https://streamsilk.com', headers: { 'Referer': 'https://streamsilk.com/' } },
    'd0000d.com': { baseUrl: 'https://d0000d.com', headers: { 'Referer': 'https://d0000d.com/' }, altDomains: ['d000d.com', 'd0o0d.com'] },
    'streamdav.com': { baseUrl: 'https://streamdav.com', headers: { 'Referer': 'https://streamdav.com/' } },
    'streamvid.net': { baseUrl: 'https://streamvid.net', headers: { 'Referer': 'https://streamvid.net/' } },
    'mixdrop.ps': { baseUrl: 'https://mixdrop.ps', headers: { 'Referer': 'https://mixdrop.ps/' }, altDomains: ['mixdrop.co'] },
    'vido.lol': { baseUrl: 'https://vido.lol', headers: { 'Referer': 'https://vido.lol/' }, altDomains: ['vido.lo', 'vido.lole'] },
    'upstream.to': { baseUrl: 'https://upstream.to', headers: { 'Referer': 'https://upstream.to/' }, altDomains: ['upstream.co'] },
    'upvideo.to': { baseUrl: 'https://upvideo.to', headers: { 'Referer': 'https://upvideo.to/' } },
    'ssblongvu.com': { baseUrl: 'https://ssblongvu.com', headers: { 'Referer': 'https://ssblongvu.com/' } },
    'streamhide.to': { baseUrl: 'https://streamhide.to', headers: { 'Referer': 'https://streamhide.to/' } },
    'louishide.com': { baseUrl: 'https://louishide.com', headers: { 'Referer': 'https://louishide.com/' } },
    'vudeo.ws': { baseUrl: 'https://vudeo.ws', headers: { 'Referer': 'https://vudeo.ws/' }, altDomains: ['vudeo.nlt'] },
    'guccihide.com': { baseUrl: 'https://guccihide.com', headers: { 'Referer': 'https://guccihide.com/' } },
    'evoload.io': { baseUrl: 'https://evoload.io', headers: { 'Referer': 'https://evoload.io/' }, altDomains: ['evoload.ioev', 'evoload.net'] },
    'mvidoo.com': { baseUrl: 'https://mvidoo.com', headers: { 'Referer': 'https://mvidoo.com/' } },
    'ds2play.com': { baseUrl: 'https://ds2play.com', headers: { 'Referer': 'https://ds2play.com/' } },
    'vidhidevip.com': { baseUrl: 'https://vidhidevip.com', headers: { 'Referer': 'https://vidhidevip.com/' } },
    'streamtape.com': { baseUrl: 'https://streamtape.com', headers: { 'Referer': 'https://streamtape.com/' } },
    'streamhub.gg': { baseUrl: 'https://streamhub.gg', headers: { 'Referer': 'https://streamhub.gg/' }, altDomains: ['streamhub.top'] },
    'filemoon.sx': { baseUrl: 'https://filemoon.sx', headers: { 'Referer': 'https://filemoon.sx/' } },
    'sdefx.cloud': { baseUrl: 'https://sdefx.cloud', headers: { 'Referer': 'https://sdefx.cloud/' } },
    'wiflix.online': { baseUrl: 'https://wiflix.online', headers: { 'Referer': 'https://wiflix.online/' } },
    'vidfast.co': { baseUrl: 'https://vidfast.co', headers: { 'Referer': 'https://vidfast.co/' }, altDomains: ['go.vidfast.co'] },
    'abcvideo.cc': { baseUrl: 'https://abcvideo.cc', headers: { 'Referer': 'https://abcvideo.cc/' } },
    'aparat.cam': { baseUrl: 'https://aparat.cam', headers: { 'Referer': 'https://aparat.cam/' } },
    'players.wiflix-pro.mom': { baseUrl: 'https://players.wiflix-pro.mom', headers: { 'Referer': 'https://players.wiflix-pro.mom/' } },
    'upvid.co': { baseUrl: 'https://upvid.co', headers: { 'Referer': 'https://upvid.co/' } },
    'hlsplay.com': { baseUrl: 'https://hlsplay.com', headers: { 'Referer': 'https://hlsplay.com/' } },
    'tazvids.to': { baseUrl: 'https://tazvids.to', headers: { 'Referer': 'https://tazvids.to/' } },
    'video.sibnet.ru': { baseUrl: 'https://video.sibnet.ru', headers: { 'Referer': 'https://video.sibnet.ru/' } },
    'www.myvi.xyz': { baseUrl: 'https://www.myvi.xyz', headers: { 'Referer': 'https://www.myvi.xyz/' } },
    'userload.co': { baseUrl: 'https://userload.co', headers: { 'Referer': 'https://userload.co/' } },
    'embed.mystream.to': { baseUrl: 'https://embed.mystream.to', headers: { 'Referer': 'https://embed.mystream.to/' } },
    'www.fembed.com': { baseUrl: 'https://www.fembed.com', headers: { 'Referer': 'https://www.fembed.com/' } },
    'hirudinoid-prisoner.hostingerapp.com': { baseUrl: 'https://hirudinoid-prisoner.hostingerapp.com', headers: { 'Referer': 'https://hirudinoid-prisoner.hostingerapp.com/' } },
    'verystream.com': { baseUrl: 'https://verystream.com', headers: { 'Referer': 'https://verystream.com/' } },
    'onlystream.tv': { baseUrl: 'https://onlystream.tv', headers: { 'Referer': 'https://onlystream.tv/' } },
    'playnow.to': { baseUrl: 'https://playnow.to', headers: { 'Referer': 'https://playnow.to/' } },
    'jetload.net': { baseUrl: 'https://jetload.net', headers: { 'Referer': 'https://jetload.net/' } },
    'prostream.to': { baseUrl: 'https://prostream.to', headers: { 'Referer': 'https://prostream.to/' } },
  };
  
  module.exports = {
    WIFLIX_BASE: 'flemmix.net',
    WITV_BASE: 'witv.skin',
    FRENCH_ANIME_BASE: 'french-anime.com',
    PAGES_TO_SCRAPE_WIFLIX_MOVIES: 651, // Max for movies
    PAGES_TO_SCRAPE_WIFLIX_SERIES: 161, // Max for series
    SCRAPE_INTERVAL: 6 * 60 * 60 * 1000, // 6 hours
    SCRAPING_ORDER: ['wiflix_series', 'anime', 'wiflix_movies', 'witv'],
    SCRAPE_MODE,
    PROVIDER_CONFIG
  };