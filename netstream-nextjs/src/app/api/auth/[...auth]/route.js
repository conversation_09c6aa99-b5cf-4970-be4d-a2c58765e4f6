// This file will be moved to netstream-nextjs/src/app/api/auth/[...auth]/route.js
// No code change, just a move. 

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { ObjectId } from 'mongodb';

const JWT_SECRET = process.env.JWT_SECRET;

export async function POST(request) {
  let action, email, password, name, profileImage, currentPassword, newPassword, icon;
  const contentType = request.headers.get('content-type') || '';
  const db = await connectToDatabase();
  const users = db.collection('users');
  let jsonData = null;

  if (contentType.includes('multipart/form-data')) {
    const formData = await request.formData();
    action = formData.get('action');
    profileImage = formData.get('profileImage');
  } else {
    jsonData = await request.json();
    action = jsonData.action;
    email = jsonData.email;
    password = jsonData.password;
    name = jsonData.name;
    currentPassword = jsonData.currentPassword;
    newPassword = jsonData.newPassword;
    icon = jsonData.icon;
  }

  if (action === 'register') {
    if (!email || !password || !name) {
      return NextResponse.json({ error: 'Missing fields' }, { status: 400 });
    }
    const existing = await users.findOne({ email });
    if (existing) {
      return NextResponse.json({ error: 'User already exists' }, { status: 400 });
    }
    const hash = await bcrypt.hash(password, 10);
    const user = { email, password: hash, name, role: 'user', createdAt: new Date() };
    await users.insertOne(user);
    return NextResponse.json({ success: true });
  }

  if (action === 'login') {
    const user = await users.findOne({ email });
    if (!user) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }
    const valid = await bcrypt.compare(password, user.password);
    if (!valid) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }
    const token = jwt.sign({ id: user._id, email: user.email, role: user.role }, JWT_SECRET, { expiresIn: '7d' });
    const response = NextResponse.json({ success: true, token, user: { email: user.email, name: user.name, role: user.role } });
    response.cookies.set('token', token, {
      httpOnly: true,
      path: '/',
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production' ? true : false
    });
    return response;
  }

  if (action === 'me') {
    // Use next/headers cookies() for Edge runtime (async)
    const cookiesStore = await cookies();
    const token = cookiesStore.get('token')?.value;
    if (!token) return NextResponse.json({ user: null });
    try {
      const payload = jwt.verify(token, JWT_SECRET);
      const user = await users.findOne({ _id: new ObjectId(payload.id) });
      if (!user) return NextResponse.json({ user: null });
      return NextResponse.json({ user: { email: user.email, name: user.name, role: user.role, profileIcon: user.profileIcon || '' } });
    } catch {
      return NextResponse.json({ user: null });
    }
  }

  if (action === 'updateProfileIcon') {
    const cookiesStore = await cookies();
    const token = cookiesStore.get('token')?.value;
    if (!token) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    try {
      const payload = jwt.verify(token, JWT_SECRET);
      const iconValue = icon || '';
      if (!iconValue) {
        return NextResponse.json({ error: 'No icon provided' }, { status: 400 });
      }
      const updatedUser = await users.findOneAndUpdate(
        { _id: new ObjectId(payload.id) },
        { $set: { profileIcon: iconValue } },
        { returnDocument: 'after' }
      );
      return NextResponse.json({ success: true, user: { email: updatedUser.email, name: updatedUser.name, role: updatedUser.role, profileIcon: iconValue } });
    } catch (error) {
      console.error('Error updating profile icon:', error);
      return NextResponse.json({ error: 'Failed to update profile icon: ' + error.message }, { status: 500 });
    }
  }

  if (action === 'updateEmail') {
    const cookiesStore = await cookies();
    const token = cookiesStore.get('token')?.value;
    if (!token) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    try {
      const payload = jwt.verify(token, JWT_SECRET);
      const existing = await users.findOne({ email });
      if (existing && existing._id.toString() !== payload.id) {
        return NextResponse.json({ error: 'Email already in use' }, { status: 400 });
      }
      const updatedUser = await users.findOneAndUpdate(
        { _id: new ObjectId(payload.id) },
        { $set: { email } },
        { returnDocument: 'after' }
      );
      return NextResponse.json({ success: true, user: { email: updatedUser.email, name: updatedUser.name, role: updatedUser.role } });
    } catch (error) {
      console.error('Error updating email:', error);
      return NextResponse.json({ error: 'Failed to update email' }, { status: 500 });
    }
  }

  if (action === 'updatePassword') {
    const cookiesStore = await cookies();
    const token = cookiesStore.get('token')?.value;
    if (!token) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    try {
      const payload = jwt.verify(token, JWT_SECRET);
      const user = await users.findOne({ _id: new ObjectId(payload.id) });
      if (!user) return NextResponse.json({ error: 'User not found' }, { status: 404 });
      const valid = await bcrypt.compare(currentPassword, user.password);
      if (!valid) return NextResponse.json({ error: 'Current password is incorrect' }, { status: 400 });
      const hash = await bcrypt.hash(newPassword, 10);
      await users.updateOne({ _id: new ObjectId(payload.id) }, { $set: { password: hash } });
      return NextResponse.json({ success: true });
    } catch (error) {
      console.error('Error updating password:', error);
      return NextResponse.json({ error: 'Failed to update password' }, { status: 500 });
    }
  }

  return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
}
