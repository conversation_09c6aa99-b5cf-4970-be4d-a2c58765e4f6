/**
 * Keep-alive utility for Render.com free tier
 * This module sets up a self-ping mechanism to prevent the service from spinning down
 * after 15 minutes of inactivity on Render.com's free tier
 */

const axios = require('axios');
const logger = require('./logger');

// Default interval is 14 minutes (840000ms) to stay under the 15-minute inactivity limit
const DEFAULT_PING_INTERVAL = 14 * 60 * 1000;

/**
 * Set up a self-ping mechanism to keep the service alive
 * @param {string} url - The URL to ping (should be the service's own URL)
 * @param {number} interval - The interval between pings in milliseconds
 * @returns {Object} - An object with start and stop methods
 */
function setupKeepAlive(url, interval = DEFAULT_PING_INTERVAL) {
  let pingInterval = null;
  let isActive = false;
  
  /**
   * Ping the service to keep it alive
   */
  async function pingService() {
    try {
      const response = await axios.get(url);
      logger.info(`Keep-alive ping sent to ${url} - Status: ${response.status}`);
    } catch (error) {
      logger.error(`Keep-alive ping failed: ${error.message}`);
    }
  }
  
  return {
    /**
     * Start the keep-alive mechanism
     */
    start: () => {
      if (isActive) {
        logger.info('Keep-alive mechanism is already active');
        return;
      }
      
      logger.info(`Starting keep-alive mechanism - Pinging ${url} every ${interval / 1000} seconds`);
      pingInterval = setInterval(pingService, interval);
      isActive = true;
      
      // Send an initial ping
      pingService();
    },
    
    /**
     * Stop the keep-alive mechanism
     */
    stop: () => {
      if (!isActive) {
        logger.info('Keep-alive mechanism is not active');
        return;
      }
      
      logger.info('Stopping keep-alive mechanism');
      clearInterval(pingInterval);
      isActive = false;
    },
    
    /**
     * Check if the keep-alive mechanism is active
     * @returns {boolean} - True if active, false otherwise
     */
    isActive: () => isActive
  };
}

module.exports = setupKeepAlive;
