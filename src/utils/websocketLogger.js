// src/utils/websocketLogger.js
const WebSocket = require('ws');
const logger = require('./logger');

let wss = null;
const logSubscriptions = new Map();

/**
 * Initialize WebSocket server for log streaming
 * @param {Object} server - HTTP server instance
 */
function initWebSocketServer(server) {
  if (wss) return;

  wss = new WebSocket.Server({ server });

  wss.on('connection', (ws) => {
    logger.info('WebSocket client connected');

    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);

        if (data.type === 'subscribe' && data.logId) {
          // Subscribe to a specific log stream
          if (!logSubscriptions.has(data.logId)) {
            logSubscriptions.set(data.logId, new Set());
          }
          logSubscriptions.get(data.logId).add(ws);
          ws.logId = data.logId;

          // Send confirmation
          ws.send(JSON.stringify({
            type: 'subscribed',
            logId: data.logId,
            message: `Subscribed to log stream: ${data.logId}`
          }));

          logger.info(`Client subscribed to log stream: ${data.logId}`);
        }

        if (data.type === 'unsubscribe' && data.logId) {
          // Unsubscribe from a specific log stream
          if (logSubscriptions.has(data.logId)) {
            logSubscriptions.get(data.logId).delete(ws);
            if (logSubscriptions.get(data.logId).size === 0) {
              logSubscriptions.delete(data.logId);
            }
          }
          delete ws.logId;

          // Send confirmation
          ws.send(JSON.stringify({
            type: 'unsubscribed',
            logId: data.logId,
            message: `Unsubscribed from log stream: ${data.logId}`
          }));

          logger.info(`Client unsubscribed from log stream: ${data.logId}`);
        }
      } catch (error) {
        logger.error(`WebSocket message error: ${error.message}`);
      }
    });

    ws.on('close', () => {
      logger.info('WebSocket client disconnected');

      // Clean up subscriptions
      if (ws.logId && logSubscriptions.has(ws.logId)) {
        logSubscriptions.get(ws.logId).delete(ws);
        if (logSubscriptions.get(ws.logId).size === 0) {
          logSubscriptions.delete(ws.logId);
        }
      }
    });

    // Send initial connection message
    ws.send(JSON.stringify({
      type: 'connected',
      message: 'Connected to NetStream WebSocket server'
    }));
  });

  logger.info('WebSocket server initialized');
}

/**
 * Send a log message to all clients subscribed to a specific log ID
 * @param {string} logId - The log ID to send to
 * @param {string} level - Log level (info, error, warn, success)
 * @param {string} message - Log message
 * @param {Object} meta - Additional metadata
 */
function sendLogToSubscribers(logId, level, message, meta = {}) {
  if (!logId || !logSubscriptions.has(logId)) return;

  const subscribers = logSubscriptions.get(logId);
  if (subscribers.size === 0) return;

  // Format message for better readability
  let formattedMessage = message;

  // For problematic URLs, add a prefix to make it clear in the logs
  if (message.includes('problematic URL') ||
      (meta && meta.isProblematicUrl) ||
      message.includes('le-renard-rouge')) {
    formattedMessage = `[SPECIAL] ${message}`;

    // Add special flag to metadata
    meta.special = true;
  }

  // Truncate very long messages
  if (formattedMessage.length > 500) {
    formattedMessage = formattedMessage.substring(0, 500) + '...';
  }

  const logData = {
    type: 'log',
    logId,
    level,
    message: formattedMessage,
    timestamp: new Date().toISOString(),
    meta
  };

  const logJson = JSON.stringify(logData);

  for (const client of subscribers) {
    if (client.readyState === WebSocket.OPEN) {
      client.send(logJson);
    }
  }
}

/**
 * Create a logger that sends logs to WebSocket clients
 * @param {string} logId - The log ID to associate with this logger
 * @returns {Object} - Logger object
 */
function createWebSocketLogger(logId) {
  return {
    info: (message, meta = {}) => {
      logger.info(`[${logId}] ${message}`, meta);
      sendLogToSubscribers(logId, 'info', message, meta);
    },
    error: (message, meta = {}) => {
      logger.error(`[${logId}] ${message}`, meta);
      sendLogToSubscribers(logId, 'error', message, meta);
    },
    warn: (message, meta = {}) => {
      logger.warn(`[${logId}] ${message}`, meta);
      sendLogToSubscribers(logId, 'warn', message, meta);
    },
    success: (message, meta = {}) => {
      logger.info(`[${logId}] ${message}`, meta); // Log as info in the server logs
      sendLogToSubscribers(logId, 'success', message, meta); // But send as success to WebSocket
    },
    debug: (message, meta = {}) => {
      logger.info(`[${logId}] [DEBUG] ${message}`, meta); // Log as info in the server logs
      sendLogToSubscribers(logId, 'debug', message, meta); // But send as debug to WebSocket
    }
  };
}

module.exports = {
  initWebSocketServer,
  createWebSocketLogger,
  sendLogToSubscribers
};
