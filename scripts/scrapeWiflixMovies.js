// scripts/scrapeWiflixMovies.js - CORRECTED STREAM FORMATTING
const logger = require('../src/utils/logger');
const cheerio = require('cheerio');
const { saveToDB } = require('./saveToDB');
const { scrapeWiflixDetail } = require('../src/scrapers/sites/wiflix/detail'); // Uses fixed Puppeteer detail
const { scrapeWiflixList } = require('../src/scrapers/sites/wiflix/list');   // Uses fixed Puppeteer list
const { enrichItem, enrichItemWithOptions } = require('../src/enrichment/services/enrichService');
const { WIFLIX_BASE } = require('../src/config/constants');
const Config = require('../src/db/models/Config');
const Movie = require('../src/db/models/Movie');
const { fetchPageWithPuppeteer } = require('../src/utils/browserUtils');
const pLimit = require('p-limit');

// --- Updated getTotalPages using Puppeteer ---
async function getTotalPages(baseUrl) {
     try {
        logger.info(`[Wiflix Movies] Detecting total pages using Puppeteer for ${baseUrl}`);
        // Use the list scraper's detection
        const listScraper = require('../src/scrapers/sites/wiflix/list');
        return await listScraper.detectTotalWiflixPages(baseUrl);
    } catch (error) {
        logger.error(`[Wiflix Movies] Error detecting total pages via Puppeteer for ${baseUrl}: ${error.message}`);
        return 1; // Default to 1 on error
     }
}

async function isDuplicateMovie(detailUrlPath) {
  if (!detailUrlPath) return false;
  const existing = await Movie.findOne({ detailUrlPath }).select('_id').lean();
  return !!existing;
}

async function timeout(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function scrapePage(page, endpoint, isLatestMode = false) {
  try {
    const movies = await scrapeWiflixList('movie', { [endpoint]: page });
    logger.info(`Collected ${movies.length} movies from ${endpoint}, page ${page} before processing`);

    if(movies.length === 0) {
        logger.warn(`No movies collected from list page ${page} of ${endpoint}. Skipping detail processing for this page.`);
        return [];
    }

    const moviesToSave = [];
    const detailLimit = pLimit(1); // Reduced concurrency for detail scraping

    await Promise.all(movies.map(movie => detailLimit(async () => {
        try {
            let detailUrlPath;
             try {
                 detailUrlPath = new URL(movie.detailUrl).pathname;
             } catch (urlError) {
                 logger.warn(`[Wiflix Movies] Invalid detail URL, cannot create path: ${movie.detailUrl}`);
                 detailUrlPath = movie.detailUrl;
             }

             // Deduplication Check
              if (!isLatestMode && detailUrlPath) {
                 const isDuplicate = await isDuplicateMovie(detailUrlPath);
                 if (isDuplicate) {
                     logger.info(`Skipping duplicate movie: ${movie.title} (path: ${detailUrlPath})`);
                     return;
                 }
             }

            logger.info(`Processing movie: ${movie.title} (${movie.detailUrl})`);
            // Use the updated detail scraper
            const details = await scrapeWiflixDetail(movie.detailUrl);

            // Combine and Enrich
            const combinedItem = { ...movie, ...details }; // details now contains structured streams
            // Use enrichItemWithOptions to ensure seasons are fetched
            const enriched = await enrichItemWithOptions(combinedItem, 'movie', {
                fetchSeasons: true,
                useAdvanced: process.env.USE_ADVANCED_ENRICHMENT === 'true'
            });
            await timeout(500);

             // --- CORRECTED Streaming URL Formatting ---
             const formattedStreamingUrls = (details.streamingUrls || []) // Use details.streamingUrls directly
                .map(stream => {
                     if (!stream || !stream.url) { // Check if stream or stream.url is falsy
                         logger.warn(`[Wiflix Movies] Invalid stream object found for ${movie.title}: ${JSON.stringify(stream)}`);
                         return null;
                     }
                     return {
                        url: stream.url,
                        provider: stream.provider || 'unknown', // Provider from detail scrape
                        language: stream.language || 'unknown', // Language from detail scrape
                        lastChecked: new Date(),
                        isActive: true,
                        sourceStreamUrl: null
                    };
                })
                .filter(Boolean); // Filter out any null entries resulted from invalid streams

            if (formattedStreamingUrls.length === 0) {
              logger.warn(`No valid streaming URLs found/formatted for ${movie.title}`);
              // Continue with saving the movie even if there are no streaming URLs
              logger.info(`Saving movie ${movie.title} with TMDB data but no streaming URLs`);
            }

            const movieData = {
              title: enriched.title || "Untitled",
              detailUrl: enriched.detailUrl,
              detailUrlPath: detailUrlPath,
              cleanedTitle: enriched.cleanedTitle || enriched.title,
              thumbnail: enriched.thumbnail || enriched.image || details.thumbnail || null,
              image: enriched.image || enriched.thumbnail || details.thumbnail || null,
              streamingUrls: formattedStreamingUrls, // Assign the correctly formatted array
              metadata: enriched.metadata || details.metadata || {},
              tmdb: enriched.tmdb || null,
              tmdbSeasons: enriched.tmdbSeasons || null,
              updatedAt: new Date() // Always set updatedAt to current date
            };

            // Always save the movie data, even if there are no streaming URLs
            // This ensures that TMDB data is saved and updatedAt is updated
            moviesToSave.push(movieData);
            logger.info(`Added ${movie.title} to moviesToSave list (${moviesToSave.length} total)`);

        } catch (err) {
            logger.error(`Error processing movie ${movie?.title || 'UNKNOWN URL'} (${movie?.detailUrl}): ${err.message}`);
        }
    })));

    logger.debug(`Page ${page} for ${endpoint} - moviesToSave length: ${moviesToSave.length}`);
    if (moviesToSave.length > 0) {
      logger.info(`Saving ${moviesToSave.length} movies from page ${page} to database`);
      // Fix parameter order: saveToDB expects (items, type) but was called with (type, items)
      await saveToDB(moviesToSave, 'movie');
      logger.info(`Successfully processed/saved ${moviesToSave.length} movies from page ${page}`);
      return moviesToSave;
    }
    logger.warn(`No new/updated movies to save from page ${page} for ${endpoint}`);
    return [];
  } catch (err) {
    logger.error(`Error scraping movies page ${page} for ${endpoint}: ${err.message}`);
    return [];
  }
}

async function scrapeWiflixMovies(pageLimit = 0, isLatestMode = false) {
  const mainStartTime = Date.now();
  try {
    const endpoints = ['film-en-streaming', 'film-ancien'];
    const allMoviesSaved = [];

    // Get the latest WIFLIX_BASE from the database
    const wiflixBase = await Config.getValue('WIFLIX_BASE', WIFLIX_BASE);
    logger.info(`[Wiflix Movies] Using Wiflix base URL: ${wiflixBase}`);

    for (const endpoint of endpoints) {
      const endpointStartTime = Date.now();
      const baseUrl = `https://${wiflixBase}/${endpoint}`;
      // Use the list scraper's pagination detection
      const listScraper = require('../src/scrapers/sites/wiflix/list'); // Re-require inside loop if needed
      const maxPagesDetected = await listScraper.detectTotalWiflixPages(baseUrl);
      const maxPages = pageLimit === -1 ? maxPagesDetected : Math.min(pageLimit || 1, maxPagesDetected);
      const pages = Array.from({ length: maxPages }, (_, i) => i + 1);
      logger.info(`[Wiflix Movies] Scraping endpoint "${endpoint}" - Limit: ${pageLimit}, Detected: ${maxPagesDetected}, Scraping: ${maxPages} pages`);
      const BATCH_SIZE = 1; // Reduced to 1 page per batch
      const batches = [];
      for (let i = 0; i < pages.length; i += BATCH_SIZE) {
        batches.push(pages.slice(i, i + BATCH_SIZE));
      }
      for (let i = 0; i < batches.length; i++) {
          const batch = batches[i];
          logger.info(`[Wiflix Movies] Processing batch ${i+1}/${batches.length} for ${endpoint}: Pages ${batch.join(', ')}`);

          // Process one page at a time instead of using Promise.all
          const batchResults = [];
          for (const page of batch) {
            // We don't need to pass saveToDB as a parameter since it's imported at the top of the file
            // and the function is already defined with the correct parameter order
            const result = await scrapePage(page, endpoint, isLatestMode);
            batchResults.push(result);
            // Add a small delay between pages
            await timeout(3000);
          }

          allMoviesSaved.push(...batchResults.flat());
          if (i < batches.length - 1) {
               logger.info(`[Wiflix Movies] Pausing 15s between batches for endpoint ${endpoint}...`);
               await timeout(15000); // Increased to 15 seconds
          }
      }
       logger.info(`[Wiflix Movies] Finished endpoint ${endpoint} in ${(Date.now() - endpointStartTime) / 1000}s`);
    }
    logger.info(`[Wiflix Movies] Total movies processed/saved: ${allMoviesSaved.length}. Total time: ${(Date.now() - mainStartTime) / 1000}s`);
    return allMoviesSaved;
  } catch (err) {
    logger.error(`[Wiflix Movies] Fatal error in scrapeWiflixMovies: ${err.message}`);
    return [];
  }
  // No finally block here
}

// Direct execution block
if (require.main === module) {
     const mongoose = require('mongoose');
     const env = require('../../src/config/env');
     mongoose.connect(env.mongoUri)
         .then(() => {
             logger.info('Connected to DB for standalone Wiflix Movies scrape');
             const mode = process.env.SCRAPE_MODE === 'latest';
             // We don't need to pass saveToDB as a parameter since it's imported at the top of the file
             scrapeWiflixMovies(env.PAGES_TO_SCRAPE_WIFLIX_MOVIES || 1, mode).finally(async () => {
                  await require('../../src/utils/browserUtils').closeBrowser();
                  await mongoose.disconnect();
                  logger.info('DB disconnected after standalone Wiflix Movies scrape');
             });
         })
         .catch(err => {
             logger.error('DB connection error for standalone Wiflix Movies scrape', err);
             process.exit(1);
         });
 }

module.exports = { scrapeWiflixMovies };