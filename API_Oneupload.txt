API Documentation
Account Info
REQUEST
GET https://oneupload.to/api/account/info?key=8641z58roysnr5hwxoz9
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
RESPONSE
{
    		    "msg": "OK",
    		    "server_time": "2021-09-11 04:30:07",
    		    "status": 200,
    		    "result": {
    		    	"files_total":"31",
    		    	"storage_left":**********,
    		    	"login":"megauploader21",
    		        "email": "<EMAIL>",
    		        "premium_expire":"2022-10-15 04:46:59",
    		        "balance":"108.00000",
    		        "premium":1,
    		        "storage_used":"685101"
    		    }
    		}
Account Stats
REQUEST
GET https://oneupload.to/api/account/stats?key=8641z58roysnr5hwxoz9
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
last	show stats for last X days (default: 7)	14	INT	
RESPONSE
{
    		  "msg": "OK",
    		  "server_time": "2021-09-13 04:30:07",
    		  "status": 200,
    		  "result": [
    		    {
    		      "downloads": "0",
    		      "profit_views": "0.00000",
    		      "views_adb": "1",
    		      "sales": "0",
    		      "profit_sales": "0.00000",
    		      "profit_refs": "0.00000",
    		      "profit_site": "0.00000",
    		      "views": "0",
    		      "refs": "0",
    		      "day": "2021-09-12",
    		      "profit_total": "0.00000",
    		      "views_prem": "0"
    		    }
    		  ]
    		}
Get Upload Server
REQUEST
GET https://oneupload.to/api/upload/server?key=8641z58roysnr5hwxoz9
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
RESPONSE
{
    		  "msg": "OK",
    		  "server_time": "2021-08-11 04:29:54",
    		  "status": 200,
    		  "result": "https://s1.myvideo.com/upload/01"
    		}
Upload File to Server
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
file	Video file(s)	1.avi	FILE	
file_title	Title of video	New release video	STRING	
file_descr	Description of video	This is sample from
our upcoming release	STRING	
snapshot	Custom video snapshot (up to 300KB)	1.jpg	FILE	
fld_id	Folder ID	25	INT	
cat_id	Category ID	5	INT	
tags	Tags list	promo, high quality	STRING	
file_public	Public flag	1	INT	
file_adult	Adult flag	1	INT	
html_redirect	Use old style HTML redirect output instead of JSON	1	INT	
Minimal HTML form upload sample:
<form method="POST" enctype="multipart/form-data" action="https://s1.myvideo.com/upload/01">
    		<input type="hidden" name="key" value="8641z58roysnr5hwxoz9">
    		<input type="hidden" name="html_redirect" value="1">
    		<input type="file" name="file">
    		<input type="submit">
    		</form>
CURL file upload sample: Upload 2 videos:
curl -X POST -F 'key=8641z58roysnr5hwxoz9' -F 'file=@1.avi' -F 'file=@2.avi' https://s1.myvideo.com/upload/01
Upload video with custom title and snapshot:
curl -X POST -F 'key=1l5ftrilhllgwx2bo' -F 'file_title="Hola!"' -F 'file=@1.avi' -F 'snapshot=@1.jpg' http://s1.xvs.tt/upload/01
Response:
{
    		    "msg": "OK",
    		    "status": 200,
    		    "files": [{
    		        "filecode": "u9150wqzvhxj",
    		        "filename": "1.avi",
    		        "status": "OK"
    		    }, {
    		        "filecode": "gzky98gfg6hn",
    		        "filename": "2.avi",
    		        "status": "OK"
    		    }]
    		}
Upload by URL
REQUEST
GET https://oneupload.to/api/upload/url?key=8641z58roysnr5hwxoz9&url={url}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
url	URL to video file	http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4	STRING	
fld_id	Folder ID	25	INT	
cat_id	Category ID	5	INT	
file_public	Public flag	1	INT	
file_adult	Adult flag	1	INT	
tags	Tags list	promo, high quality	STRING	
RESPONSE returns future filecode that will work after upload complete
{
    			"msg":"OK",
    			"server_time":"2021-08-12 20:56:47",
    			"status":200,
    			"result":{
    				"filecode":"fb5asfuj2snh"
    			}
    		}
File Info
REQUEST
GET https://oneupload.to/api/file/info?key=8641z58roysnr5hwxoz9&file_code={file_code}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
file_code	file code, or list separated by comma	gi4o0tlro01u,gi4o0tlro012	STRING	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-12 21:10:07",
    			"status":200,
    			"result":[
    				{
    				"file_views_full":"0",
    				"cat_id":"3",
    				"player_img":"http://img.xvs.tt/fb5asfuj2snh.jpg",
    				"status":200,
    				"file_code":"fb5asfuj2snh",
    				"file_last_download":"2021-08-12 20:56:54",
    				"canplay":1,
    				"file_public":"1",
    				"file_length":"60",
    				"file_title":"big buck bunny",
    				"file_views":"0",
    				"file_created":"2021-08-102 20:51:52",
    				"file_premium_only":"0",
    				"file_adult":"1",
    				"file_fld_id":"25",
    				"tags":"promo, high quality"
    				}
    			]
    		}
File Edit
REQUEST
GET https://oneupload.to/api/file/edit?key=8641z58roysnr5hwxoz9&file_code={file_code}&file_title={file_title}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	<
file_code	File code or comma separated list	gi4o0tlro01u,hqpibyqwxumrt	STRING	
file_title	File title	The 100 S01E02	STRING	
file_descr	File description	release by MegaDrive	STRING	
cat_id	Category ID	5	INT	
file_fld_id	Folder ID	25	INT	
file_public	Public flag	1	INT	
file_adult	Adult flag	0	INT	
tags	File tags	promo, high quality	STRING	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-13 20:17:12",
    			"status":200,
    			"result":"true"
    		}
File List
REQUEST
GET https://oneupload.to/api/file/list?key=8641z58roysnr5hwxoz9
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
fld_id	Folder ID	25	INT	
title	Filter videos by title	Iron man	STRING	
created	Show only videos uploaded after timestamp.
Specify number to show videos uploaded X minutes ago	2021-07-21 05:07:10	STRING	
public	Show public (1) or private (0) videos only, empty to show all	1	INT	
adult	Show adult (1) or safe (0) videos only, empty to show all	0	INT	
per_page	Number of results per page	50	INT	
page	Page number	2	INT	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-13 20:35:18",
    			"status":200,
    			"result":
    			{
    				"files":[
    					{
    						"thumbnail":"http://img.xvs.tt/fb5asfuj2snh_t.jpg",
    						"link":"http://xvs.tt/fb5asfuj2snh.html",
    						"file_code":"fb5asfuj2snh",
    						"canplay":1,
    						"length":"60",
    						"views":"0",
    						"uploaded":"2021-07-12 20:56:54",
    						"public":"0",
    						"fld_id":"0",
    						"title":"Test 123"
    					}
    				],
    				"results_total":9,
    				"pages":9,
    				"results":1
    			}
    		}
File Direct Link
REQUEST
GET https://oneupload.to/api/file/direct_link?key=8641z58roysnr5hwxoz9&file_code={file_code}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
file_code	File code	fb5asfuj2snh	STRING	
q	Video quality if available. Possible values: n , h , l, o	n	STRING	
hls	Generate HLS manifest URL (HLS mod required)	1	INT	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-14 05:32:27",
    			"status":200,
    			"result":
    			{
    				"versions":[
    					{"url":"http://s1.xvs.tt/zqsccwdttqsal2eyd2pjrlvodgh7iyejldkogrnzwypvlnajbosxsuchfurq/v.mp4","name":"n","size":"120755726"},
    					{"url":"http://s1.xvs.tt/zqsccwdttqsal2eyd2pjrlvodgh7iyejldkogrnzwapvlnajbostzwskpx4a/v.mp4","name":"h","size":"135481436"},
    					{"url":"http://s1.xvs.tt/zqsccwdttqsal2eyd2pjrlvodgh7iyejldkogrnzwqpvlnajbosrpvmuh5la/v.mp4","name":"l","size":"34744877"},
    					{"url":"http://s1.xvs.tt/zqsccwdttqsal2eyd2pjrlvodgh7iyejldkogrnzw4pvlnajbosyx264xtsq/v.mp4","name":"o","size":"319256576"}
    				],
    				"file_length":"1560",
    				"player_img":"http://img.xvs.tt/abnormamorph.jpg",
    				"hls_direct":"http://s1.xvs.tt/hls/zqsccwdttqsal2eyd2pjrlvodgh7iyejldkogrnzw,qpvlnajbosrpvmuh5la,ypvlnajbosxsuchfurq,apvlnajbostzwskpx4a,.urlset/master.m3u8"
    			}
    		}
File Clone
REQUEST
GET https://oneupload.to/api/file/clone?key=8641z58roysnr5hwxoz9&file_code={file_code}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
file_code	Source file code	abnormamorph	STRING	
file_title	New file title if required	New video title	STRING	
fld_id	Target folder id	5	INT	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-14 19:39:58",
    			"status":200,
    			"result":
    			{
    				"url":"http://xvs.tt/u405p6qz5xpi",
    				"filecode":"u405p6qz5xpi"
    			}
    		}
File Delete
REQUEST
GET https://oneupload.to/api/file/delete?key=8641z58roysnr5hwxoz9&file_code={file_code}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
file_code	File code	abnormamorph	STRING	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-14 19:39:58",
    			"status":200
    		}
Deleted Files
Get last deleted files list

REQUEST
GET https://oneupload.to/api/file/deleted?key=8641z58roysnr5hwxoz9&last={last}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
last	Show files deleted in last X hours	24	INT	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-15 19:04:06",
    			"status":200,
    			"result":[
    				{
    					"file_code":"38j4wvxw164d",
    					"deleted_by":"me",
    					"deleted_ago_sec":"40",
    					"deleted":"2021-08-15 19:03:26",
    					"title":"Video 109779195"
    				}
    			]
    		}
File DMCA
Get files scheduled for DMCA delete

REQUEST
GET https://oneupload.to/api/file/dmca?key=8641z58roysnr5hwxoz9&last={last}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
last	Show files reported last X hours only	24	INT	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-15 19:31:48",
    			"status":200,
    			"result":[
    				{
    					"file_code":"x2q5h0uhfzdu",
    					"del_in_sec":"42097",
    					"del_time":"2021-08-16 07:13:25"
    				}
    			]
    		}
Folder List
REQUEST
GET https://oneupload.to/api/folder/list?key=8641z58roysnr5hwxoz9&fld_id={fld_id}&files=1
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
fld_id	Parent folder id, default=0	25	INT	
files	Show file list in this folder	1	INT	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-15 19:54:22",
    			"status":200,
    			"result":{
    				"folders":[
    					{
    						"name":"Breaking Bad",
    						"fld_id":"16",
    						"code":"4pwb4yvp7v"
    					},
    					{
    						"name":"Travis",
    						"fld_id":"15",
    						"code":"68dth39m76"
    					}
    				],
    				"files":[
    					{
    						"thumbnail":"http://img.xvs.tt/abnormamorph_t.jpg",
    						"link":"http://xvs.tt/abnormamorph.html",
    						"file_code":"abnormamorph",
    						"canplay":1,
    						"length":"1560",
    						"views":"10",
    						"uploaded":"2021-08-20 20:37:22",
    						"public":"0",
    						"fld_id":"0",
    						"title":"Tri pljus dva 2012 SATRip"
    					}
    				]
    			}
    		}
Folder Create
REQUEST
GET https://oneupload.to/api/folder/create?key=8641z58roysnr5hwxoz9&name={name}&parent_id={parent_id}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
name	Folder name	New Videos	STRING	
parent_id	Parent Folder ID	0	INT	
descr	Folder description	new stuff	STRING	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-18 20:32:46",
    			"status":200,
    			"result":
    			{
    				"fld_id":"29"
    			}
    		}
Folder Edit
Update folder details, skipped fields won't be updated

REQUEST
GET https://oneupload.to/api/folder/edit?key=8641z58roysnr5hwxoz9&fld_id={fld_id}&name={name}
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
fld_id	Folder ID	25	INT	
name	Folder name	Movies 2021	STRING	
parent_id	Parent Folder ID	0	INT	
descr	Folder description	old stuff	STRING	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-18 21:21:44",
    			"status":200,
    			"result":"true"
    		}
File Encodings
Get current encoding queues

REQUEST
GET https://oneupload.to/api/file/encodings?key=8641z58roysnr5hwxoz9
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
file_code	Filter by File code	fb5asfuj2snh	STRING	
RESPONSE
{
    			"msg":"OK",
    			"server_time":"2021-08-18 21:44:09",
    			"status":200,
    			"result":[
    				{"link":"http://xvs.tt/fb5asfuj2snh.html","progress":15,"status":"ENCODING","title":"Test video","quality":"h","file_code":"fb5asfuj2snh"},
    				{"link":"http://xvs.tt/fb5asfuj2snh.html","progress":0,"status":"PENDING","title":"Test video","quality":"l","file_code":"fb5asfuj2snh"}
    			]
    		}
File URL Uploads
Get current encoding queues

REQUEST
GET https://oneupload.to/api/file/url_uploads?key=8641z58roysnr5hwxoz9
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
file_code	Filter by File code	fb5asfuj2snh	STRING	
RESPONSE
{
    			"requests_available":2,
    			"msg":"OK",
    			"server_time":"2022-06-14 20:30:20",
    			"status":200,
    			"result":[
    				{"remote_url":"https://xvs.tt/1.mp4","progress":0,"status":"PENDING","file_code":"","fld_id":"0"},
    				{"remote_url":"https://xvs.tt/2.mp4","progress":0,"status":"PENDING","file_code":"xyayxm9ajlys","fld_id":"7"}
    			]
    		}
File URL Uploads Actions
Get current encoding queues

REQUEST
GET https://oneupload.to/api/file/url_actions?key=8641z58roysnr5hwxoz9
PARAMETERS
Name	Description	Example	Format	Required
key	API key	8641z58roysnr5hwxoz9	STRING	
restart_errors	Restart all failed uploads	1	INT	
delete_errors	Delete all failed uploads	1	INT	
delete_all	Delete all current uploads	1	INT	
delete_code	Delete specific uploads by code	xyayxm9ajlys,6bnyg8rnu11e	STRING	
RESPONSE
{
    			"requests_available":2,
    			"msg":"OK",
    			"server_time":"2022-06-14 20:30:20",
    			"status":200
    		}