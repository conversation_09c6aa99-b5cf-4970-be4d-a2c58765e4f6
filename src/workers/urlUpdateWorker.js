const TelegramBot = require('node-telegram-bot-api');
const mongoose = require('mongoose');
const { telegramToken } = require('../config/env');
//const Config = require('../db/models/Config');
const logger = require('../utils/logger');

const bot = new TelegramBot(telegramToken, { polling: true });
const CHANNEL_ID = '@wiflix2023';

async function updateBaseUrl() {
  try {
    await mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });
    logger.info('Connected to MongoDB for URL update');

    bot.on('channel_post', async (msg) => {
      if (msg.chat.username === 'wiflix2023') {
        const text = msg.text || '';
        const urlMatch = text.match(/https?:\/\/(wiflix[-a-z\.]+)\//i);
        if (urlMatch) {
          const newBase = urlMatch[1];
          const current = require('../config/constants').WIFLIX_BASE;
          if (newBase !== current) {
            await Config.findOneAndUpdate(
              { key: 'WIFLIX_BASE' },
              { value: newBase, lastUpdated: new Date() },
              { upsert: true }
            );
            require('../config/constants').WIFLIX_BASE = newBase;
            logger.info(`Updated Wiflix base URL to ${newBase}`);
          }
        }
      }
    });
  } catch (error) {
    logger.error(`URL update error: ${error.message}`);
  }
}

updateBaseUrl();

module.exports = { updateBaseUrl };