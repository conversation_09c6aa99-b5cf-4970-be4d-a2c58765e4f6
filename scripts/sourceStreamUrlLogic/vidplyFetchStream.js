const axios = require('axios');
const fs = require('fs');

/**
 * Fetches direct video URL from Vidply by extracting a /pass_md5/ URL from the embed page,
 * fetching that URL, and appending a random token if needed.
 */
async function fetchVidplyStream(inputUrl, config, cookies, debug = false) { // Added debug parameter
    const md5Match = cookies.eResponse.data.match(/['"]?\/pass_md5\/([^'"]+)['"]?/);
    if (!md5Match) throw new Error('No /pass_md5/ URL found');
    const md5Url = `${config.baseUrl}${md5Match[0].replace(/['"]/g, '')}`;
    console.log(`[Vidply] Found /pass_md5/ URL: ${md5Url}`);

    const md5Response = await axios.get(md5Url, {
        headers: { 'User-Agent': 'Mozilla/5.0', 'Referer': inputUrl, 'Cookie': cookies.cookieHeader },
        timeout: 10000
    });
    let directUrl = md5Response.data.trim();
    if (!directUrl.includes('?token')) {
        const token = Math.random().toString(36).substring(2);
        directUrl += `?token=${token}`;
    }
    console.log(`[Vidply] Extracted direct source URL: ${directUrl}`);
    return directUrl;
}

module.exports = fetchVidplyStream;