import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/mongodb';
import fs from 'fs';
import path from 'path';

export async function POST() {
  try {
    const db = await connectToDatabase();
    
    // Create backup directory if it doesn't exist
    const backupDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Generate backup filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFilename = `netstream-backup-${timestamp}.json`;
    const backupPath = path.join(backupDir, backupFilename);

    // Get all collections
    const collections = ['movies', 'series', 'animes', 'livetv', 'config'];
    const backupData = {
      metadata: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        collections: collections.length
      },
      collections: {}
    };

    // Backup each collection
    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);
        const documents = await collection.find({}).toArray();
        
        backupData.collections[collectionName] = {
          count: documents.length,
          documents: documents
        };
        
        console.log(`Backed up ${documents.length} documents from ${collectionName}`);
      } catch (error) {
        console.error(`Error backing up collection ${collectionName}:`, error.message);
        backupData.collections[collectionName] = {
          count: 0,
          documents: [],
          error: error.message
        };
      }
    }

    // Write backup to file
    fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));

    // Get backup file stats
    const stats = fs.statSync(backupPath);
    const fileSize = Math.round(stats.size / 1024 / 1024 * 100) / 100; // MB

    const backupInfo = {
      filename: backupFilename,
      path: backupPath,
      size: `${fileSize} MB`,
      collections: Object.keys(backupData.collections).length,
      totalDocuments: Object.values(backupData.collections).reduce((sum, coll) => sum + coll.count, 0),
      timestamp: new Date().toISOString(),
      status: 'completed'
    };

    console.log(`Backup completed: ${backupFilename} (${fileSize} MB)`);

    return NextResponse.json({
      success: true,
      message: 'Database backup completed successfully',
      data: backupInfo
    });

  } catch (error) {
    console.error('System backup error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to create backup: ${error.message}`
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const backupDir = path.join(process.cwd(), 'backups');
    
    if (!fs.existsSync(backupDir)) {
      return NextResponse.json({
        success: true,
        data: {
          backups: [],
          totalBackups: 0,
          totalSize: '0 MB'
        }
      });
    }

    // Get list of backup files
    const backupFiles = fs.readdirSync(backupDir)
      .filter(file => file.endsWith('.json'))
      .map(file => {
        const filePath = path.join(backupDir, file);
        const stats = fs.statSync(filePath);
        return {
          filename: file,
          size: `${Math.round(stats.size / 1024 / 1024 * 100) / 100} MB`,
          created: stats.birthtime.toISOString(),
          modified: stats.mtime.toISOString()
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));

    const totalSize = backupFiles.reduce((sum, file) => {
      const sizeMB = parseFloat(file.size.replace(' MB', ''));
      return sum + sizeMB;
    }, 0);

    return NextResponse.json({
      success: true,
      data: {
        backups: backupFiles,
        totalBackups: backupFiles.length,
        totalSize: `${Math.round(totalSize * 100) / 100} MB`
      }
    });

  } catch (error) {
    console.error('Backup list error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to list backups: ${error.message}`
    }, { status: 500 });
  }
} 