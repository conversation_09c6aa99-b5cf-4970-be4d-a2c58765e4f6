
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for NetStream_graphql/src/scrapers/sites/wiflix/detail.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">NetStream_graphql/src/scrapers/sites/wiflix</a> detail.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/313</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/259</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/21</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/295</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// src/scrapers/sites/wiflix/detail.js - FINAL: Added Path Extraction
const cheerio = <span class="cstat-no" title="statement not covered" >require('cheerio');</span>
const logger = <span class="cstat-no" title="statement not covered" >require('../../../utils/logger');</span>
const { WIFLIX_BASE } = <span class="cstat-no" title="statement not covered" >require('../../../config/constants');</span>
const { fetchPageWithPuppeteer } = <span class="cstat-no" title="statement not covered" >require('../../../utils/browserUtils');</span>
const fs = <span class="cstat-no" title="statement not covered" >require('fs');</span>
const path = <span class="cstat-no" title="statement not covered" >require('path');</span>
const Config = <span class="cstat-no" title="statement not covered" >require('../../../db/models/Config');</span>
&nbsp;
// Will be set dynamically in the scrapeWiflixDetail function
let WIFLIX_BASE_URL;
// Debug HTML generation removed
&nbsp;
// Helper function to safely extract path from URL
function <span class="fstat-no" title="function not covered" >extractPathFromUrl(</span>fullUrl, logContext) {
<span class="cstat-no" title="statement not covered" >    if (!fullUrl || typeof fullUrl !== 'string' || !fullUrl.startsWith('http')) {</span>
         // Handle potential relative URLs passed from thumbnail logic specifically
<span class="cstat-no" title="statement not covered" >         if (fullUrl &amp;&amp; typeof fullUrl === 'string' &amp;&amp; fullUrl.startsWith('/')) {</span>
<span class="cstat-no" title="statement not covered" >             return fullUrl; </span>// Assume it's already a valid path
         }
<span class="cstat-no" title="statement not covered" >        return '';</span>
    }
<span class="cstat-no" title="statement not covered" >    try {</span>
        const urlObject = <span class="cstat-no" title="statement not covered" >new URL(fullUrl);</span>
<span class="cstat-no" title="statement not covered" >        return urlObject.pathname + urlObject.search + urlObject.hash;</span>
    } catch (e) {
<span class="cstat-no" title="statement not covered" >        logger.warn(`[${logContext}] Could not parse path from URL "${fullUrl}": ${e.message}`);</span>
<span class="cstat-no" title="statement not covered" >        return ''; </span>// Return empty string on error
    }
}
&nbsp;
function <span class="fstat-no" title="function not covered" >containsRealContent(</span>html) {
<span class="cstat-no" title="statement not covered" >    if (!html || typeof html !== 'string') <span class="cstat-no" title="statement not covered" >return false;</span></span>
    const hasMetadata = <span class="cstat-no" title="statement not covered" >html.includes('&lt;ul class="mov-list"&gt;');</span>
    // Check for either the movie link structure OR the series episode structure
    const hasPlayerHost = <span class="cstat-no" title="statement not covered" >html.includes('&lt;div class="tabsbox filmlinks"&gt;') || html.includes('&lt;div class="epblocks"&gt;');</span>
<span class="cstat-no" title="statement not covered" >    if (!hasMetadata) <span class="cstat-no" title="statement not covered" >logger.warn('[Wiflix Detail Final] Detail HTML missing metadata block (&lt;ul class="mov-list"&gt;)');</span></span>
<span class="cstat-no" title="statement not covered" >    if (!hasPlayerHost) <span class="cstat-no" title="statement not covered" >logger.warn('[Wiflix Detail Final] Detail HTML missing player/host/episode block.');</span></span>
<span class="cstat-no" title="statement not covered" >    return hasMetadata || hasPlayerHost; </span>// Require at least one key section
}
&nbsp;
// Helper Function: Extracts provider from URL (as fallback or primary if needed)
function <span class="fstat-no" title="function not covered" >getProviderFromUrl(</span>urlString) {
<span class="cstat-no" title="statement not covered" >    if (typeof urlString !== 'string' || !urlString.startsWith('http')) <span class="cstat-no" title="statement not covered" >return 'unknown';</span></span>
<span class="cstat-no" title="statement not covered" >    try {</span>
        const hostname = <span class="cstat-no" title="statement not covered" >new URL(urlString).hostname.toLowerCase().replace(/^www\./, '');</span>
        const providerMap = <span class="cstat-no" title="statement not covered" >{</span>
            'do7go.com': 'Do7go',
            'magasavor.net': 'Magasavor',
            'alejandrocenturyoil.com': 'Magasavor',
            'tipfly.xyz': 'Tipfly',
            'vidply.com': 'Vidply',
            'uqload.net': 'Uqload', 'uqload.to': 'Uqload', 'uqloada.ws': 'Uqload',
            'voe.sx': 'Voe',
            'streamtape.com': 'Streamtape',
            'vidmoly.to': 'Vidmoly', 'vidmoly.me': 'Vidmoly',
            'dood.re': 'Doodstream', 'dood.wf': 'Doodstream', 'dood.pro': 'Doodstream', 'dood.sh': 'Doodstream', 'dooood.com': 'Doodstream', 'dooodster.com': 'Doodstream', 'd0000d.com': 'Doodstream', 'd000d.com': 'Doodstream', 'd0o0d.com': 'Doodstream',
            'mixdrop.co': 'Mixdrop', 'mixdrop.ps': 'Mixdrop',
            'vudeo.ws': 'Vudeo', 'vudeo.nlt': 'Vudeo',
            'upstream.to': 'Upstream', 'upstream.co': 'Upstream',
            'sbface.com': 'StreamSB', 'sbanh.com': 'StreamSB', 'sbchill.com': 'StreamSB', 'sbrity.com': 'StreamSB', 'sbbrisk.com': 'StreamSB', 'sblanh.com': 'StreamSB', 'sbhight.com': 'StreamSB', 'sbspeed.com': 'StreamSB', 'ssblongvu.com': 'StreamSB',
            'filemoon.sx': 'Filemoon',
            'luluvdo.com': 'Luluvdo', // Added from HTML
            'waaw1.tv': 'Waaw', // Base name
            'waaw.tv': 'Waaw',
            'waaw.to': 'Waaw',
            'smoothpre.com': 'Filelions', // Added from HTML
            'cybervynx.com': 'Swish',     // Added from HTML
             // Add more mappings as discovered
        };
<span class="cstat-no" title="statement not covered" >        if (providerMap[hostname]) <span class="cstat-no" title="statement not covered" >return providerMap[hostname];</span></span>
<span class="cstat-no" title="statement not covered" >        for (const domainKey in providerMap) {</span>
<span class="cstat-no" title="statement not covered" >            if (hostname.includes(domainKey.split('.')[0])) <span class="cstat-no" title="statement not covered" >return providerMap[domainKey];</span></span>
        }
        const domainParts = <span class="cstat-no" title="statement not covered" >hostname.split('.');</span>
        const mainDomain = <span class="cstat-no" title="statement not covered" >domainParts.length &gt; 1 ? domainParts[domainParts.length - 2] : domainParts[0];</span>
<span class="cstat-no" title="statement not covered" >        return mainDomain ? mainDomain.charAt(0).toUpperCase() + mainDomain.slice(1) : 'unknown';</span>
    } catch (err) {
<span class="cstat-no" title="statement not covered" >        logger.warn(`[Wiflix Detail Final] Could not parse provider from URL ${urlString}: ${err.message}`);</span>
<span class="cstat-no" title="statement not covered" >        return 'unknown';</span>
    }
}
// --- End Helper ---
&nbsp;
async function <span class="fstat-no" title="function not covered" >scrapeWiflixDetail(</span>detailUrl) {
<span class="cstat-no" title="statement not covered" >    logger.info(`[Wiflix Detail Final] Scraping detail page using Puppeteer: ${detailUrl.substring(0, 100)}...`);</span>
&nbsp;
    // Get the latest WIFLIX_BASE from the database
    const wiflixBase = <span class="cstat-no" title="statement not covered" >await Config.getValue('WIFLIX_BASE', WIFLIX_BASE);</span>
<span class="cstat-no" title="statement not covered" >    WIFLIX_BASE_URL = `https://${wiflixBase}`;</span>
<span class="cstat-no" title="statement not covered" >    logger.info(`[Wiflix Detail Final] Using Wiflix base URL: ${WIFLIX_BASE_URL}`);</span>
&nbsp;
    let html;
    let timeout = <span class="cstat-no" title="statement not covered" >180000;</span> // Increased timeout to 3 minutes
    let retryCount = <span class="cstat-no" title="statement not covered" >0;</span>
    const maxRetries = <span class="cstat-no" title="statement not covered" >3;</span> // Increased max retries
&nbsp;
    // Check if this is a known problematic URL
    const isProblematicUrl = <span class="cstat-no" title="statement not covered" >detailUrl.includes('le-renard-rouge') ||</span>
                             detailUrl.includes('red-fox') ||
                             detailUrl.includes('renard-rouge');
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (isProblematicUrl) {</span>
<span class="cstat-no" title="statement not covered" >        logger.info(`[Wiflix Detail Final] Special handling for problematic URL: ${detailUrl}`);</span>
<span class="cstat-no" title="statement not covered" >        timeout = 120000; </span>// Increase timeout for problematic URLs
    }
&nbsp;
    async function <span class="fstat-no" title="function not covered" >attemptScrape(</span>) {
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >            logger.info(`[Wiflix Detail Final] Attempt ${retryCount + 1} with timeout ${timeout}ms`);</span>
<span class="cstat-no" title="statement not covered" >            html = await fetchPageWithPuppeteer(detailUrl, timeout, retryCount);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (!html) {</span>
<span class="cstat-no" title="statement not covered" >                throw new Error("Puppeteer returned null HTML for detail page.");</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (!containsRealContent(html)) {</span>
<span class="cstat-no" title="statement not covered" >                logger.warn(`[Wiflix Detail Final] Puppeteer HTML for ${detailUrl} missing key content markers.`);</span>
&nbsp;
                // For problematic URLs, add detailed diagnostics
<span class="cstat-no" title="statement not covered" >                if (isProblematicUrl) {</span>
                    const diagnosticHtml = <span class="cstat-no" title="statement not covered" >html.substring(0, 1000) + "...";</span> // First 1000 chars
<span class="cstat-no" title="statement not covered" >                    logger.debug(`[Wiflix Detail Final] HTML snippet for diagnostics: ${diagnosticHtml}`);</span>
                }
&nbsp;
                // Return empty object with path field included
<span class="cstat-no" title="statement not covered" >                return { streamingUrls: [], metadata: {}, episodes: [], thumbnail: undefined, thumbnailPath: undefined };</span>
            }
        } catch (error) {
            // Special handling for concurrency errors
<span class="cstat-no" title="statement not covered" >            if (error.message.includes('concurrency limit') || error.message.includes('Max retry attempts')) {</span>
<span class="cstat-no" title="statement not covered" >                logger.error(`[Wiflix Detail Final] Attempt ${retryCount + 1} failed due to concurrency limits: ${error.message}`);</span>
&nbsp;
                // Add a longer delay for concurrency issues to allow other requests to complete
                const concurrencyDelay = <span class="cstat-no" title="statement not covered" >5000 + (retryCount * 2000);</span>
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Waiting ${concurrencyDelay}ms before retry due to concurrency issues...`);</span>
<span class="cstat-no" title="statement not covered" >                await new Promise(<span class="fstat-no" title="function not covered" >re</span>solve =&gt; <span class="cstat-no" title="statement not covered" >setTimeout(resolve, concurrencyDelay))</span>;</span>
            } else {
<span class="cstat-no" title="statement not covered" >                logger.error(`[Wiflix Detail Final] Attempt ${retryCount + 1} failed: ${error.message}`);</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (retryCount &lt; maxRetries) {</span>
<span class="cstat-no" title="statement not covered" >                retryCount++;</span>
<span class="cstat-no" title="statement not covered" >                timeout += 30000; </span>// Increase timeout for each retry
<span class="cstat-no" title="statement not covered" >                return await attemptScrape();</span>
            }
<span class="cstat-no" title="statement not covered" >            throw error;</span>
        }
<span class="cstat-no" title="statement not covered" >        return html;</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >        html = await attemptScrape();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!html) { <span class="cstat-no" title="statement not covered" >throw new Error("All scrape attempts failed for detail page."); </span>}</span>
&nbsp;
        const $ = <span class="cstat-no" title="statement not covered" >cheerio.load(html);</span>
        const streamingUrls = <span class="cstat-no" title="statement not covered" >[];</span> // For movies
        const episodes = <span class="cstat-no" title="statement not covered" >[];</span>      // For series
&nbsp;
        // --- Metadata Extraction (Keep robust version) ---
        const metadata = <span class="cstat-no" title="statement not covered" >{};</span>
<span class="cstat-no" title="statement not covered" >        $('ul.mov-list li').each(<span class="fstat-no" title="function not covered" >(_</span>i, li) =&gt; {</span>
            const element = <span class="cstat-no" title="statement not covered" >$(li);</span>
            const labelElement = <span class="cstat-no" title="statement not covered" >element.find('strong').first();</span>
            let label = <span class="cstat-no" title="statement not covered" >labelElement.text().trim().toUpperCase().replace(':', '');</span>
<span class="cstat-no" title="statement not covered" >             if (!label) {</span>
                const liText = <span class="cstat-no" title="statement not covered" >element.clone().children().remove().end().text();</span>
                const parts = <span class="cstat-no" title="statement not covered" >liText.split(':');</span>
<span class="cstat-no" title="statement not covered" >                if (parts.length &gt; 1) { <span class="cstat-no" title="statement not covered" >label = parts[0].trim().toUpperCase(); </span>}</span>
            }
            const valueElement = <span class="cstat-no" title="statement not covered" >element.find('.mov-desc');</span>
            let valueText = <span class="cstat-no" title="statement not covered" >valueElement.text().trim();</span>
<span class="cstat-no" title="statement not covered" >            if (!valueText &amp;&amp; label) {</span>
<span class="cstat-no" title="statement not covered" >                valueText = element.text().substring(label.length + 1).trim();</span>
            } else <span class="cstat-no" title="statement not covered" >if (!valueText &amp;&amp; !label) {</span>
<span class="cstat-no" title="statement not covered" >                valueText = element.text().trim();</span>
            }
&nbsp;
            // ... (keep all the metadata extraction logic: SYNOPSIS, ACTEURS, etc.)
<span class="cstat-no" title="statement not covered" >             if (label.includes('SYNOPSIS')) {</span>
                let synopsisRaw = <span class="cstat-no" title="statement not covered" >valueElement.find('span[itemprop="description"]').text().trim() || valueText;</span>
                const synopsisPrefixRegex = <span class="cstat-no" title="statement not covered" >/^(Résumé|Synopsis)\s*(du film|de la série)?\s*.*?(- Saison \d+)?\s*(en Streaming Complet)?:?\s*/i;</span>
<span class="cstat-no" title="statement not covered" >                metadata.synopsis = synopsisRaw.replace(synopsisPrefixRegex, '').trim();</span>
             } else <span class="cstat-no" title="statement not covered" >if (label.includes('ACTEURS')) {</span>
<span class="cstat-no" title="statement not covered" >                 metadata.actors = valueElement.find('span[itemprop="actor"]').map(<span class="fstat-no" title="function not covered" >(_</span>j, actor) =&gt; <span class="cstat-no" title="statement not covered" >$(actor).text().trim())</span>.get();</span>
<span class="cstat-no" title="statement not covered" >                 if (!metadata.actors || metadata.actors.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >                     metadata.actors = valueText.split(',').map(<span class="fstat-no" title="function not covered" >a </span>=&gt; <span class="cstat-no" title="statement not covered" >a.trim())</span>.filter(Boolean);</span>
                 }
             } else <span class="cstat-no" title="statement not covered" >if (label.includes('DATE DE SORTIE')) {</span>
<span class="cstat-no" title="statement not covered" >                 metadata.year = valueText.match(/\d{4}/)?.[0] || '';</span>
             } else <span class="cstat-no" title="statement not covered" >if (label.includes('GENRE')) {</span>
<span class="cstat-no" title="statement not covered" >                 metadata.genre = valueElement.find('span[itemprop="genre"]').map(<span class="fstat-no" title="function not covered" >(_</span>j, genre) =&gt; <span class="cstat-no" title="statement not covered" >$(genre).text().trim())</span>.get().join(', ');</span>
<span class="cstat-no" title="statement not covered" >                  if (!metadata.genre) { <span class="cstat-no" title="statement not covered" >metadata.genre = valueText; </span>}</span>
             } else <span class="cstat-no" title="statement not covered" >if (label.includes('ORIGINE')) {</span>
<span class="cstat-no" title="statement not covered" >                 metadata.origin = valueText;</span>
             } else <span class="cstat-no" title="statement not covered" >if (label.includes('RÉALISATEUR')) {</span>
<span class="cstat-no" title="statement not covered" >                 metadata.creator = valueElement.find('span[itemprop="name"]').text().trim() || valueText;</span>
             } else <span class="cstat-no" title="statement not covered" >if (label.includes('DURÉE')) {</span>
                 const directorName = <span class="cstat-no" title="statement not covered" >$('li:contains("RÉALISATEUR") .mov-desc span[itemprop="name"]').text().trim();</span>
                 let durationText = <span class="cstat-no" title="statement not covered" >valueText;</span>
<span class="cstat-no" title="statement not covered" >                 if (directorName &amp;&amp; durationText.includes(directorName)) { <span class="cstat-no" title="statement not covered" >durationText = durationText.replace(directorName, '').trim(); </span>}</span>
<span class="cstat-no" title="statement not covered" >                 metadata.duration = durationText || '';</span>
             }
        });
        // --- End Metadata ---
&nbsp;
        // --- Thumbnail Extraction ---
        let thumbnail = <span class="cstat-no" title="statement not covered" >$('#posterimg[itemprop="thumbnailUrl"]').attr('src') || $('.mov-i img[itemprop="image"]').attr('src') || '';</span>
        let thumbnailPath = <span class="cstat-no" title="statement not covered" >'';</span> // Initialize path
<span class="cstat-no" title="statement not covered" >        if (thumbnail) {</span>
            // Ensure thumbnail URL is absolute before extracting path
<span class="cstat-no" title="statement not covered" >            if (!thumbnail.startsWith('http')) {</span>
<span class="cstat-no" title="statement not covered" >                thumbnail = `${WIFLIX_BASE_URL}${thumbnail.startsWith('/') ? '' : '/'}${thumbnail}`;</span>
            }
            // Extract path from the absolute URL
<span class="cstat-no" title="statement not covered" >            thumbnailPath = extractPathFromUrl(thumbnail, 'Wiflix Detail Final');</span>
        }
        // --- End Thumbnail ---
&nbsp;
&nbsp;
        // --- Stream/Episode Extraction (Enhanced Logic for Both) ---
        const episodeBlocks = <span class="cstat-no" title="statement not covered" >$('.epblocks .blocvostfr, .epblocks .blocfr');</span>
&nbsp;
        // Log all episode blocks for debugging problematic URLs
<span class="cstat-no" title="statement not covered" >        if (isProblematicUrl) {</span>
<span class="cstat-no" title="statement not covered" >            logger.info(`[Wiflix Detail Final] Detailed episode block analysis for problematic URL`);</span>
<span class="cstat-no" title="statement not covered" >            logger.info(`[Wiflix Detail Final] Episode blocks count: ${episodeBlocks.length}`);</span>
<span class="cstat-no" title="statement not covered" >            logger.info(`[Wiflix Detail Final] All episode blocks: ${$('.epblocks').html()?.substring(0, 200)}...`);</span>
&nbsp;
            // Log all rel attributes
            const relAttributes = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >            $('li.clicbtn').each(<span class="fstat-no" title="function not covered" >(i</span>, el) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                relAttributes.push($(el).attr('rel'));</span>
            });
<span class="cstat-no" title="statement not covered" >            logger.info(`[Wiflix Detail Final] Rel attributes: ${relAttributes.join(', ')}`);</span>
&nbsp;
            // Check if host blocks exist for each rel attribute
<span class="cstat-no" title="statement not covered" >            relAttributes.forEach(<span class="fstat-no" title="function not covered" >re</span>l =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Host block for ${rel} exists: ${$(`.hostsblock .${rel}`).length &gt; 0}`);</span>
<span class="cstat-no" title="statement not covered" >                if ($(`.hostsblock .${rel}`).length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                    logger.info(`[Wiflix Detail Final] Links in ${rel}: ${$(`.hostsblock .${rel} a`).length}`);</span>
                }
            });
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (episodeBlocks.length &gt; 0) {</span>
            // Series Logic
<span class="cstat-no" title="statement not covered" >            logger.info(`[Wiflix Detail Final] Found ${episodeBlocks.length} language blocks. Processing as Series.`);</span>
            const seasonMatch = <span class="cstat-no" title="statement not covered" >detailUrl.match(/saison-(\d+)/i);</span>
            const season = <span class="cstat-no" title="statement not covered" >seasonMatch ? seasonMatch[1] : '1';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            episodeBlocks.each(<span class="fstat-no" title="function not covered" >(_</span>, langBlock) =&gt; {</span>
                const isVostfr = <span class="cstat-no" title="statement not covered" >$(langBlock).hasClass('blocvostfr');</span>
                const language = <span class="cstat-no" title="statement not covered" >isVostfr ? 'VOSTFR' : 'VF';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Processing ${language} block`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                $(langBlock).find('ul.eplist li.clicbtn').each(<span class="fstat-no" title="function not covered" >(_</span>i, epTab) =&gt; {</span>
                    const epText = <span class="cstat-no" title="statement not covered" >$(epTab).text().trim();</span>
                    const epMatch = <span class="cstat-no" title="statement not covered" >epText.match(/(\d+)/);</span>
                    let episodeNumber = <span class="cstat-no" title="statement not covered" >epMatch ? epMatch[1] : null;</span>
                    const relAttr = <span class="cstat-no" title="statement not covered" >$(epTab).attr('rel');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    logger.info(`[Wiflix Detail Final] Found episode tab: "${epText}" with rel="${relAttr}"`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    if (!episodeNumber &amp;&amp; relAttr) {</span>
                         const relMatch = <span class="cstat-no" title="statement not covered" >relAttr.match(/ep(\d+)/i);</span>
<span class="cstat-no" title="statement not covered" >                         if(relMatch) <span class="cstat-no" title="statement not covered" >episodeNumber = relMatch[1];</span></span>
                    }
&nbsp;
                    // Additional fallback for episode number extraction
<span class="cstat-no" title="statement not covered" >                    if (!episodeNumber &amp;&amp; epText) {</span>
                        // Try to extract any number from the text as a last resort
                        const anyNumberMatch = <span class="cstat-no" title="statement not covered" >epText.match(/\d+/);</span>
<span class="cstat-no" title="statement not covered" >                        if (anyNumberMatch) {</span>
<span class="cstat-no" title="statement not covered" >                            episodeNumber = anyNumberMatch[0];</span>
<span class="cstat-no" title="statement not covered" >                            logger.info(`[Wiflix Detail Final] Extracted episode number ${episodeNumber} from text as fallback`);</span>
                        }
                    }
&nbsp;
<span class="cstat-no" title="statement not covered" >                    if (!episodeNumber || !relAttr) {</span>
<span class="cstat-no" title="statement not covered" >                        logger.warn(`[Wiflix Detail Final] Could not extract episode number or rel attribute from tab: "${epText}"`);</span>
<span class="cstat-no" title="statement not covered" >                        return;</span>
                    }
&nbsp;
                    // Try multiple selector patterns for the links container
                    let linksContainer = <span class="cstat-no" title="statement not covered" >$(`.hostsblock .${relAttr}`);</span>
&nbsp;
                    // If not found, try alternative selectors
<span class="cstat-no" title="statement not covered" >                    if (linksContainer.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >                        logger.warn(`[Wiflix Detail Final] Could not find links container with selector .hostsblock .${relAttr}`);</span>
&nbsp;
                        // Try alternative selectors
                        const alternativeSelectors = <span class="cstat-no" title="statement not covered" >[</span>
                            `.hostsblock div[class*="${relAttr}"]`,
                            `.hostsblock div[id*="${relAttr}"]`,
                            `.hostsblock div[class*="${episodeNumber}"]`,
                            `.hostsblock div[id*="${episodeNumber}"]`
                        ];
&nbsp;
<span class="cstat-no" title="statement not covered" >                        for (const selector of alternativeSelectors) {</span>
                            const altContainer = <span class="cstat-no" title="statement not covered" >$(selector);</span>
<span class="cstat-no" title="statement not covered" >                            if (altContainer.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                                logger.info(`[Wiflix Detail Final] Found alternative links container with selector: ${selector}`);</span>
<span class="cstat-no" title="statement not covered" >                                linksContainer = altContainer;</span>
<span class="cstat-no" title="statement not covered" >                                break;</span>
                            }
                        }
&nbsp;
                        // If still not found, try a more aggressive approach for problematic URLs
<span class="cstat-no" title="statement not covered" >                        if (linksContainer.length === 0 &amp;&amp; isProblematicUrl) {</span>
<span class="cstat-no" title="statement not covered" >                            logger.info(`[Wiflix Detail Final] Using aggressive link extraction for problematic URL`);</span>
                            // For problematic URLs, just get all links in the hostsblock
<span class="cstat-no" title="statement not covered" >                            linksContainer = $('.hostsblock');</span>
                        }
                    }
&nbsp;
                    const episodeStreamingUrls = <span class="cstat-no" title="statement not covered" >[];</span>
&nbsp;
                    // Enhanced streaming URL extraction with better error handling
<span class="cstat-no" title="statement not covered" >                    linksContainer.find('a[onclick*="loadVideo"]').each(<span class="fstat-no" title="function not covered" >(_</span>j, link) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                        try {</span>
                            const onclickAttr = <span class="cstat-no" title="statement not covered" >$(link).attr('onclick') || '';</span>
                            const urlMatch = <span class="cstat-no" title="statement not covered" >onclickAttr.match(/loadVideo\(['"]([^'"]+)['"]\)/);</span>
                            const hostText = <span class="cstat-no" title="statement not covered" >$(link).find('span.clichost').text().trim();</span> // Get "Lecteur X" or actual name
&nbsp;
                            // Log for problematic URLs
<span class="cstat-no" title="statement not covered" >                            if (isProblematicUrl) {</span>
<span class="cstat-no" title="statement not covered" >                                logger.info(`[Wiflix Detail Final] Processing link: onclick="${onclickAttr}", hostText="${hostText}"`);</span>
                            }
&nbsp;
<span class="cstat-no" title="statement not covered" >                            if (urlMatch &amp;&amp; urlMatch[1]) {</span>
                                const streamUrl = <span class="cstat-no" title="statement not covered" >urlMatch[1].trim();</span>
                                let providerName = <span class="cstat-no" title="statement not covered" >hostText.toLowerCase();</span>
                                // If provider is generic, try getting from URL
<span class="cstat-no" title="statement not covered" >                                if (providerName.startsWith('lecteur') || providerName === 'unknown' || !providerName) {</span>
<span class="cstat-no" title="statement not covered" >                                    providerName = getProviderFromUrl(streamUrl);</span>
                                } else {
                                    // Attempt to capitalize if it's not generic (like "DDSTREAM" -&gt; "Ddstream")
<span class="cstat-no" title="statement not covered" >                                    providerName = providerName.charAt(0).toUpperCase() + providerName.slice(1).toLowerCase();</span>
                                }
&nbsp;
                                // Validate URL before adding
<span class="cstat-no" title="statement not covered" >                                if (streamUrl &amp;&amp; streamUrl.startsWith('http')) {</span>
<span class="cstat-no" title="statement not covered" >                                    episodeStreamingUrls.push({</span>
                                        url: streamUrl,
                                        provider: providerName, // Use determined name
                                        language: language,
                                    });
&nbsp;
<span class="cstat-no" title="statement not covered" >                                    if (isProblematicUrl) {</span>
<span class="cstat-no" title="statement not covered" >                                        logger.info(`[Wiflix Detail Final] Added streaming URL: ${streamUrl} (${providerName})`);</span>
                                    }
                                } else {
<span class="cstat-no" title="statement not covered" >                                    logger.warn(`[Wiflix Detail Final] Invalid streaming URL: "${streamUrl}"`);</span>
                                }
                            } else <span class="cstat-no" title="statement not covered" >if (isProblematicUrl) {</span>
                                // For problematic URLs, try a more aggressive approach
                                // Look for any URL in the onclick attribute
                                const anyUrlMatch = <span class="cstat-no" title="statement not covered" >onclickAttr.match(/https?:\/\/[^'"]+/);</span>
<span class="cstat-no" title="statement not covered" >                                if (anyUrlMatch &amp;&amp; anyUrlMatch[0]) {</span>
                                    const streamUrl = <span class="cstat-no" title="statement not covered" >anyUrlMatch[0].trim();</span>
                                    const providerName = <span class="cstat-no" title="statement not covered" >getProviderFromUrl(streamUrl);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                                    logger.info(`[Wiflix Detail Final] Found URL with aggressive matching: ${streamUrl}`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                                    episodeStreamingUrls.push({</span>
                                        url: streamUrl,
                                        provider: providerName,
                                        language: language,
                                    });
                                } else {
<span class="cstat-no" title="statement not covered" >                                    logger.warn(`[Wiflix Detail Final] Could not extract URL from onclick: "${onclickAttr}"`);</span>
                                }
                            }
                        } catch (error) {
<span class="cstat-no" title="statement not covered" >                            logger.error(`[Wiflix Detail Final] Error processing streaming link: ${error.message}`);</span>
                        }
                    });
&nbsp;
<span class="cstat-no" title="statement not covered" >                    if (episodeStreamingUrls.length &gt; 0) {</span>
                         let existingEp = <span class="cstat-no" title="statement not covered" >episodes.find(<span class="fstat-no" title="function not covered" >e </span>=&gt; <span class="cstat-no" title="statement not covered" >String(e.episodeNumber) === String(episodeNumber) &amp;&amp; String(e.season) === String(season))</span>;</span>
<span class="cstat-no" title="statement not covered" >                         if (existingEp) {</span>
<span class="cstat-no" title="statement not covered" >                             existingEp.streamingUrls.push(...episodeStreamingUrls);</span>
<span class="cstat-no" title="statement not covered" >                              if (existingEp.language === 'unknown' &amp;&amp; language !== 'unknown') {</span>
<span class="cstat-no" title="statement not covered" >                                 existingEp.language = language;</span>
                             } else <span class="cstat-no" title="statement not covered" >if (existingEp.language !== language &amp;&amp; existingEp.language !== 'Multi') {</span>
<span class="cstat-no" title="statement not covered" >                                 existingEp.language = 'Multi';</span>
                             }
                         } else {
<span class="cstat-no" title="statement not covered" >                             episodes.push({</span>
                                episodeNumber: String(episodeNumber),
                                season: String(season),
                                language: language,
                                streamingUrls: episodeStreamingUrls
                             });
                         }
                    } else {
<span class="cstat-no" title="statement not covered" >                         if(linksContainer.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                            logger.warn(`[Wiflix Detail Final] No streaming URLs found in container .${relAttr} for Ep ${episodeNumber} (${language}) on ${detailUrl}`);</span>
                        } else {
<span class="cstat-no" title="statement not covered" >                             logger.warn(`[Wiflix Detail Final] Could not find links container .${relAttr} for Ep ${episodeNumber} (${language}) on ${detailUrl}`);</span>
                        }
                    }
                });
            });
&nbsp;
<span class="cstat-no" title="statement not covered" >             episodes.sort(<span class="fstat-no" title="function not covered" >(a</span>, b) =&gt; {</span>
                const numA = <span class="cstat-no" title="statement not covered" >parseInt(a.episodeNumber, 10);</span>
                const numB = <span class="cstat-no" title="statement not covered" >parseInt(b.episodeNumber, 10);</span>
<span class="cstat-no" title="statement not covered" >                if (isNaN(numA) || isNaN(numB)) <span class="cstat-no" title="statement not covered" >return String(a.episodeNumber).localeCompare(String(b.episodeNumber));</span></span>
<span class="cstat-no" title="statement not covered" >                return numA - numB;</span>
             });
&nbsp;
        } else {
            // Movie Logic - Look inside `.tabsbox .linkstab` with more flexible selectors
<span class="cstat-no" title="statement not covered" >            logger.debug(`[Wiflix Detail Final] No episode blocks found, processing as Movie.`);</span>
&nbsp;
            // Try multiple selectors for movie streaming links
            const movieLinkSelectors = <span class="cstat-no" title="statement not covered" >[</span>
                '.tabsbox .linkstab a[onclick*="loadVideo"]',
                '.tabsbox a[onclick*="loadVideo"]',
                '.linkstab a[onclick*="loadVideo"]',
                'a[onclick*="loadVideo"]',
                '.player-option',
                '.server-item',
                '.streaming-server',
                '.play-btn',
                'a[data-link]',
                'a[data-url]',
                'a[href*="embed"]',
                'a[href*="player"]',
                'a[href*="stream"]',
                'a[href*="watch"]'
            ];
&nbsp;
            let movieServerLinks = <span class="cstat-no" title="statement not covered" >$();</span>
&nbsp;
            // Try each selector until we find links
<span class="cstat-no" title="statement not covered" >            for (const selector of movieLinkSelectors) {</span>
                const links = <span class="cstat-no" title="statement not covered" >$(selector);</span>
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Selector '${selector}' found ${links.length} movie server links`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                if (links.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                    movieServerLinks = links;</span>
<span class="cstat-no" title="statement not covered" >                    break;</span>
                }
            }
&nbsp;
            // If we found links, process them
<span class="cstat-no" title="statement not covered" >            if (movieServerLinks.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                movieServerLinks.each(<span class="fstat-no" title="function not covered" >(_</span>i, link) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    try {</span>
                        const element = <span class="cstat-no" title="statement not covered" >$(link);</span>
                        const onclickAttr = <span class="cstat-no" title="statement not covered" >element.attr('onclick') || '';</span>
                        const dataLink = <span class="cstat-no" title="statement not covered" >element.attr('data-link') || element.attr('data-url') || '';</span>
                        const href = <span class="cstat-no" title="statement not covered" >element.attr('href') || '';</span>
&nbsp;
                        // Try to extract URL from onclick attribute
                        let streamUrl = <span class="cstat-no" title="statement not covered" >null;</span>
                        let urlMatch = <span class="cstat-no" title="statement not covered" >onclickAttr.match(/loadVideo\(['"]([^'"]+)['"]\)/);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                        if (urlMatch &amp;&amp; urlMatch[1]) {</span>
<span class="cstat-no" title="statement not covered" >                            streamUrl = urlMatch[1].trim();</span>
                        } else <span class="cstat-no" title="statement not covered" >if (dataLink &amp;&amp; dataLink.startsWith('http')) {</span>
                            // Try data-link or data-url attribute
<span class="cstat-no" title="statement not covered" >                            streamUrl = dataLink;</span>
                        } else <span class="cstat-no" title="statement not covered" >if (href &amp;&amp; href.startsWith('http') &amp;&amp; !href.includes('javascript:')) {</span>
                            // Try href attribute
<span class="cstat-no" title="statement not covered" >                            streamUrl = href;</span>
                        } else <span class="cstat-no" title="statement not covered" >if (onclickAttr) {</span>
                            // Try to find any URL in the onclick attribute
                            const anyUrlMatch = <span class="cstat-no" title="statement not covered" >onclickAttr.match(/https?:\/\/[^'"]+/);</span>
<span class="cstat-no" title="statement not covered" >                            if (anyUrlMatch &amp;&amp; anyUrlMatch[0]) {</span>
<span class="cstat-no" title="statement not covered" >                                streamUrl = anyUrlMatch[0].trim();</span>
                            }
                        }
&nbsp;
                        // If we found a URL, add it to streamingUrls
<span class="cstat-no" title="statement not covered" >                        if (streamUrl) {</span>
                            const hostText = <span class="cstat-no" title="statement not covered" >element.find('span').first().text().trim() || element.text().trim();</span> // Get text like "DdStream", "Voe"
                            const linkText = <span class="cstat-no" title="statement not covered" >element.text().toLowerCase();</span> // For language detection
                            const langMatch = <span class="cstat-no" title="statement not covered" >linkText.includes('vostfr') ? 'VOSTFR' : (linkText.includes('vf') || linkText.includes('french') || linkText.includes('truefrench') ? 'VF' : 'unknown');</span> // Improved language check
&nbsp;
                            let providerName = <span class="cstat-no" title="statement not covered" >hostText;</span> // Trust the text first for movies
<span class="cstat-no" title="statement not covered" >                            if (!providerName || providerName.toLowerCase().startsWith('lecteur')) {</span>
<span class="cstat-no" title="statement not covered" >                                providerName = getProviderFromUrl(streamUrl); </span>// Fallback to URL parsing
                            } else {
                                // Basic capitalization
<span class="cstat-no" title="statement not covered" >                                providerName = providerName.charAt(0).toUpperCase() + providerName.slice(1).toLowerCase();</span>
                            }
&nbsp;
<span class="cstat-no" title="statement not covered" >                            streamingUrls.push({</span>
                                url: streamUrl,
                                provider: providerName, // Use extracted/parsed name
                                language: langMatch,
                            });
&nbsp;
<span class="cstat-no" title="statement not covered" >                            logger.info(`[Wiflix Detail Final] Added streaming URL: ${streamUrl} (${providerName})`);</span>
                        } else {
<span class="cstat-no" title="statement not covered" >                            logger.warn(`[Wiflix Detail Final] Could not extract URL from movie link: ${element.html()}`);</span>
                        }
                    } catch (error) {
<span class="cstat-no" title="statement not covered" >                        logger.error(`[Wiflix Detail Final] Error processing movie link: ${error.message}`);</span>
                    }
                });
            } else {
                // If no links found with selectors, try a more aggressive approach
<span class="cstat-no" title="statement not covered" >                logger.warn(`[Wiflix Detail Final] No movie server links found with standard selectors, trying more aggressive approach`);</span>
&nbsp;
                // Try to find any links that might be streaming links
                const allLinks = <span class="cstat-no" title="statement not covered" >$('a[href^="http"]');</span>
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Found ${allLinks.length} links that might be streaming links`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                if (allLinks.length &gt; 0) {</span>
                    // Filter links that look like streaming links
<span class="cstat-no" title="statement not covered" >                    allLinks.each(<span class="fstat-no" title="function not covered" >(_</span>i, link) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                        try {</span>
                            const element = <span class="cstat-no" title="statement not covered" >$(link);</span>
                            const href = <span class="cstat-no" title="statement not covered" >element.attr('href');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                            if (href &amp;&amp; (</span>
                                href.includes('embed') ||
                                href.includes('player') ||
                                href.includes('stream') ||
                                href.includes('watch') ||
                                href.includes('video') ||
                                href.includes('play')
                            )) {
                                const streamUrl = <span class="cstat-no" title="statement not covered" >href;</span>
                                const providerName = <span class="cstat-no" title="statement not covered" >getProviderFromUrl(streamUrl);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                                streamingUrls.push({</span>
                                    url: streamUrl,
                                    provider: providerName,
                                    language: 'unknown',
                                });
&nbsp;
<span class="cstat-no" title="statement not covered" >                                logger.info(`[Wiflix Detail Final] Added streaming URL (aggressive): ${streamUrl} (${providerName})`);</span>
                            }
                        } catch (error) {
<span class="cstat-no" title="statement not covered" >                            logger.error(`[Wiflix Detail Final] Error processing link in aggressive approach: ${error.message}`);</span>
                        }
                    });
                }
&nbsp;
<span class="cstat-no" title="statement not covered" >                if (streamingUrls.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >                    logger.warn(`[Wiflix Detail Final] No streaming URLs found for ${detailUrl} using any method`);</span>
                }
            }
        }
        // --- END Logic ---
&nbsp;
        // Enhanced result logging
<span class="cstat-no" title="statement not covered" >        logger.info(`[Wiflix Detail Final] Scraped: ${detailUrl.substring(0,100)}... | Episodes: ${episodes.length}, Movie Streams: ${streamingUrls.length}`);</span>
&nbsp;
        // For problematic URLs, log more details about the results
<span class="cstat-no" title="statement not covered" >        if (isProblematicUrl) {</span>
<span class="cstat-no" title="statement not covered" >            logger.info(`[Wiflix Detail Final] Detailed results for problematic URL:`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (episodes.length &gt; 0) {</span>
                const episodeSummary = <span class="cstat-no" title="statement not covered" >episodes.map(<span class="fstat-no" title="function not covered" >ep</span> =&gt;</span>
<span class="cstat-no" title="statement not covered" >                    `S${ep.season}:E${ep.episodeNumber} (${ep.streamingUrls.length} streams)`</span>
                ).join(', ');
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Episodes found: ${episodeSummary}`);</span>
            } else <span class="cstat-no" title="statement not covered" >if (streamingUrls.length &gt; 0) {</span>
                const streamSummary = <span class="cstat-no" title="statement not covered" >streamingUrls.map(<span class="fstat-no" title="function not covered" >s </span>=&gt; <span class="cstat-no" title="statement not covered" >s.provider)</span>.join(', ');</span>
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Stream providers: ${streamSummary}`);</span>
            } else {
<span class="cstat-no" title="statement not covered" >                logger.warn(`[Wiflix Detail Final] No episodes or streams found for problematic URL`);</span>
            }
&nbsp;
            // Log metadata
            const metadataKeys = <span class="cstat-no" title="statement not covered" >Object.keys(metadata);</span>
<span class="cstat-no" title="statement not covered" >            if (metadataKeys.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Metadata keys found: ${metadataKeys.join(', ')}`);</span>
            } else {
<span class="cstat-no" title="statement not covered" >                logger.warn(`[Wiflix Detail Final] No metadata found for problematic URL`);</span>
            }
        }
&nbsp;
        const result = <span class="cstat-no" title="statement not covered" >{</span>
            streamingUrls: streamingUrls, // Structured for movies
            metadata,
            episodes: episodes, // Structured for series
            thumbnail: thumbnail || undefined, // Keep full URL
            thumbnailPath: thumbnailPath || undefined, // Add path
        };
&nbsp;
<span class="cstat-no" title="statement not covered" >        return result;</span>
&nbsp;
    } catch (error) {
<span class="cstat-no" title="statement not covered" >        logger.error(`[Wiflix Detail Final] Failed Puppeteer scrape for ${detailUrl}: ${error.message}`, { stack: error.stack });</span>
&nbsp;
        // For problematic URLs, provide more detailed error information
<span class="cstat-no" title="statement not covered" >        if (isProblematicUrl) {</span>
<span class="cstat-no" title="statement not covered" >            logger.error(`[Wiflix Detail Final] Detailed error for problematic URL: ${error.stack}`);</span>
&nbsp;
            // Try one last approach for problematic URLs - use a different scraping strategy
<span class="cstat-no" title="statement not covered" >            try {</span>
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Attempting alternative scraping approach for problematic URL`);</span>
&nbsp;
                // This could be implemented with a different scraping strategy
                // For now, we'll just return empty results
<span class="cstat-no" title="statement not covered" >                logger.info(`[Wiflix Detail Final] Alternative approach not implemented yet`);</span>
            } catch (altError) {
<span class="cstat-no" title="statement not covered" >                logger.error(`[Wiflix Detail Final] Alternative approach failed: ${altError.message}`);</span>
            }
        }
&nbsp;
        // Return empty object with path field included
<span class="cstat-no" title="statement not covered" >        return { streamingUrls: [], metadata: {}, episodes: [], thumbnail: undefined, thumbnailPath: undefined };</span>
    }
}
&nbsp;
<span class="cstat-no" title="statement not covered" >module.exports = { scrapeWiflixDetail };</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T16:31:40.786Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    