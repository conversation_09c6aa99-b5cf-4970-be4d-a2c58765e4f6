
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for NetStream_graphql/src/config/constants.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">NetStream_graphql/src/config</a> constants.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/3</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/3</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// NetStream_GraphQL/src/config/constants.js
const SCRAPE_MODE = <span class="cstat-no" title="statement not covered" >{</span>
    FULL: 'full',
    LATEST: 'latest'
  };
  
  const PROVIDER_CONFIG = <span class="cstat-no" title="statement not covered" >{</span>
    'magasavor.net': { baseUrl: 'https://magasavor.net', headers: { 'Referer': 'https://magasavor.net/', 'User-Agent': 'Mozilla/5.0' }, altDomains: ['magasavor', 'magasavor.nete', 'alejandrocenturyoil.com'] },
    'vidply.com': { baseUrl: 'https://vidply.com', headers: { 'Referer': 'https://vidply.com/' }, altDomains: ['vidply', 'vidply.come'] },
    'vidmoly.to': { baseUrl: 'https://vidmoly.to', headers: { 'Referer': 'https://vidmoly.to/' }, altDomains: ['vidmoly.me', 'vidmoly'] },
    'luluvdo.com': { baseUrl: 'https://luluvdo.com', headers: { 'Referer': 'https://luluvdo.com/' }, altDomains: ['luluvdo'] },
    'tipfly.xyz': { baseUrl: 'https://tipfly.xyz', headers: { 'Referer': 'https://tipfly.xyz/' } },
    'uqload.net': { baseUrl: 'https://uqload.net', headers: { 'Referer': 'https://uqload.net/', 'Origin': 'https://uqload.net', 'X-Requested-With': 'XMLHttpRequest' }, altDomains: ['uqload.to', 'uqloada.ws', 'uqload.'] },
    'oneupload.to': { baseUrl: 'https://oneupload.to', headers: { 'Referer': 'https://oneupload.to/' }, altDomains: ['oneupload.net'] },
    'waaw1.tv': { baseUrl: 'https://waaw1.tv', headers: { 'Referer': 'https://waaw1.tv/' }, altDomains: ['waaw.to', 'waaw.tv', 'waaw1.tve'] },
    'filegram.to': { baseUrl: 'https://filegram.to', headers: { 'Referer': 'https://filegram.to/' } },
    'dooodster.com': { baseUrl: 'https://dooodster.com', headers: { 'Referer': 'https://dooodster.com/' }, altDomains: ['dooood.com', 'dood.re', 'dood.wf', 'dood.pro', 'dood.sh'] },
    'voe.sx': { baseUrl: 'https://voe.sx', headers: { 'Referer': 'https://voe.sx/' } },
    'cybervynx.com': { baseUrl: 'https://cybervynx.com', headers: { 'Referer': 'https://cybervynx.com/' } },
    'sbface.com': { baseUrl: 'https://sbface.com', headers: { 'Referer': 'https://sbface.com/' }, altDomains: ['sbanh.com', 'sbchill.com', 'sbrity.com', 'sbbrisk.com', 'sblanh.com', 'sbhight.com', 'sbspeed.com'] },
    'lvturbo.com': { baseUrl: 'https://lvturbo.com', headers: { 'Referer': 'https://lvturbo.com/' } },
    'streamsilk.com': { baseUrl: 'https://streamsilk.com', headers: { 'Referer': 'https://streamsilk.com/' } },
    'd0000d.com': { baseUrl: 'https://d0000d.com', headers: { 'Referer': 'https://d0000d.com/' }, altDomains: ['d000d.com', 'd0o0d.com'] },
    'streamdav.com': { baseUrl: 'https://streamdav.com', headers: { 'Referer': 'https://streamdav.com/' } },
    'streamvid.net': { baseUrl: 'https://streamvid.net', headers: { 'Referer': 'https://streamvid.net/' } },
    'mixdrop.ps': { baseUrl: 'https://mixdrop.ps', headers: { 'Referer': 'https://mixdrop.ps/' }, altDomains: ['mixdrop.co'] },
    'vido.lol': { baseUrl: 'https://vido.lol', headers: { 'Referer': 'https://vido.lol/' }, altDomains: ['vido.lo', 'vido.lole'] },
    'upstream.to': { baseUrl: 'https://upstream.to', headers: { 'Referer': 'https://upstream.to/' }, altDomains: ['upstream.co'] },
    'upvideo.to': { baseUrl: 'https://upvideo.to', headers: { 'Referer': 'https://upvideo.to/' } },
    'ssblongvu.com': { baseUrl: 'https://ssblongvu.com', headers: { 'Referer': 'https://ssblongvu.com/' } },
    'streamhide.to': { baseUrl: 'https://streamhide.to', headers: { 'Referer': 'https://streamhide.to/' } },
    'louishide.com': { baseUrl: 'https://louishide.com', headers: { 'Referer': 'https://louishide.com/' } },
    'vudeo.ws': { baseUrl: 'https://vudeo.ws', headers: { 'Referer': 'https://vudeo.ws/' }, altDomains: ['vudeo.nlt'] },
    'guccihide.com': { baseUrl: 'https://guccihide.com', headers: { 'Referer': 'https://guccihide.com/' } },
    'evoload.io': { baseUrl: 'https://evoload.io', headers: { 'Referer': 'https://evoload.io/' }, altDomains: ['evoload.ioev', 'evoload.net'] },
    'mvidoo.com': { baseUrl: 'https://mvidoo.com', headers: { 'Referer': 'https://mvidoo.com/' } },
    'ds2play.com': { baseUrl: 'https://ds2play.com', headers: { 'Referer': 'https://ds2play.com/' } },
    'vidhidevip.com': { baseUrl: 'https://vidhidevip.com', headers: { 'Referer': 'https://vidhidevip.com/' } },
    'streamtape.com': { baseUrl: 'https://streamtape.com', headers: { 'Referer': 'https://streamtape.com/' } },
    'streamhub.gg': { baseUrl: 'https://streamhub.gg', headers: { 'Referer': 'https://streamhub.gg/' }, altDomains: ['streamhub.top'] },
    'filemoon.sx': { baseUrl: 'https://filemoon.sx', headers: { 'Referer': 'https://filemoon.sx/' } },
    'sdefx.cloud': { baseUrl: 'https://sdefx.cloud', headers: { 'Referer': 'https://sdefx.cloud/' } },
    'wiflix.online': { baseUrl: 'https://wiflix.online', headers: { 'Referer': 'https://wiflix.online/' } },
    'vidfast.co': { baseUrl: 'https://vidfast.co', headers: { 'Referer': 'https://vidfast.co/' }, altDomains: ['go.vidfast.co'] },
    'abcvideo.cc': { baseUrl: 'https://abcvideo.cc', headers: { 'Referer': 'https://abcvideo.cc/' } },
    'aparat.cam': { baseUrl: 'https://aparat.cam', headers: { 'Referer': 'https://aparat.cam/' } },
    'players.wiflix-pro.mom': { baseUrl: 'https://players.wiflix-pro.mom', headers: { 'Referer': 'https://players.wiflix-pro.mom/' } },
    'upvid.co': { baseUrl: 'https://upvid.co', headers: { 'Referer': 'https://upvid.co/' } },
    'hlsplay.com': { baseUrl: 'https://hlsplay.com', headers: { 'Referer': 'https://hlsplay.com/' } },
    'tazvids.to': { baseUrl: 'https://tazvids.to', headers: { 'Referer': 'https://tazvids.to/' } },
    'video.sibnet.ru': { baseUrl: 'https://video.sibnet.ru', headers: { 'Referer': 'https://video.sibnet.ru/' } },
    'www.myvi.xyz': { baseUrl: 'https://www.myvi.xyz', headers: { 'Referer': 'https://www.myvi.xyz/' } },
    'userload.co': { baseUrl: 'https://userload.co', headers: { 'Referer': 'https://userload.co/' } },
    'embed.mystream.to': { baseUrl: 'https://embed.mystream.to', headers: { 'Referer': 'https://embed.mystream.to/' } },
    'www.fembed.com': { baseUrl: 'https://www.fembed.com', headers: { 'Referer': 'https://www.fembed.com/' } },
    'hirudinoid-prisoner.hostingerapp.com': { baseUrl: 'https://hirudinoid-prisoner.hostingerapp.com', headers: { 'Referer': 'https://hirudinoid-prisoner.hostingerapp.com/' } },
    'verystream.com': { baseUrl: 'https://verystream.com', headers: { 'Referer': 'https://verystream.com/' } },
    'onlystream.tv': { baseUrl: 'https://onlystream.tv', headers: { 'Referer': 'https://onlystream.tv/' } },
    'playnow.to': { baseUrl: 'https://playnow.to', headers: { 'Referer': 'https://playnow.to/' } },
    'jetload.net': { baseUrl: 'https://jetload.net', headers: { 'Referer': 'https://jetload.net/' } },
    'prostream.to': { baseUrl: 'https://prostream.to', headers: { 'Referer': 'https://prostream.to/' } },
  };
  
<span class="cstat-no" title="statement not covered" >  module.exports = {</span>
    WIFLIX_BASE: 'flemmix.net',
    WITV_BASE: 'witv.skin',
    FRENCH_ANIME_BASE: 'french-anime.com',
    PAGES_TO_SCRAPE_WIFLIX_MOVIES: 651, // Max for movies
    PAGES_TO_SCRAPE_WIFLIX_SERIES: 161, // Max for series
    SCRAPE_INTERVAL: 6 * 60 * 60 * 1000, // 6 hours
    SCRAPING_ORDER: ['wiflix_series', 'anime', 'wiflix_movies', 'witv'],
    SCRAPE_MODE,
    PROVIDER_CONFIG
  };</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T16:31:40.786Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    