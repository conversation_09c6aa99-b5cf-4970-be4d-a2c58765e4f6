// File: tests/testResultsProcessor.js
// Custom test results processor for performance metrics

module.exports = (results) => {
  // Calculate performance metrics
  const totalTests = results.numTotalTests;
  const passedTests = results.numPassedTests;
  const failedTests = results.numFailedTests;
  const totalTime = results.testResults.reduce((acc, result) => acc + result.perfStats.end - result.perfStats.start, 0);
  const avgTestTime = totalTime / totalTests;

  // Performance thresholds
  const SLOW_TEST_THRESHOLD = 1000; // 1 second
  const VERY_SLOW_TEST_THRESHOLD = 5000; // 5 seconds

  // Find slow tests
  const slowTests = [];
  results.testResults.forEach(testResult => {
    testResult.testResults.forEach(test => {
      const duration = test.duration || 0;
      if (duration > SLOW_TEST_THRESHOLD) {
        slowTests.push({
          name: test.fullName,
          duration,
          file: testResult.testFilePath
        });
      }
    });
  });

  // Generate performance report
  const performanceReport = {
    summary: {
      totalTests,
      passedTests,
      failedTests,
      totalTime: Math.round(totalTime),
      avgTestTime: Math.round(avgTestTime),
      slowTestsCount: slowTests.length
    },
    slowTests: slowTests.sort((a, b) => b.duration - a.duration).slice(0, 10),
    thresholds: {
      slow: SLOW_TEST_THRESHOLD,
      verySlow: VERY_SLOW_TEST_THRESHOLD
    }
  };

  // Log performance summary
  console.log('\n📊 Performance Summary:');
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Passed: ${passedTests}`);
  console.log(`   Failed: ${failedTests}`);
  console.log(`   Total Time: ${Math.round(totalTime)}ms`);
  console.log(`   Average Test Time: ${Math.round(avgTestTime)}ms`);
  
  if (slowTests.length > 0) {
    console.log(`\n⚠️  Slow Tests (>${SLOW_TEST_THRESHOLD}ms):`);
    slowTests.slice(0, 5).forEach(test => {
      console.log(`   ${test.name}: ${test.duration}ms`);
    });
  }

  // Save performance report
  const fs = require('fs');
  const path = require('path');
  
  try {
    const reportPath = path.join(process.cwd(), 'test-performance-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(performanceReport, null, 2));
    console.log(`\n📄 Performance report saved to: ${reportPath}`);
  } catch (error) {
    console.warn('Failed to save performance report:', error.message);
  }

  return results;
};
