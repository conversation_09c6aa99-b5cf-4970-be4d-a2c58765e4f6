import HeroSection from '@/components/home/<USER>'
import GenreCarousels from '@/components/media/GenreCarousels'
import WishlistCarousels from '@/components/features/WishlistCarousels'
import TrendingCarousel from '@/components/media/TrendingCarousel'
import LatestCarousel from '@/components/media/LatestCarousel'

export default function MoviesPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <HeroSection type="movies" />
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-white mb-8">Movies</h1>

        {/* Trending Movies Carousel */}
        <TrendingCarousel type="movies" />

        {/* Latest Movies Carousel */}
        <LatestCarousel type="movies" />

        {/* Wishlist Carousel */}
        <WishlistCarousels filterType="movies" />

        {/* Genre-based carousels */}
        <GenreCarousels type="movies" />
      </div>
    </div>
  )
}
