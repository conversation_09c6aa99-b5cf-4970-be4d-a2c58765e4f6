/**
 * <PERSON><PERSON><PERSON> to add TMDB seasons data to a series
 * Usage: node scripts/add_tmdb_seasons.js --id=<series_id>
 */

require('dotenv').config();
const mongoose = require('mongoose');
const axios = require('axios');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('id', {
    description: 'Series ID to update',
    type: 'string',
    demandOption: true
  })
  .option('type', {
    description: 'Item type (series or anime)',
    type: 'string',
    default: 'series',
    choices: ['series', 'anime']
  })
  .help()
  .alias('help', 'h')
  .argv;

// Connect to MongoDB
const connectDB = async () => {
  const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/NetStream';
  console.log('Connecting to MongoDB with URI:', mongoURI);
  await mongoose.connect(mongoURI);
  console.log('Connected to MongoDB');
};

// Fetch TMDB seasons data for a TV series
const fetchTmdbSeasons = async (tmdbId) => {
  try {
    const tmdbApiKey = process.env.TMDB_API_KEY;
    if (!tmdbApiKey) {
      throw new Error('TMDB API key not found in environment variables');
    }

    console.log(`Fetching TMDB seasons data for TV series with ID: ${tmdbId}`);
    const response = await axios.get(
      `https://api.themoviedb.org/3/tv/${tmdbId}?api_key=${tmdbApiKey}&language=en-US&append_to_response=seasons`
    );

    if (!response.data || !response.data.seasons) {
      throw new Error('No seasons data found in TMDB response');
    }

    console.log(`Found ${response.data.seasons.length} seasons for TMDB ID: ${tmdbId}`);

    // Process seasons data
    const seasons = response.data.seasons.map(season => ({
      air_date: season.air_date,
      tmdb_season_id: season.id,
      name: season.name,
      overview: season.overview,
      poster_path: season.poster_path,
      season_number: season.season_number,
      vote_average: season.vote_average,
      episodes: [] // We'll fetch episodes for each season separately
    }));

    // Fetch episodes for each season
    for (const season of seasons) {
      console.log(`Fetching episodes for season ${season.season_number}`);
      const seasonResponse = await axios.get(
        `https://api.themoviedb.org/3/tv/${tmdbId}/season/${season.season_number}?api_key=${tmdbApiKey}&language=en-US`
      );

      if (seasonResponse.data && seasonResponse.data.episodes) {
        season.episodes = seasonResponse.data.episodes.map(episode => ({
          air_date: episode.air_date,
          episode_number: episode.episode_number,
          tmdb_episode_id: episode.id,
          name: episode.name,
          overview: episode.overview,
          still_path: episode.still_path,
          vote_average: episode.vote_average
        }));
        console.log(`Added ${season.episodes.length} episodes to season ${season.season_number}`);
      }
    }

    return seasons;
  } catch (error) {
    console.error('Error fetching TMDB seasons data:', error.message);
    return null;
  }
};

// Update series with TMDB seasons data
const updateSeriesWithTmdbSeasons = async () => {
  try {
    await connectDB();

    const { id, type } = argv;
    console.log(`Updating ${type} with ID: ${id}`);

    // Get the model based on the type
    const Model = type === 'anime' ? Anime : Series;

    // Find the item
    const item = await Model.findById(id);
    if (!item) {
      throw new Error(`${type} with ID ${id} not found`);
    }

    console.log(`Found ${type}: ${item.title}`);

    // Check if the item has TMDB data
    if (!item.tmdb || !item.tmdb.id) {
      throw new Error(`${type} does not have TMDB data`);
    }

    const tmdbId = item.tmdb.id;
    console.log(`TMDB ID: ${tmdbId}`);

    // Fetch TMDB seasons data
    const seasons = await fetchTmdbSeasons(tmdbId);
    if (!seasons) {
      throw new Error('Failed to fetch TMDB seasons data');
    }

    // Update the item with TMDB seasons data
    item.tmdbSeasons = seasons;
    await item.save();

    console.log(`Successfully updated ${type} with TMDB seasons data`);
  } catch (error) {
    console.error('Error updating series with TMDB seasons data:', error);
  } finally {
    // Close the database connection
    await mongoose.connection.close();
    console.log('Disconnected from MongoDB');
  }
};

// Run the function
updateSeriesWithTmdbSeasons();
