import HeroSection from '@/components/home/<USER>'
import GenreCarousels from '@/components/media/GenreCarousels'
import WishlistCarousels from '@/components/features/WishlistCarousels'
import TrendingCarousel from '@/components/media/TrendingCarousel'
import LatestCarousel from '@/components/media/LatestCarousel'

export default function AnimePage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <HeroSection type="anime" />
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-white mb-8">Anime</h1>

        {/* Trending Anime Carousel */}
        <TrendingCarousel type="anime" />

        {/* Latest Anime Carousel */}
        <LatestCarousel type="anime" />

        {/* Wishlist Carousel */}
        <WishlistCarousels filterType="anime" />

        {/* Genre-based carousels */}
        <GenreCarousels type="anime" />
      </div>
    </div>
  )
}
