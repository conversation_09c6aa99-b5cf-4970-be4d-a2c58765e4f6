# Backend files (exclude everything except frontend)
src/
server-*.js
*.js
!netstream-nextjs/**/*.js
!netstream-nextjs/**/*.jsx
!netstream-nextjs/**/*.mjs

# Backend directories
api/
cache/
config/
db/
enrichment/
graphql/
jobs/
middleware/
resolvers/
routes/
schema/
scrapers/
scripts/
utils/
workers/

# Backend files
package.json
package-lock.json
!netstream-nextjs/package.json
!netstream-nextjs/package-lock.json

# Documentation
*.md
!netstream-nextjs/*.md

# Git
.git
.gitignore

# Docker files (except the one we're using)
Dockerfile
Dockerfile.redis
!Dockerfile.frontend

# Coverage and tests
coverage/
tests/
*.test.js
*.spec.js

# Environment files
.env
.env.local
.env.production

# Logs
*.log

# OS files
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/

# Temporary files
temp/
tmp/ 