// src/utils/sourceStreamFetcher.js
const axios = require('axios');
const fs = require('fs');
const https = require('https');

// Import fetch methods from individual modules
const fetchVidplyStream = require('../../scripts/sourceStreamUrlLogic/vidplyFetchStream');
const fetchUqloadStream = require('../../scripts/sourceStreamUrlLogic/uqloadFetchStream');
const fetchVoeStream = require('../../scripts/sourceStreamUrlLogic/voeFetchStream');
const fetchGenericStream = require('../../scripts/sourceStreamUrlLogic/genericFetchStream');
const fetchStreamtapeStream = require('../../scripts/sourceStreamUrlLogic/streamtapeFetchStream');

// **DEBUG MODE CONFIGURATION**
const DEBUG_MODE = false; // Set to true to enable debug file creation, false to disable

// Logging utilities (adapted to use server logger)
const logger = require('../utils/logger');

// PROVIDER_CONFIG (full list from your script)
const PROVIDER_CONFIG = {
    'magasavor.net': { baseUrl: 'https://magasavor.net', headers: { 'Referer': 'https://magasavor.net/', 'User-Agent': 'Mozilla/5.0' }, altDomains: ['magasavor', 'magasavor.nete', 'alejandrocenturyoil.com'] },
    'vidply.com': { baseUrl: 'https://vidply.com', headers: { 'Referer': 'https://vidply.com/' }, altDomains: ['vidply', 'vidply.come'] },
    'vidmoly.to': { baseUrl: 'https://vidmoly.to', headers: { 'Referer': 'https://vidmoly.to/' }, altDomains: ['vidmoly.me', 'vidmoly'] },
    'luluvdo.com': { baseUrl: 'https://luluvdo.com', headers: { 'Referer': 'https://luluvdo.com/' }, altDomains: ['luluvdo'] },
    'luluvdoo.com': { baseUrl: 'https://luluvdoo.com', headers: { 'Referer': 'https://luluvdoo.com/' }, altDomains: ['luluvdoo'] },
    'tipfly.xyz': { baseUrl: 'https://tipfly.xyz', headers: { 'Referer': 'https://tipfly.xyz/' } },
    'vide0.net': { baseUrl: 'https://vide0.net', headers: { 'Referer': 'https://vide0.net/' }, altDomains: ['vide0'] },
    'ups2up.fun': { baseUrl: 'https://ups2up.fun', headers: { 'Referer': 'https://ups2up.fun/' }, altDomains: ['ups2up'] },
    'jilliandescribecompany.com': { baseUrl: 'https://jilliandescribecompany.com', headers: { 'Referer': 'https://jilliandescribecompany.com/' } },
    'uqload.net': { baseUrl: 'https://uqload.net', headers: { 'Referer': 'https://uqload.net/', 'Origin': 'https://uqload.net', 'X-Requested-With': 'XMLHttpRequest' }, altDomains: ['uqload.to', 'uqloada.ws', 'uqload.'] },
    'oneupload.to': { baseUrl: 'https://oneupload.to', headers: { 'Referer': 'https://oneupload.to/' }, altDomains: ['oneupload.net'] },
    'waaw1.tv': { baseUrl: 'https://waaw1.tv', headers: { 'Referer': 'https://waaw1.tv/' }, altDomains: ['waaw.to', 'waaw.tv', 'waaw1.tve'] },
    'filegram.to': { baseUrl: 'https://filegram.to', headers: { 'Referer': 'https://filegram.to/' } },
    'dooodster.com': { baseUrl: 'https://dooodster.com', headers: { 'Referer': 'https://dooodster.com/' }, altDomains: ['dooood.com', 'dood.re', 'dood.wf', 'dood.pro', 'dood.sh'] },
    'voe.sx': { baseUrl: 'https://voe.sx', headers: { 'Referer': 'https://voe.sx/' } },
    'cybervynx.com': { baseUrl: 'https://cybervynx.com', headers: { 'Referer': 'https://cybervynx.com/' } },
    'sbface.com': { baseUrl: 'https://sbface.com', headers: { 'Referer': 'https://sbface.com/' }, altDomains: ['sbanh.com', 'sbchill.com', 'sbrity.com', 'sbbrisk.com', 'sblanh.com', 'sbhight.com', 'sbspeed.com'] },
    'lvturbo.com': { baseUrl: 'https://lvturbo.com', headers: { 'Referer': 'https://lvturbo.com/' } },
    'streamsilk.com': { baseUrl: 'https://streamsilk.com', headers: { 'Referer': 'https://streamsilk.com/' } },
    'd0000d.com': { baseUrl: 'https://d0000d.com', headers: { 'Referer': 'https://d0000d.com/' }, altDomains: ['d000d.com', 'd0o0d.com'] },
    'streamdav.com': { baseUrl: 'https://streamdav.com', headers: { 'Referer': 'https://streamdav.com/' } },
    'streamvid.net': { baseUrl: 'https://streamvid.net', headers: { 'Referer': 'https://streamvid.net/' } },
    'mixdrop.ps': { baseUrl: 'https://mixdrop.ps', headers: { 'Referer': 'https://mixdrop.ps/' }, altDomains: ['mixdrop.co'] },
    'vido.lol': { baseUrl: 'https://vido.lol', headers: { 'Referer': 'https://vido.lol/' }, altDomains: ['vido.lo', 'vido.lole'] },
    'upstream.to': { baseUrl: 'https://upstream.to', headers: { 'Referer': 'https://upstream.to/' }, altDomains: ['upstream.co'] },
    'upvideo.to': { baseUrl: 'https://upvideo.to', headers: { 'Referer': 'https://upvideo.to/' } },
    'ssblongvu.com': { baseUrl: 'https://ssblongvu.com', headers: { 'Referer': 'https://ssblongvu.com/' } },
    'streamhide.to': { baseUrl: 'https://streamhide.to', headers: { 'Referer': 'https://streamhide.to/' } },
    'louishide.com': { baseUrl: 'https://louishide.com', headers: { 'Referer': 'https://louishide.com/' } },
    'vudeo.ws': { baseUrl: 'https://vudeo.ws', headers: { 'Referer': 'https://vudeo.ws/' }, altDomains: ['vudeo.nlt'] },
    'guccihide.com': { baseUrl: 'https://guccihide.com', headers: { 'Referer': 'https://guccihide.com/' } },
    'evoload.io': { baseUrl: 'https://evoload.io', headers: { 'Referer': 'https://evoload.io/' }, altDomains: ['evoload.ioev', 'evoload.net'] },
    'mvidoo.com': { baseUrl: 'https://mvidoo.com', headers: { 'Referer': 'https://mvidoo.com/' } },
    'ds2play.com': { baseUrl: 'https://ds2play.com', headers: { 'Referer': 'https://ds2play.com/' } },
    'vidhidevip.com': { baseUrl: 'https://vidhidevip.com', headers: { 'Referer': 'https://vidhidevip.com/' } },
    'streamtape.com': { baseUrl: 'https://streamtape.com', headers: { 'Referer': 'https://streamtape.com/' } },
    'streamhub.gg': { baseUrl: 'https://streamhub.gg', headers: { 'Referer': 'https://streamhub.gg/' }, altDomains: ['streamhub.top'] },
    'filemoon.sx': { baseUrl: 'https://filemoon.sx', headers: { 'Referer': 'https://filemoon.sx/' } },
    'sdefx.cloud': { baseUrl: 'https://sdefx.cloud', headers: { 'Referer': 'https://sdefx.cloud/' } },
    'wiflix.online': { baseUrl: 'https://wiflix.online', headers: { 'Referer': 'https://wiflix.online/' } },
    'vidfast.co': { baseUrl: 'https://vidfast.co', headers: { 'Referer': 'https://vidfast.co/' }, altDomains: ['go.vidfast.co'] },
    'abcvideo.cc': { baseUrl: 'https://abcvideo.cc', headers: { 'Referer': 'https://abcvideo.cc/' } },
    'aparat.cam': { baseUrl: 'https://aparat.cam', headers: { 'Referer': 'https://aparat.cam/' } },
    'players.wiflix-pro.mom': { baseUrl: 'https://players.wiflix-pro.mom', headers: { 'Referer': 'https://players.wiflix-pro.mom/' } },
    'upvid.co': { baseUrl: 'https://upvid.co', headers: { 'Referer': 'https://upvid.co/' } },
    'hlsplay.com': { baseUrl: 'https://hlsplay.com', headers: { 'Referer': 'https://hlsplay.com/' } },
    'tazvids.to': { baseUrl: 'https://tazvids.to', headers: { 'Referer': 'https://tazvids.to/' } },
    'video.sibnet.ru': { baseUrl: 'https://video.sibnet.ru', headers: { 'Referer': 'https://video.sibnet.ru/' } },
    'www.myvi.xyz': { baseUrl: 'https://www.myvi.xyz', headers: { 'Referer': 'https://www.myvi.xyz/' } },
    'userload.co': { baseUrl: 'https://userload.co', headers: { 'Referer': 'https://userload.co/' } },
    'embed.mystream.to': { baseUrl: 'https://embed.mystream.to', headers: { 'Referer': 'https://embed.mystream.to/' } },
    'www.fembed.com': { baseUrl: 'https://www.fembed.com', headers: { 'Referer': 'https://www.fembed.com/' } },
    'hirudinoid-prisoner.hostingerapp.com': { baseUrl: 'https://hirudinoid-prisoner.hostingerapp.com', headers: { 'Referer': 'https://hirudinoid-prisoner.hostingerapp.com/' } },
    'verystream.com': { baseUrl: 'https://verystream.com', headers: { 'Referer': 'https://verystream.com/' } },
    'onlystream.tv': { baseUrl: 'https://onlystream.tv', headers: { 'Referer': 'https://onlystream.tv/' } },
    'playnow.to': { baseUrl: 'https://playnow.to', headers: { 'Referer': 'https://playnow.to/' } },
    'jetload.net': { baseUrl: 'https://jetload.net', headers: { 'Referer': 'https://jetload.net/' } },
    'prostream.to': { baseUrl: 'https://prostream.to', headers: { 'Referer': 'https://prostream.to/' } },
};

// Custom axios instance to bypass SSL errors
const axiosWithSSLBypass = axios.create({
    httpsAgent: new https.Agent({ rejectUnauthorized: false }),
    timeout: 15000,
    maxRedirects: 10,
});

async function verifyDirectUrl(directUrl, referer, cookieHeader = '', extraHeaders = {}, retries = 2) {
    for (let attempt = 0; attempt <= retries; attempt++) {
        try {
            if (typeof directUrl !== 'string' || !directUrl.startsWith('http')) {
                throw new Error('Invalid URL format');
            }
            logger.info(`[Verification] Testing URL: ${directUrl}`, { attempt: attempt + 1, retries });
            const headResponse = await axiosWithSSLBypass.head(directUrl, {
                headers: { 'User-Agent': 'Mozilla/5.0', 'Referer': referer, 'Cookie': cookieHeader, ...extraHeaders },
            });
            const contentType = headResponse.headers['content-type'] || '';
            const isValid = headResponse.status === 200 && (
                contentType.includes('video') ||
                contentType.includes('application/x-mpegURL') ||
                contentType.includes('application/vnd.apple.mpegurl') ||
                directUrl.match(/\.(mp4|m3u8)$/)
            );

            logger.info(`[Verification] HEAD Response: ${directUrl}`, { status: headResponse.status, contentType });

            if (!isValid) {
                logger.info(`[Verification] URL considered invalid: ${directUrl}`, { status: headResponse.status, contentType, isValid });
                throw new Error(`Invalid content type: ${contentType}`);
            }

            logger.info(`Verified video URL`, {
                url: directUrl,
                status: headResponse.status,
                contentType,
                size: headResponse.headers['content-length'] || 'Unknown'
            });
            return {
                url: directUrl,
                size: headResponse.headers['content-length'] || 'Unknown',
                type: contentType.includes('x-mpegURL') || contentType.includes('mpegurl') || directUrl.includes('m3u8') ? 'HLS' : 'MP4'
            };
        } catch (error) {
            logger.error(`Verification failed for ${directUrl} (Attempt ${attempt + 1}/${retries})`, {
                message: error.message,
                code: error.code,
                status: error.response?.status,
                statusText: error.response?.statusText,
            });
            if (attempt < retries && (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT')) {
                logger.info(`Retrying ${directUrl} (${attempt + 1}/${retries})`);
                await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
                continue;
            }
            return null;
        }
    }
    return null;
}

async function getDirectVideoLink(inputUrl, config, cookies) {
    const fetchMethods = [
        { name: 'PassMd5', fn: fetchVidplyStream },
        { name: 'VOE', fn: fetchVoeStream },
        { name: 'Generic', fn: fetchGenericStream },
        { name: 'Uqload', fn: fetchUqloadStream },
        { name: 'Streamtape', fn: fetchStreamtapeStream }
    ];

    for (const method of fetchMethods) {
        try {
            const directUrl = await method.fn(inputUrl, config, cookies, DEBUG_MODE, logger.info, logger.error);
            if (directUrl) {
                const verified = await verifyDirectUrl(directUrl, inputUrl, cookies.cookieHeader, config.headers);
                if (verified) {
                    logger.info(`Success with ${method.name}`, { url: inputUrl, directUrl: verified.url });
                    return { ...verified, method: method.name };
                }
            }
        } catch (error) {
            logger.error(`Method ${method.name} failed for ${inputUrl}`, {
                message: error.message,
                code: error.code,
                status: error.response?.status,
                statusText: error.response?.statusText,
            });
        }
    }
    return null;
}

async function fetchSourceStreamUrl(streamingUrl, provider) {
    logger.info(`[fetchSourceStreamUrl] Starting fetch for URL: ${streamingUrl}, Provider: ${provider}`);

    // Map provider names to domain names
    const providerDomainMap = {
        'Vide0': 'vide0.net',
        'Luluvdo': 'luluvdoo.com',
        'Tipfly': 'tipfly.xyz',
        'Ups2up': 'ups2up.fun',
        'Jilliandescribecompany': 'jilliandescribecompany.com'
    };

    // Extract domain from URL
    const urlDomain = streamingUrl.split('/')[2];
    logger.info(`[fetchSourceStreamUrl] URL domain: ${urlDomain}`);

    // Try to get config by provider name mapping, then by URL domain, then fallback
    const mappedDomain = providerDomainMap[provider] || urlDomain;
    logger.info(`[fetchSourceStreamUrl] Mapped domain: ${mappedDomain}`);

    const config = PROVIDER_CONFIG[mappedDomain] || PROVIDER_CONFIG[urlDomain] || {
        baseUrl: `https://${urlDomain}`,
        headers: { 'Referer': `https://${urlDomain}/` }
    };
    config.provider = provider;

    logger.info(`[fetchSourceStreamUrl] Using config:`, {
        baseUrl: config.baseUrl,
        headers: config.headers,
        provider: config.provider
    });

    try {
        logger.info(`[fetchSourceStreamUrl] Fetching embed page: ${streamingUrl}`);
        const eResponse = await axiosWithSSLBypass.get(streamingUrl, {
            headers: { 'User-Agent': 'Mozilla/5.0', ...config.headers },
        });

        logger.info(`[fetchSourceStreamUrl] Embed page fetched successfully`, {
            status: eResponse.status,
            contentLength: eResponse.headers['content-length'],
            contentType: eResponse.headers['content-type']
        });

        const cookies = {
            eResponse,
            cookieHeader: (eResponse.headers['set-cookie'] || []).map(c => c.split(';')[0]).join('; ') || 'None'
        };

        logger.info(`[fetchSourceStreamUrl] Calling getDirectVideoLink...`);
        const result = await getDirectVideoLink(streamingUrl, config, cookies);

        logger.info(`[fetchSourceStreamUrl] getDirectVideoLink result:`, {
            result: result,
            hasResult: !!result,
            url: result?.url,
            method: result?.method
        });

        return result;
    } catch (error) {
        logger.error(`[fetchSourceStreamUrl] Failed to fetch embed page for ${streamingUrl}`, {
            message: error.message,
            code: error.code,
            status: error.response?.status,
            statusText: error.response?.statusText,
            stack: error.stack,
            provider: provider,
            mappedDomain: mappedDomain,
            urlDomain: urlDomain
        });
        return null;
    }
}

module.exports = { fetchSourceStreamUrl };