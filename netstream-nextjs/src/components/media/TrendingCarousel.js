'use client'
import { useQuery } from '@apollo/client'
import { GET_MOVIES, GET_SERIES, GET_ANIME } from '@/lib/queries'
import Carousel from '@/components/ui/Carousel'

const QUERY_MAP = {
  movies: GET_MOVIES,
  series: GET_SERIES,
  anime: GET_ANIME
}

export default function TrendingCarousel({ type }) {
  const query = QUERY_MAP[type]
  
  const { data, loading, error } = useQuery(query, {
    variables: {
      page: 1,
      limit: 20,
      sort: 'TRENDING'
    },
    errorPolicy: 'all'
  })

  if (loading) {
    return (
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-white flex items-center">
            <span className="w-1 h-8 bg-red-500 mr-4 rounded"></span>
            Trending {type.charAt(0).toUpperCase() + type.slice(1)}
          </h2>
        </div>
        <div className="flex justify-center py-8">
          <i className="fas fa-spinner fa-spin text-xl text-blue-500"></i>
        </div>
      </div>
    )
  }

  if (error) {
    console.error(`Error loading trending ${type}:`, error)
    return null
  }

  const items = data?.[type] || []

  if (!items.length) {
    return null
  }

  return (
    <div className="mb-12">
      <Carousel
        title={`Trending ${type.charAt(0).toUpperCase() + type.slice(1)}`}
        items={items}
        type={type}
      />
    </div>
  )
}
