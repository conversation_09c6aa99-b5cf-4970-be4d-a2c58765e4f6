'use client'
import { useState } from 'react'
import { EditItemModal } from './EditItemModal'
import { getThumbnailUrl, getTitleWithState, formatRating, getYear, getLanguageEpisodeInfo } from '@/utils/helpers'

export function AdminContentCard({ item, onDelete, onUpdate }) {
  const [imageError, setImageError] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  
  const type = item.__typename || 'Movie'
  const imageUrl = getThumbnailUrl(item)
  const title = getTitleWithState(item, type)
  const rating = formatRating(item)
  const year = getYear(item)
  const languageEpisodeInfo = getLanguageEpisodeInfo(item, type)
  
  // Robust TMDB/Jikan extraction with null checks
  const tmdb = item.tmdb || null
  const jikan = item.jikan || null
  const tmdbId = tmdb && (tmdb.id !== undefined && tmdb.id !== null) ? tmdb.id : null
  const tmdbTitle = tmdb && tmdb.title ? tmdb.title : null
  const jikanId = jikan && (jikan.mal_id !== undefined && jikan.mal_id !== null) ? jikan.mal_id : null
  const jikanTitle = jikan && jikan.title ? (jikan.title.english || jikan.title.default || jikan.title.japanese) : null

  return (
    <>
      <div className="flex items-center bg-gray-800 rounded-lg shadow mb-2 p-2">
        <img
          src={imageError ? '/default-thumbnail.jpg' : imageUrl}
          alt={title}
          className="rounded mr-3 flex-shrink-0"
          style={{ width: 48, height: 72, objectFit: 'cover' }}
          onError={() => setImageError(true)}
        />
        <div className="flex-1 min-w-0">
          <div className="text-xs text-gray-400">ID: {item.id}</div>
          {item.season && (
            <div className="text-xs text-gray-400">Season: {item.season}</div>
          )}
          <div className="text-sm text-white font-bold truncate">{item.title}</div>
          <div className="flex flex-row flex-wrap gap-4 mt-1">
            {(tmdbId || tmdbTitle) && (
              <span className="text-xs text-blue-300 whitespace-nowrap">
                TMDB:
                {tmdbId && <span className="font-mono ml-1">{tmdbId}</span>}
                {tmdbTitle && <span className="ml-1">- {tmdbTitle}</span>}
              </span>
            )}
            {(jikanId || jikanTitle) && (
              <span className="text-xs text-purple-300 whitespace-nowrap">
                Jikan:
                {jikanId && <span className="font-mono ml-1">{jikanId}</span>}
                {jikanTitle && <span className="ml-1">- {jikanTitle}</span>}
              </span>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-2 ml-4">
          <button 
            className="bg-yellow-500 hover:bg-yellow-600 text-white px-2 py-1 rounded text-xs mb-1"
            onClick={() => setIsEditing(true)}
          >
            <i className="fas fa-edit"></i>
          </button>
          <button 
            className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs action-btn delete-btn" 
            onClick={() => onDelete(item.id, item.__typename)}
          >
            <i className="fas fa-trash"></i>
          </button>
        </div>
      </div>
      
      {isEditing && (
        <EditItemModal
          isOpen={true}
          onClose={() => setIsEditing(false)}
          onSave={onUpdate}
          itemId={item.id}
          itemType={item.__typename}
        />
      )}
    </>
  )
}
