# Docker and git files
Dockerfile
.dockerignore
.git
.gitignore

# Dependencies
node_modules

# Next.js build output
.next

# Development logs
npm-debug.log

# Documentation
README.md

# Misc
.DS_Store
*.tgz
*.tar.gz

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode
.idea

# Tests
__tests__
*.test.js
*.test.jsx
*.spec.js
*.spec.jsx

# Coverage
coverage 