const fs = require('fs');

/**
 * Fetches direct video URL from Streamtape by extracting a token from the embed page and constructing a get_video URL.
 */
async function fetchStreamtapeStream(inputUrl, config, cookies, debug = false) { // Added debug parameter
    const html = cookies.eResponse.data;
    const tokenMatch = html.match(/get_video\?id=[^&]+&expires=[^&]+&ip=[^&]+&token=([^'"]+)/);
    if (!tokenMatch) {
        if (debug) { // Conditionally write debug file
            const debugFile = `streamtape_debug_${Date.now()}.html`;
            fs.writeFileSync(debugFile, html);
            console.log(`[Streamtape] Debug HTML saved to: ${debugFile}`); // Informative log
        }
        throw new Error(`No token found in embed page${debug ? ` - check ${debugFile}` : ''}`); // Conditional debug file message
    }
    const videoId = inputUrl.split('/e/')[1];
    const directUrl = `${config.baseUrl}/get_video?id=${videoId}&token=${tokenMatch[1]}`;
    console.log(`[Streamtape] Constructed URL: ${directUrl}`);
    return directUrl;
}

module.exports = fetchStreamtapeStream;